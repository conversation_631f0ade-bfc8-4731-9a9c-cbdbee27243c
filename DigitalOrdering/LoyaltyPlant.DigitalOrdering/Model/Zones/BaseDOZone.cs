using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.DigitalOrdering.Model.Zones
{
    [Serializable]
    public abstract class BaseDOZone
    {
        public int Id { get; set; }
        public decimal MinimalOrderSum { get; set; }
        public bool EnableOrderWhileNotWorking { get; set; }
        public decimal Tax { get; set; }

        public List<decimal> Tips { get; set; } = new List<decimal>();
        public decimal DefaultTip { get; set; }
        public int ProcessId { get; set; }
        public int TimePickerInterval { get; set; } = DOProcess.DefaultTimePickerInterval;

        public List<PaymentMethodType> PaymentTypes { get; set; } = new List<PaymentMethodType>();
        public List<WorkingDay> WorkingDays { get; set; } = new List<WorkingDay>();

        // 0 - только ASAP, 1 - заказ на сегодня, {1, 2, 3 ..} на N дней вперед
        public int MaxCheckoutPeriod { get; set; } = DOProcess.DefaultPickUpEatInDaysCount;
        // 0 - без ограничений, 1 - с завтра, N - c N-го дня
        public int MinCheckoutPeriod { get; set; } = 0;

        public bool IsOrderOnlyToday => MaxCheckoutPeriod == 0 || MaxCheckoutPeriod == 1;

        public bool ShowDriveThruMessage { get; set; }

        // если тип заказа Pickup || EatIn, то defaultExecutionTime = cookingTime
        // если тип заказа Delivery || Catering, то defaultExecutionTime = cookingTime + deliveryTime
        public int DefaultExecutionTime;

        public int OnlineOrderDeliveryTime;
        public int OnlineOrderCookingTime;

        public int OnlineOrderStartTimeOffset;
        public int OnlineOrderEndTimeOffset;

        private Outlet _mainOutlet;

        public DateTime OutletDateTimeNow
        {
            get
            {
                if (_mainOutlet != null)
                    return _mainOutlet.OutletDateTimeNow;

                return LpDateTime.PartnerNow;
            }
        }

        public BaseDOZone(int id)
        {
            Id = id;
        }

        public WorkingDay GetToday()
        {
            return WorkingDays.FirstOrDefault((arg) => arg.Day == OutletDateTimeNow.DayOfWeek);
        }

        public string GetTimeToFormatted()
        {
            var today = GetToday();
            if (today == null)
            {
                return "";
            }

            TimeSpan timeTo;
            if (IsOrderOnlyToday)
            {
                var todayDateTime = OutletDateTimeNow.Date;
                var timeToWithoutExecutionTime = today.GetTo().Subtract(TimeSpan.FromMinutes(today.EndOffset));
                var executionTime = today.GetExecutionTimeMinutes(todayDateTime.Add(timeToWithoutExecutionTime));

                timeTo = timeToWithoutExecutionTime.Subtract(TimeSpan.FromMinutes(executionTime));
            }
            else
            {
                timeTo = today.GetTo();
            }
            return LpDateTime.TimeToStringCurrentFormat(timeTo);
        }

        public string GetNearestWorkingDayDateFromFormatted()
        {
            var asap = GetAsap();
            return DateToFormattedString(asap);
        }

        public string GetNearestWorkingDayTimeFromFormatted()
        {
            var asap = GetAsap();
            var workingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == asap.DayOfWeek);
            if (workingDay == null)
            {
                LpLogger.LOG_W("WorkingDays in GetNearestWorkingDayTimeFromFormatted is null");
                return string.Empty;
            }

            // APP-3962 тут с false костыльное решение, но так как это используется тока для сообщений, то в принципе некритично
            return LpDateTime.TimeToStringCurrentFormat(workingDay.GetFromTimeSpan(asap.TimeOfDay, false));
        }

        // TODO: переименовать, а то у нас 3 GetAsap и легко запутаться
        public DateTime GetAsap(bool isNeedToGetServerTime = false)
        {
            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;

            if (currentOrder != null && currentOrder.IsNeedToGetAsapTimeFromServer() && !isNeedToGetServerTime)
            {
                if (currentOrder.AsapTimeFromServer.Year != 1)
                    return currentOrder.AsapTimeFromServer;
            }

            var minAvailableDay = OutletDateTimeNow.AddDays(MinCheckoutPeriod);
            if (!(WorkingDays?.Any() ?? false))
                LpLogger.LOG_W("BaseDOZone. Нет WorkingDays! Кажется, некорректная настройка точек!");
            var firstAvailableDay = WorkingDays.FirstOrDefault((arg) => arg.Day == minAvailableDay.DayOfWeek);
            if (firstAvailableDay != null)
            {
                var currentAsap = firstAvailableDay.GetAsapForFirstAvailableWorkingDay(minAvailableDay, MinCheckoutPeriod);
                if (currentAsap.HasValue)
                    return currentAsap.Value;
            }

            var nextWorkingDateTime = minAvailableDay;

            for (int i = 0; i < 8; i++)
            {
                nextWorkingDateTime = GetNextWorkingDay(nextWorkingDateTime);
                var nextWorkingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == nextWorkingDateTime.DayOfWeek);

                var currentAsap = nextWorkingDay.GetAsap(nextWorkingDateTime.Date);
                if (currentAsap.HasValue)
                    return currentAsap.Value;

                firstAvailableDay = nextWorkingDay;
            }

            return default(DateTime);
        }

        public string DateToFormattedString(DateTime date)
        {
            if (OutletDateTimeNow.Date == date.Date)
                return I18N.TODAY;
            else if (OutletDateTimeNow.Date.AddDays(1) == date.Date)
                return I18N.TOMORROW;

            return date.ToString("m", Engine.Instance.Platform.CurrentCulture);
        }

        private DateTime GetNextWorkingDay(DateTime prevWorkingDay)
        {
            var next = prevWorkingDay.AddDays(1);
            var start = WorkingDays.FirstOrDefault((arg) => arg.Day == next.DayOfWeek);

            while (start == null)
            {
                next = next.AddDays(1);
                start = WorkingDays.FirstOrDefault((arg) => arg.Day == next.DayOfWeek);
            }

            return next;
        }

        public bool IsOrderingAvailable()
        {
            return IsOrderingAvailable(out _);
        }

        public bool IsDateAvailable(DateTime date)
        {
            return WorkingDays.Any(arg => arg.Day == date.DayOfWeek);
        }

        public bool IsTimeAvailable(DateTime date, TimeSpan time)
        {
            try
            {
                time = new TimeSpan(0, time.Hours, time.Minutes, 0, 0);
                var finalDate = date.Date.Add(time);
                var asap = DigitalOrderingModule.Instance.CurrentOrder.AsapTime;
                var asapTime = new TimeSpan(asap.Hour, asap.Minute, 0);
                asap = asap.Date.Add(asapTime);
                if (finalDate < asap)
                    return false;

                var currentWorkingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == finalDate.DayOfWeek);
                return currentWorkingDay.GetIntervalForDateTime(finalDate) != null;
            }
            catch (Exception e)
            {
                LpLogger.LOG_W("IsTimeAvailable exception", e);
                return false;
            }
        }

        public TimeSpan GetMin(DateTime date)
        {
            var currentWorkingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == date.DayOfWeek);

            if (currentWorkingDay == null || currentWorkingDay.Day == OutletDateTimeNow.DayOfWeek)
                return DigitalOrderingModule.Instance.CurrentOrder.AsapTime.TimeOfDay;

            if (currentWorkingDay.IsWasTransitThroughMidnight && currentWorkingDay.GetFrom() == new TimeSpan(0, 0, 0))
                return currentWorkingDay.GetFrom();

            var from = currentWorkingDay.GetFrom().Add(TimeSpan.FromMinutes(currentWorkingDay.StartOffset));
            var execTime = currentWorkingDay.GetExecutionTimeMinutes(date.Date.Add(from));
            return from.Add(TimeSpan.FromMinutes(execTime));
        }

        public TimeSpan GetMax(DateTime date)
        {
            var currentWorkingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == date.DayOfWeek);

            if (currentWorkingDay.IsWillTransitThroughMidnight && currentWorkingDay.GetTo() == new TimeSpan(1, 0, 0, 0))
                return currentWorkingDay.GetTo();

            return currentWorkingDay.GetTo().Subtract(TimeSpan.FromMinutes(currentWorkingDay.EndOffset));
        }

        // min, max
        public List<TimeInterval> GetWorkingIntervals(DateTime date)
        {
            var result = new List<TimeInterval>();
            var currentWorkingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == date.DayOfWeek);

            if (currentWorkingDay == null)
                return result;

            var asapTime = DigitalOrderingModule.Instance.CurrentOrder.AsapTime.TimeOfDay;

            result = currentWorkingDay.GetWorkingDayIntervals(date, asapTime);

            return result;
        }


        public bool IsOrderingAvailable(out string unavailableReason)
        {
            if (this is DOOutlet doOutlet)
            {
                if (!doOutlet.IsAvailable)
                {
                    unavailableReason = I18N.ORDERING_NOT_AVAILABLE_THIS_AREA;
                    return false;
                }
                else
                {
                    _mainOutlet = doOutlet.Outlet;
                }
            }

            if (this is DODistrict district)
            {
                if (district.AssociatedOutlet == null)
                {
                    unavailableReason = "";
                    return false;
                }
                else
                {
                    _mainOutlet = district.AssociatedOutlet;
                }
            }

            if (WorkingDays == null || WorkingDays.Count <= 0)
            {
                //do nothing
                unavailableReason = string.Empty;
                return true;
            }

            var today = WorkingDays.FirstOrDefault((arg) => arg.Day == OutletDateTimeNow.DayOfWeek);

            // https://jira.loyaltyplant.com/browse/APP-957
            if (IsOrderOnlyToday)
            {
                if (today == null)
                {
                    unavailableReason = DigitalOrderingTexts.WeCannotProcessYourOrder(this);
                    return false;
                }

                var asapNow = today.GetAsap(OutletDateTimeNow);
                if (asapNow == null)
                {
                    unavailableReason = DigitalOrderingTexts.WeCannotProcessYourOrder(this);
                    return false;
                }
            }

            if (today == null)
            {
                if (EnableOrderWhileNotWorking)
                {
                    unavailableReason = DigitalOrderingTexts.WeWillProcessYourOrderLater(this);
                    return true;
                }

                unavailableReason = DigitalOrderingTexts.WeCannotProcessYourOrder(this);
                return false;
            }

            var interval = GetOrderingInvervals(today);
            if (interval == null)
            {
                if (EnableOrderWhileNotWorking)
                {
                    unavailableReason = DigitalOrderingTexts.WeWillProcessYourOrderLater(this);
                    return true;
                }

                unavailableReason = DigitalOrderingTexts.WeCannotProcessYourOrder(this);
                return false;
            }

            // за 15 мин до закрытия мы сообщаем об этом
            if ((interval.To - OutletDateTimeNow.TimeOfDay) < TimeSpan.FromMinutes(15))
            {
                // на всякий случай try-catch :D
                try
                {
                    var workingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == OutletDateTimeNow.DayOfWeek);
                    if (workingDay.IsWillTransitThroughMidnight &&
                        interval.To == new TimeSpan(1, 0, 0, 0))
                    {
                        unavailableReason = "";
                        return true;
                    }
                    else
                    {
                        var minutes = interval.To - OutletDateTimeNow.TimeOfDay;
                        if (minutes.TotalMinutes > 0)
                        {
                            unavailableReason = DigitalOrderingTexts.ToBeClosetAt(minutes);
                            return true;
                        }
                        else
                        {
                            if (EnableOrderWhileNotWorking)
                            {
                                unavailableReason = DigitalOrderingTexts.WeWillProcessYourOrderLater(this);
                                return true;
                            }

                            unavailableReason = DigitalOrderingTexts.WeCannotProcessYourOrder(this);
                            return false;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LpLogger.LOG_E("TimeSpan.FromMinutes(15) exception: " + ex);
                }
            }

            unavailableReason = "";
            return true;
        }

        private WorkingDayInterval GetOrderingInvervals(WorkingDay today)
        {
            // we now use only interval without StartTimeOffSet (https://jira.loyaltyplant.com/browse/APP-427)
            return today.Intervals.FirstOrDefault(obj => OutletDateTimeNow.TimeOfDay >= obj.From &&
                                                         OutletDateTimeNow.TimeOfDay <= obj.To);
        }

        public Dictionary<string, DateTime> GetDateControlValues(out KeyValuePair<string, DateTime> defaultDate)
        {
            var values = new Dictionary<string, DateTime>();
            var dateTimes = new List<DateTime>();
            WorkingDays.Sort();

            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;

            if (currentOrder.IsNeedToGetAsapTimeFromServer())
            {
                var start = currentOrder.MinimalOrderDate;

                if (WorkingDays.Any((arg) => arg.Day == start.DayOfWeek))
                {
                    dateTimes.Add(start.Date);
                }

                var daysCount = (currentOrder.MaximalOrderDate - start).Duration().Days;
                for (var i = 0; i < daysCount; i++)
                {
                    start = start.AddDays(1).Date;
                    var workingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == start.DayOfWeek);
                    if (workingDay != null)
                    {
                        dateTimes.Add(start);
                    }
                }
            }
            else
            {
                var start = GetAsap();
                dateTimes.Add(start.Date);
                while (dateTimes.Count < (MaxCheckoutPeriod - MinCheckoutPeriod))
                {
                    start = start.AddDays(1).Date;
                    var workingDay = WorkingDays.FirstOrDefault((arg) => arg.Day == start.DayOfWeek);
                    if (workingDay != null)
                    {
                        dateTimes.Add(start);
                    }
                }
            }

            foreach (var date in dateTimes)
            {
                values.Add(DateToFormattedString(date), date);
            }

            if (dateTimes.Count > 0)
            {
                defaultDate = values.ElementAt(0);
            }
            else
            {
                defaultDate = default(KeyValuePair<string, DateTime>);
            }

            return values;
        }
    }
}