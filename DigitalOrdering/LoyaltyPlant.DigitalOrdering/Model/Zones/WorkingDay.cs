using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model.ExternalVendors.Postmates;

namespace LoyaltyPlant.DigitalOrdering.Model.Zones
{
    [Serializable]
    public class WorkingDay : IComparable<WorkingDay>
    {
        // константа для того, чтобы сравнить левую границу интервала с 00:00
        public static TimeSpan ZeroFrom => new(0, 0, 0);

        // константа для того, чтобы сравнить правую границу интервала с 00:00
        // 1 означает переход на след день, для правой границы это ок
        public static TimeSpan ZeroTo => new(1, 0, 0, 0);

        // Если To == TimeSpan(1, 0, 0, 0) - то есть скорее всего будет переход на след день (через полночь)
        public bool IsWillTransitThroughMidnight { get; set; }
        // Если From == TimeSpan(0, 0, 0) - то есть скорее всего был переход на след день (через полночь)
        public bool IsWasTransitThroughMidnight { get; set; }

        public DayOfWeek Day { get; }
        public List<WorkingDayInterval> Intervals { get; set; }
        public List<PeakInterval> PeakIntervals { get; set; }

        public int DeliveryTime { get; set; }
        public int CookingTime { get; set; }

        public int StartOffset { get; set; }
        public int EndOffset { get; set; }

        // если тип заказа Pickup || EatIn, то defaultExecutionTime = cookingTime
        // если тип заказа Delivery || Catering, то defaultExecutionTime = cookingTime + deliveryTime
        public int DefaultExecutionTime { get; set; }

        public WorkingDay(string day, int startOffset, int endOffset, int defaultExecutionTime, int cookingTime, int deliveryTime = 0)
        {
            Intervals = new List<WorkingDayInterval>();
            PeakIntervals = new List<PeakInterval>();

            DeliveryTime = deliveryTime;
            CookingTime = cookingTime;

            StartOffset = startOffset;
            EndOffset = endOffset;

            DefaultExecutionTime = defaultExecutionTime;

            Day = LpDateTime.ParseServerDay(day);
        }

        /// <summary>
        /// Returns the most appropriate interval "from" with offset or first interval with start offset
        /// </summary>
        /// <returns>The most appropriate interval</returns>
        public TimeSpan GetFromTimeSpan(TimeSpan currentTimeSpan, bool isNeedStartOffset = true)
        {
            if (Intervals.Count == 0)
                return default(TimeSpan);

            // Возвращает From интервала (в который попадает currentTimeSpan) со смещением на StartOffset
            for (int i = 0; i < Intervals.Count(); i++)
            {
                if (i + 1 >= Intervals.Count())
                    break;

                // здесь не надо со StartOffset ничего делать в зависимости от isNeedStartOffset
                var nextIntervalFrom = Intervals.ElementAt(i + 1).From.Add(TimeSpan.FromMinutes(StartOffset));
                var prevIntervalTo = Intervals.ElementAt(i).To.Subtract(TimeSpan.FromMinutes(EndOffset));

                if (currentTimeSpan >= prevIntervalTo && currentTimeSpan <= nextIntervalFrom)
                    return nextIntervalFrom;
            }

            // APP-2835
            // Суть фикса: возвращать не Intervals[0], как было раньше,
            // а тот интервал, который "справа" на числовой оси от точки (currentTimeSpan)
            var customDeliveryTime = GetMaxForDeliveryOrCookingTime(currentTimeSpan, DeliveryTime, true, false);
            var customCookingTime = GetMaxForDeliveryOrCookingTime(currentTimeSpan, CookingTime, false, true);

            foreach (var item in Intervals)
            {
                var maxRightTime = Math.Max(EndOffset, customDeliveryTime) + customCookingTime;

                // TODO: если заказ ASAP, то тут должен быть не только EndOffset, но еще и cookingTime ?
                if (item.To.Subtract(TimeSpan.FromMinutes(maxRightTime)) > currentTimeSpan)
                    return item.From.Add(TimeSpan.FromMinutes(isNeedStartOffset ? StartOffset : 0));
            }

            // вернуть первый интервал в сутках, прибавив StartOffset, если надо
            return Intervals[0].From.Add(TimeSpan.FromMinutes(isNeedStartOffset ? StartOffset : 0));
        }

        public void SetupPeakIntervals(List<PeakInterval> peakIntervals)
        {
            PeakIntervals = peakIntervals;
        }

        // про границы интервалов
        //https://confluence.loyaltyplant.com/pages/viewpage.action?pageId=20189083&preview=/20189083/21498469/Рабочие%20часы%20митинг.png

        // значение в минутах, на которое сдвигается левая граница интервала (типа того 00:10 -> 00:30)
        private int ShiftLeftBorderOnXMinutes(TimeSpan ts)
        {
            if (DigitalOrderingModule.Instance.CurrentOrder == null)
            {
                return 0;
            }

            var customDeliveryTime = GetMaxForDeliveryOrCookingTime(ts, DeliveryTime, true, false);
            var customCookingTime = GetMaxForDeliveryOrCookingTime(ts, CookingTime, false, true);

            if (DigitalOrderingModule.Instance.CurrentOrder.IsDeliveryOrCatering())
                return StartOffset + customCookingTime + customDeliveryTime;
            else
                return StartOffset + customCookingTime;
        }

        // значение в минутах, на которое сдвигается правая граница интервала (типа того 23:50 -> 23:30)
        private int ShiftRightBorderOnXMinutes(TimeSpan ts)
        {
            if (DigitalOrderingModule.Instance.CurrentOrder == null)
            {
                return 0;
            }

            var customDeliveryTime = GetMaxForDeliveryOrCookingTime(ts, DeliveryTime, true, false);
            var customCookingTime = GetMaxForDeliveryOrCookingTime(ts, CookingTime, false, true);

            if (DigitalOrderingModule.Instance.CurrentOrder.IsDeliveryOrCatering())
                return customCookingTime + Math.Max(customDeliveryTime, EndOffset);
            else
                return customCookingTime + EndOffset;
        }

        // Со скольки работает точка в этот день
        public TimeSpan GetFrom()
        {
            if (Intervals.Count == 0)
                return default(TimeSpan);

            Intervals.Sort();
            return Intervals[0].From;
        }

        // До скольки работает точка в этот день
        public TimeSpan GetTo()
        {
            if (Intervals.Count == 0)
                return default(TimeSpan);

            Intervals.Sort();
            return Intervals[Intervals.Count - 1].To;
        }

        public DateTime? GetAsapForFirstAvailableWorkingDay(DateTime startTime, int minCheckoutPeriod)
        {
            var executionTimeMinutes = GetExecutionTimeMinutes(startTime);
            var asap = startTime.AddMinutes(executionTimeMinutes);

            if (minCheckoutPeriod > 0)
            {
                if (asap.Day == startTime.Day)
                {
                    return GetExcetionTime(startTime);
                }
            }

            return GetAsap(startTime);
        }

        // Вернуть время "исключения", когда currentTime не попадает ни в какой интервал
        private DateTime GetExcetionTime(DateTime startTime)
        {
            // сомнительное решение
            if (IsWasTransitThroughMidnight && startTime.TimeOfDay == ZeroFrom)
            {
                return startTime;
            }

            var from = startTime.Date.Add(GetFromTimeSpan(startTime.TimeOfDay));
            var executionTimeMinutes = GetExecutionTimeMinutes(from);
            return from.AddMinutes(executionTimeMinutes);
        }

        // TODO: надо переименовать, чтобы было понятнее. А то у нас 3 GetAsap метода сейчас.
        public DateTime? GetAsap(DateTime startTime)
        {
            // если был переход через сутки и startTime = 00:00, то не надо левую границу интервала обрезать
            var executionTimeMinutes = IsWasTransitThroughMidnight && (startTime.TimeOfDay == ZeroFrom) ? 0 : GetExecutionTimeMinutes(startTime);
            var asap = startTime.AddMinutes(executionTimeMinutes);

            if (asap.DayOfWeek != Day)
            {
                // был переход через полночь, поэтому вернем asap учитывая StartOffset
                // TODO потенциально тут проблема если след день выходной
                if (IsWasTransitThroughMidnight || IsWillTransitThroughMidnight)
                {
                    LpLogger.LOG_D($"Was24hPerehod, it should be already a new working day, asap = " + asap.ToString());
                    return asap;
                }

                LpLogger.LOG_D($"ASAP with execution for {Day} is not available because it should be already a new working day");
                return null;
            }

            // Если asap попадает хотя бы в какой-то интервал
            var interval = GetIntervalForDateTime(asap);
            if (interval != null)
            {
                return asap;
            }

            // Если asap больше, чем правая граница всех временных интервалов
            if (Intervals.All((arg) => arg.To.Subtract(TimeSpan.FromMinutes(EndOffset)) < asap.TimeOfDay))
            {
                LpLogger.LOG_D($"ASAP with execution for {Day} is not available because it should be already a new working day by the time");
                return null;
            }

            // вернуть время "исключения"
            // по идеи, если мы не вернули ничего раньше, то у нас одна из двух ситуаций:
            // 1. время asap находится до начала работы точки
            // 2. asap находится между двумя интервалами
            return GetExcetionTime(startTime);
        }

        /// <summary>
        /// Detects if date is in one of the intervals of the current day including offsets
        /// </summary>
        /// <returns>Interval for dateTime</returns>
        /// <param name="dt">DateTime</param>
        /// https://confluence.loyaltyplant.com/pages/viewpage.action?pageId=20189083
        public WorkingDayInterval GetIntervalForDateTime(DateTime dt)
        {
            Intervals.Sort();

            var currentDay = dt.DayOfWeek;
            var currentTime = dt.TimeOfDay;

            if (currentDay != Day)
            {
                return null;
            }

            // когда currentOrder == null это тоже самое что asap
            if (DigitalOrderingModule.Instance.CurrentOrder == null ||
                DigitalOrderingModule.Instance.CurrentOrder.IsAsap)
            {
                return Intervals.FirstOrDefault(obj =>
                        currentTime >= obj.From.Add(TimeSpan.FromMinutes(obj.From == ZeroFrom && IsWasTransitThroughMidnight ? 0 : ShiftLeftBorderOnXMinutes(dt.TimeOfDay))) &&
                        currentTime.Subtract(TimeSpan.FromMinutes(GetExecutionTimeMinutes(dt))) <= obj.To.Subtract(TimeSpan.FromMinutes(obj.To == ZeroTo && IsWillTransitThroughMidnight ? 0 : ShiftRightBorderOnXMinutes(dt.TimeOfDay))));
            }
            else
            {
                return Intervals.FirstOrDefault(obj =>
                        currentTime >= obj.From.Add(TimeSpan.FromMinutes(obj.From == ZeroFrom && IsWasTransitThroughMidnight ? 0 : ShiftLeftBorderOnXMinutes(dt.TimeOfDay))) &&
                        currentTime <= obj.To);
            }
        }

        public PeakInterval GetPeakIntervalForDateTime(DateTime dt)
        {
            var currentDay = dt.DayOfWeek;
            var currentTime = dt.TimeOfDay;

            if (currentDay != Day)
            {
                return null;
            }

            return PeakIntervals.FirstOrDefault(obj => currentTime >= obj.From && currentTime <= obj.To);
        }

        // standardTime - DeliveryTime или CookingTime
        // тут можно обойтись одним параметром is... но вроде так нагляднее будет
        private int GetMaxForDeliveryOrCookingTime(TimeSpan currentTime, int standardTime, bool isDeliveryTime, bool isCookingTime)
        {
            if (PeakIntervals == null || PeakIntervals.Count == 0)
                return standardTime;

            var peakInterval = PeakIntervals.FirstOrDefault(obj => currentTime >= obj.From && currentTime <= obj.To);

            if (peakInterval != null)
            {
                if (isDeliveryTime)
                    return Math.Max(standardTime, peakInterval.PeakDeliveryTime);

                if (isCookingTime)
                    return Math.Max(standardTime, peakInterval.PeakCookingTime);
            }

            return standardTime;
        }

        public int GetExecutionTimeMinutes(DateTime dt)
        {
            var diffPostmates = 0;
            if (DigitalOrderingModule.Instance.CurrentOrder != null)
            {
                var zone = DigitalOrderingModule.Instance.CurrentOrder.CurrentZone as DODistrict;
                if (zone != null && zone.ExternalVendor != null)
                {
                    if (zone.ExternalVendor is DoPostmatesVendor doPostmatesVendor)
                    {
                        diffPostmates = doPostmatesVendor.ExecutionTimeDuration;
                    }
                }
            }

            var peakInterval = GetPeakIntervalForDateTime(dt);
            var fromProcesses = peakInterval != null ? peakInterval.PeakExecutionTime : DefaultExecutionTime;
            return Math.Max(fromProcesses, diffPostmates);
        }

        public int CompareTo(WorkingDay other)
        {
            if (Day > other.Day)
                return 1;
            if (Day < other.Day)
                return -1;
            else
                return 0;
        }

        public List<TimeInterval> GetTimeSpanIntervals(WorkingDayInterval interval, TimeSpan start, int pickerInterval)
        {
            var invervals = new List<TimeInterval>();

            var customStartOffset = (IsWasTransitThroughMidnight && interval.From == ZeroFrom) ? 0 : ShiftLeftBorderOnXMinutes(start);

            // get all intervals for working day interval
            var dayStart = interval.From.Add(TimeSpan.FromMinutes(customStartOffset));
            var dayEnd = interval.To;

            var nextIntervalStart = dayStart;
            while (nextIntervalStart < dayEnd)
            {
                var startInterval = nextIntervalStart;
                nextIntervalStart = nextIntervalStart.Add(TimeSpan.FromMinutes(pickerInterval));
                var endOfInterval = nextIntervalStart > dayEnd ? dayEnd : nextIntervalStart;
                invervals.Add(new TimeInterval(startInterval, endOfInterval));
            }

            // remove all intervals that are in conflict with start timeSpan
            invervals.RemoveAll((obj) => obj.EndTime <= start);
            return invervals;
        }

        public List<TimeInterval> GetWorkingDayIntervals(DateTime date, TimeSpan asapTime)
        {
            var invervals = new List<TimeInterval>();

            var from = GetFrom().Add(TimeSpan.FromMinutes(StartOffset));
            var execTime = GetExecutionTimeMinutes(date.Date.Add(from));
            // TODO сейчас закомментирую но для asap заказа оно должно учитываться
            //var to = TimeSpan.FromMinutes(EndOffset);
            var isAddedAsapToResult = false;

            foreach (var interval in Intervals)
            {
                var minValue = interval.From.Add(TimeSpan.FromMinutes(StartOffset + execTime));
                if (IsWasTransitThroughMidnight && interval.From == ZeroFrom)
                {
                    minValue = interval.From;
                }

                var maxValue = interval.To;/* - to;
                if (Will24hPerehod && interval.To == ZeroTo)
                {
                    maxValue = interval.To;
                }*/

                var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;
                if (Day == currentOrder.CurrentZone?.OutletDateTimeNow.DayOfWeek && !isAddedAsapToResult)
                {
                    if (minValue <= asapTime && asapTime < maxValue)
                    {
                        invervals.Add(new TimeInterval(asapTime, maxValue));
                        isAddedAsapToResult = true;
                    }
                }
                else
                {
                    invervals.Add(new TimeInterval(minValue, maxValue));
                }
            }
            return invervals;
        }
    }
}