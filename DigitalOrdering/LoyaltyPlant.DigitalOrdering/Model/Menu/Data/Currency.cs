using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace LoyaltyPlant.DigitalOrdering.Model.Menu.Data
{
    [Serializable]
    public class Currency
    {
        [JsonConverter(typeof(KindMoneyJsonConverter))]
        public enum KindMoney
        {
            Money,
            Points,
        }

        [JsonProperty("originalPrice")]
        public decimal OriginalPrice { get; set; }

        [JsonProperty("kind")]
        public KindMoney Kind { get; set; }

        [JsonProperty("price")]
        public decimal Price { get; set; }

        [JsonProperty("taxes")]
        public decimal[] Taxes { get; set; } = {};
    }
    
    public class KindMoneyJsonConverter : JsonConverter
    {
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var enumValue = (Currency.KindMoney) value;
            switch (enumValue)
            {
                case Currency.KindMoney.Money:
                    writer.WriteValue("money");
                    break;
                case Currency.KindMoney.Points:
                    writer.WriteValue("points");
                    break;
            }
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            var enumString = (string)reader.Value;
            return Enum.Parse(typeof(Currency.KindMoney), enumString, true);
        }

        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(string);
        }
    }
}