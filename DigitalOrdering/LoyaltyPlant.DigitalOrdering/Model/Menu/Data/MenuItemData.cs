using System;
using System.Collections.Generic;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Images;
using LoyaltyPlant.Images.Model;
using Newtonsoft.Json;

namespace LoyaltyPlant.DigitalOrdering.Model.Menu.Data
{
    [Serializable]
	public abstract class MenuItemData : BaseEntityData
    {
		private string _description;
		[JsonProperty("description")]
        public string Description
        {
            get
            {
                return DigitalOrderingTexts.TryToParseMultiLanguageValue(_description);
            }
            set
            {
                _description = value?.TrimStart(' ') ?? string.Empty;
            }
        }

        [JsonProperty("categories")]
        public List<CategorySetting> CategorySettings { get; set; } = new List<CategorySetting>();

        [JsonProperty("images")]
        public string[] ImageUrls { get; set; } = { };
        
        [JsonIgnore]
        public string FormattedPrice { get; set; }

        [JsonIgnore]
        public decimal FormattedPriceDecimal { get; set; } = default;

        [JsonIgnore]
	    internal List<LPImage> Images { get; } = new List<LPImage>();

	    public LPImage Image
	    {
		    get
		    {
			    return Images.Count > 0 ? Images[0] : null;
		    }
		    set
		    {
			    if (Images.Count == 0)
			    {
				    Images.Add(value);
			    }
			    else
			    {
				    Images[0] = value;
			    }
		    }
	    }
	    
        [JsonProperty("prices")]
        public CurrencyContainer Prices { get; set; } = new CurrencyContainer();

	    [JsonProperty("modifierGroups")]
	    public List<GroupModifierData> GroupModifiers { get; set; } = new List<GroupModifierData>();

		[JsonProperty("availabilityInfo")]
		public List<AvailabilityIntervalData> AvailabilityIntervalsDataInfo { get; set; } = new List<AvailabilityIntervalData>();

		[JsonIgnore]
        public AvailabilityInfo AvailabilityIntervalsInfo { get; set; }

		[Serializable]
	    public class CategorySetting
	    {
		    [JsonProperty("categoryId")]
		    public string CategoryId { get; set; }

		    [JsonProperty("sortOrder")]
		    public long SortOrder { get; set; }
	    }

	    public void SetupImage()
	    {
		    var imageUrl = ImageUrls.Length == 0 ? "" : ImageUrls[0];
		    var lpImage = ImageFactory.MenuPreview(imageUrl, ImageType.DeliveryImageType).PlaceHolder().Build();
			Image = lpImage;
	    }
    }
}
