using System;
using Newtonsoft.Json;

namespace LoyaltyPlant.DigitalOrdering.Model.Menu.Data
{
    [Serializable]
    public class CurrencyContainer
    {
        [JsonProperty("money")]
        public Currency Money { get; set; } = new Currency();

        [JsonProperty("points")]
        public Currency Points { get; set; } = new Currency();

        public decimal Price
        {
            get
            {
                return Money.Price;
            }
            set
            {
                Money.Price = value;
            }
        }

        public long PointsCount
        {
            get
            {
                return (long) Points.Price;
            }
            set { Points.Price = value; }
        }
    }
}