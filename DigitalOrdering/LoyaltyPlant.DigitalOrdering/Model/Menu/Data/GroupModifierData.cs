using System;
using System.Collections.Generic;
using LoyaltyPlant.DigitalOrdering.Utils;
using Newtonsoft.Json;

namespace LoyaltyPlant.DigitalOrdering.Model.Menu.Data
{
    [Serializable]
    public class GroupModifierData : BaseEntityData
	{
		[JsonProperty("collapsibility")]
		public Collapsibility Collapsibility { get; set; }

		[JsonProperty("basedOnModifierGroupId")]
		public string BasedOnModifierGroupId { get; set; }

        private string _description;
        [JsonProperty("description")]
        public string Description
        {
            get
            {
                return DigitalOrderingTexts.TryToParseMultiLanguageValue(_description);
            }
            set
            {
                _description = value;
            }
        }

        [JsonProperty("sortOrder")]
		public long SortOrder { get; set; }

	    [JsonProperty("min")]
	    public Constraint Min { get; set; } = new Constraint();

        [JsonProperty("max")]
		public Constraint Max { get; set; } = new Constraint();

        [JsonProperty("free")]
		public Constraint Free { get; set; } = new Constraint();

        [JsonProperty("modifiers")]
		public List<ModifierSetting> ModifierSettings { get; set; } = new List<ModifierSetting>();
	    
        [Serializable]
		public class ModifierSetting
		{
			[JsonProperty("selected")]
			public bool Selected { get; set; }

			[JsonProperty("modifierId")]
			public string ModifierId { get; set; }

			[JsonProperty("sortOrder")]
			public long SortOrder { get; set; }
		}
	}

	public enum GroupModifierFreeTypes
	{
		None,
		Price,
		Quantity,
		Points
	}
	
	public enum GroupModifierType
	{
		SelectOne,
		Multiselect
	}
}

