using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Messages.ChooserMessages;

namespace LoyaltyPlant.DigitalOrdering
{
    public interface IDigitalOrderingFrontend : IModuleFrontend
    {
        void ShowAvailabilityInfoHint(AvailabilityInfo info);
        void ShowDigitalOrderingMenuScreen(string categoryId = "");
        void ShowDoCategoryScreen(Category category);
        void ShowDoMenuItemScreen(string positionId, bool isUpsellItem = false, bool isChangeItemFlow = false);
        // TODO избавиться от этого метода во frontend-е
        void ShowDoCartScreen();
        void ShowStatusScreen(CommonAction commonAction);
        void ShowSubscriptionStatusScreen(CommonAction commonAction);
        void ShowDoOrderScreen(DOPaidOrder doPaidOrder);
        void ShowDoSelectProcessMessage();
        void ShowCallToActionMessage(string uuid, CallToActionType callToActionType);
        void ShowOrdersHistoryScreen();
        void ShowDoDeliveryMapScreen(DOProcess process);
        void ShowDoPickupMapScreen(DOProcess process, bool isReturnBack);
        void ShowChooserMessage(ChooserMessage message);
        void ShowChooserBottomSheetMessage(ChooserMessage message);
        void ShowCheckoutScreen();
        void ShowSubscriptionCheckoutScreen();
        void ShowDoSearchDeliveryAddressScreen();
        void ShowAddressApartmentError(string errorText);
        void ShowScanQrCodeScreen();
        void ShowDoMenuSearchScreen();
        void ShowDoMultibrandScreen(Place place);
        void ShowQuicklyOrderToLastAddressMessage(IList<ChooserActionItem> chooserActionItems);

        void UpdateUpsellBlock();

        void ShowPresentRedeemSelectionMessage(Present present);

        // открыть меню как будто только что зашли  с карты.
        void ShowRootMenu();

        // открыть конкретную категорию, которая содержит категории как будто только что зашли с карты.
        void ShowRootMenu(string categoryId);

        // открыть конкретную категорию, который содержит айтемы как будто только что зашли с карты.
        void ShowRootCategory(Category category);
    }
}
