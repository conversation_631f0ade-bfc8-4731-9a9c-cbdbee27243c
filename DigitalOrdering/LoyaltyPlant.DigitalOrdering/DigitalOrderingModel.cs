using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Utils;

namespace LoyaltyPlant.DigitalOrdering
{
    [Serializable]

    // !!! Обязательно прочти перед тем, как сохранить изменения в этом классе
    // Если тут что-то поменял в этом классе, то не забудь
    // УвЕлИчИтЬ на 1 CacheVersion !!!

    public class DigitalOrderingModel : IModuleModel
    {
        public int CacheVersion => 23;
        
        public DateTime LastTimeRefreshedProcesses { get; set; }

        public List<DigitalOrderingCard> DigitalOrderingCards { get; } = new List<DigitalOrderingCard>();
        public string ProcessesTitle { get; set; }
        public long ProcessesVersion { get; set; }
        public long SavedVersion { get; set; }
        public List<DOProcess> Processes { get; } = new List<DOProcess>();
		
        public DeliveryPlaces DeliveryPlaces = new DeliveryPlaces();
        
        private DOOrder _currentOrder;

        public DOOrder CurrentOrder
        {
            get => _currentOrder;
            set
            {
                _currentOrder = value;
                DigitalOrderingModule.Instance?.SetOrderContextFromOrder(value);
            }
        }
        
        public int OutletIdOfLastOrderWithBankCard { get; set; }
        public int MaxPresentNumberInOrder { get; set; }
        public DoScanQrCodeCard ScanQrCodeCard { get; set; }
    }
}
