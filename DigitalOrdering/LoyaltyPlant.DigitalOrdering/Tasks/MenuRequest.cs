using System;
using System.Net.Http;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.DigitalOrdering.Model;

namespace LoyaltyPlant.DigitalOrdering.Tasks
{
    public class MenuRequest : BaseLpJsonRequest
    {
        private readonly bool _changesRequest;
        public override HttpMethod Method => HttpMethod.Get;

#if ACCEPTANCE_TESTING
        public override string Url
        {
            get
            {
                if (Engine.Instance.Configuration?.CustomDigitalOrderingServer != null)
                    return Engine.Instance.Configuration.CustomDigitalOrderingServer.Combine("do-menu/api/v1/menu" + (_changesRequest ? "/changes" : ""));

                return Engine.Instance.Configuration.Server.Combine("do-menu/api/v1/menu" + (_changesRequest ? "/changes" : ""));
            }
        }
#else
        public override string Url => Engine.Instance.Configuration.Server.Combine("do-menu/api/v1/menu" + (_changesRequest ? "/changes" : ""));
#endif

        public MenuRequest(DOOrder currentOrder, MenuIdentificator menuId = null)
        {
            _changesRequest = currentOrder.GetMenu(menuId).LastModified != default(DateTime) && (menuId == null);

            if (_changesRequest)
            {
                AddQuery("menuVersion", currentOrder.GetMenu().LastModified.ToString(DigitalOrderingModule.IsoDateTimeFormat));
            }

            AddQuery("partnerId", Engine.Instance.Configuration.PartnerId.ToString());

            string zoneId;
            if (menuId != null)
            {
                zoneId = menuId.ZoneId.ToString();
                DigitalOrderingModule.Instance.CurrentOrder.ChangedOutletId = menuId.ZoneId;
            }
            else
            {
                zoneId = currentOrder.IsDeliveryOrCatering() ? currentOrder.SelectedRegionId.ToString() : currentOrder.OutletId.ToString();
            }

            if (currentOrder.IsDeliveryOrCatering())
                AddQuery("districtId", zoneId);
            else
                AddQuery("salesOutletId", zoneId);

            AddQuery("orderingProcessId", currentOrder.ProcessId.ToString());
            AddQuery("orderingType", currentOrder.Process.Type.GetQueryName());
        }

        protected override void WriteJson()
        {
            
        }
    }
}