using System;
using System.Net.Http;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using Newtonsoft.Json.Linq;

namespace LoyaltyPlant.DigitalOrdering.Tasks
{
    public class DeliveryFeeOrderingTimeRequest : BaseLpJsonRequest
    {
        private readonly DOOrder _order;

        public override HttpMethod Method => HttpMethod.Post;
        public override string Url => Engine.Instance.Configuration.Server.Combine("/digitalordering/api/v1/" + "delivery-fee-ordering-time");

        public DeliveryFeeOrderingTimeRequest(DOOrder currentOrder)
        {
            _order = currentOrder;

            Encrypted = true;
            Decrypted = true;
        }

        protected override void WriteJson()
        {
            if (_order != null)
            {
                if (_order.IsDeliveryOrCatering())
                    JObject.Add("districtId", _order.SelectedRegionId);
                else
                    JObject.Add("outletId", _order.OutletId);

                JObject.Add("processId", _order.ProcessId);
                JObject.Add("processType", JToken.FromObject(DigitalOrderingModule.Instance.CurrentOrder.Process.Type).ToString());
                JObject.Add("addressBuilding", _order.Address);

                // todo refactor this https://jira.loyaltyplant.com/browse/APP-620
                JObject.Add("subtotal", _order.GetSubtotal());

                if (_order.Process.Type.IsDeliveryOrCatering())
                {
                    var lat = _order.DeliveryPlace.Latitude.ToString();
                    var lng = _order.DeliveryPlace.Longitude.ToString();

                    JObject.Add("coordinatesBuilding", WriteCoordinatesBuilding(lat, lng));
                }
                
                var type = _order.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap ? "ASAP" : "CUSTOM";

                var date = LpDateTime.DateToStringServerFormat(_order.OrderDate);
                var timeFrom = LpDateTime.TimeToStringServerFormat(_order.OrderTimeFrom);
                var timeTo = LpDateTime.TimeToStringServerFormat(_order.OrderTimeTo);

                JObject.Add("desirableCompleteOrderTime", WriteDesirableCompleteOrderTime(type, date, timeFrom, timeTo));
            }
        }

        public static JObject WriteCoordinatesBuilding(string lat, string lng)
        {
            var itemRoot = new JObject
            {
                { "lat", lat },
                { "lng", lng }
            };

            return itemRoot;
        }

        public static JObject WriteDesirableCompleteOrderTime(string type, string date, string timeFrom, string timeTo)
        {
            var itemRoot = new JObject
            {
                { "type", type },
                { "date", date },
                { "timeFrom", timeFrom },
                { "timeTo", timeTo }
            };

            return itemRoot;
        }
    }
}