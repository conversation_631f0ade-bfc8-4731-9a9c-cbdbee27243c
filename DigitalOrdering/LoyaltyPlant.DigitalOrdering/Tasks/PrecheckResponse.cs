using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using Newtonsoft.Json.Linq;

namespace LoyaltyPlant.DigitalOrdering.Tasks
{
    public class PrecheckResponse : BaseLpJsonResponse
    {
        public decimal DeliveryFee { get; private set; }
        public decimal ServiceFee { get; private set; }
        public bool Accepted { get; set; }
        public decimal Subtotal { get; private set; }
        public DateTime MinimalOrderTime { get; private set; }
        public decimal Tax { get; private set; }
        public List<string> ItemIds { get; private set; }
        public int VendorDuration { get; private set; }
        public decimal Discount { get; private set; }
        public bool IsDOUnavailable { get; private set; }

        protected override void ParseJson(JObject root)
        {
            var preCheck = root.ToObject<Precheck>();
            Accepted = preCheck.Accepted;
            DeliveryFee = preCheck.DeliveryFee;
            ServiceFee = preCheck.ServiceFee;
            Subtotal = preCheck.Subtotal;
            Tax = preCheck.Tax;
            ItemIds = preCheck.Messages.Select(x => x.ItemId).ToList();
            VendorDuration = preCheck.VendorDuration;
            Discount = Math.Min(preCheck.Discount, preCheck.Subtotal);
            IsDOUnavailable = preCheck.Messages.Count() == 1 && preCheck.Messages[0].Text.Contains("Online ordering is not available at the moment");

            if (!preCheck.Accepted)
            {
                LpLogger.LOG_E("DoPreCheck was not accepted\n" +
                               $"Subtotal:{preCheck.Subtotal}\n" +
                               $"DeliveryFee:{preCheck.DeliveryFee}\n" +
                               $"ServiceFee:{preCheck.ServiceFee}\n" +
                               $"Tax:{preCheck.Tax}\n");
                foreach (var preCheckMessage in preCheck.Messages)
                {
                    LpLogger.LOG_W($"itemId: {preCheckMessage.ItemId}\n{preCheckMessage.Text}");
                }
                return;
            }

            MinimalOrderTime = preCheck.MinimalOrderTime;
        }
    }
}