using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Analytics;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.ExternalVendors.Postmates;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Messages.ChooserMessages;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.Utils;
using LoyaltyPlant.Payment;
using LoyaltyPlant.Payment.Gates;
using LoyaltyPlant.Payment.Gates.VivaWalletGate;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Texts;
using LoyaltyPlant.User;
using Xamarin.Essentials;

namespace LoyaltyPlant.DigitalOrdering
{
    [Analytics]
    public class DigitalOrderingController : BaseModuleController<DigitalOrderingModule, DigitalOrderingModel, IDigitalOrderingFrontend>
    {
        private int _presentIdForGettingInDO;
        private Dictionary<int, HashSet<DoProcessType>> _presentsAvailableProcesses = new Dictionary<int, HashSet<DoProcessType>>();
        private string _ctaPositionUuidForGettingInDO;
        private string _ctaMenuCategoryUuid;
        private List<int> _ctaAvailableProcessesIds;
        private DOOrder CurrentOrder => Module.CurrentOrder;
        private bool _needToShowMinSumForOrderDialogNextTime = true;

        public MultiplyActionPreventer QuickRemoveActionPreventer = new MultiplyActionPreventer(500);
        #region Constructor
        public DigitalOrderingController(IDigitalOrderingFrontend frontend) : base(frontend)
        {
        }
        #endregion

        public void ShowDOMenuPositionScreen(MenuPosition position, bool isUpsellItem = false)
        {
            Frontend.ShowDoMenuItemScreen(position.Id, isUpsellItem);
        }

        public void ShowDOMenuPositionScreen(CartMenuItem cartMenuItem, bool isUpsellItem = false)
        {
            var positionInCurrentOrder = Module.CurrentOrder.Cart.Entities.IndexOf(cartMenuItem);
            Frontend.ShowDoMenuItemScreen(positionInCurrentOrder.ToString(), isUpsellItem, true);
        }

        public void ShowQrCodeScanner()
        {
            Frontend.ShowScanQrCodeScreen();
        }

        public void ShowDoCartScreen()
        {
            // TODO заменить на другое действие и переписать юнит-тесты
            // вот бы еще вспомнить почему
            Frontend.ShowDoCartScreen();
        }

        public async Task ShowEatInMapScreen()
        {
            try
            {
                ShowLoadingIndicator(I18N.LOADING);

                var eatInProcess = await GetAvailableEatInProcess();
                if (eatInProcess == null)
                    return;

                await OnSelectDoProcess(eatInProcess);
            }
            finally
            {
                HideLoadingIndicator();
            }
        }

        public async Task<DOProcess> GetAvailableEatInProcess()
        {
            var updateProcessesResult = await Module.EnsureLoadProcesses();
            if (!updateProcessesResult.IsResponseEnsured)
            {
                if (!updateProcessesResult.IsConnectionProblemMessageWasShown)
                    CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER);
                return null;
            }

            var eatInProcess = Module.GetEatInProcess();
            if (eatInProcess == null)
            {
                //TODO добавить вменяемый текст ?
                ShowPopup(I18N.DO_NO_PROCESSES_FOR_ORDER);
                return null;
            }

            return eatInProcess;
        }

        public void ShowDoSearchDeliveryAddressScreen()
        {
            Frontend.ShowDoSearchDeliveryAddressScreen();
        }

        public void ShowDoMenuSearchScreen()
        {
            Frontend.ShowDoMenuSearchScreen();
        }

        public void ShowDoMultibrandScreen(Place place)
        {
            Frontend.ShowDoMultibrandScreen(place);
        }

        private void StartNewDoProcess(bool isNeedToShowProcesses = false)
        {
            _ctaMenuCategoryUuid = null;
            _ctaPositionUuidForGettingInDO = null;

            Module.DeleteAllCreatedOrders();
            _ = Start(isNeedToShowProcesses);
        }

        public void CreateErrorScanQrCodeMessage(Action clickAction = null)
        {
            CreateOneButtonMessage(I18N.ERROR_QR_CODE_SCAN, I18N.OK, () =>
            {
                clickAction?.Invoke();
                ShowMainScreen();
            });
        }

        public void RestoreAllSubscriptionsFromCartItem(CartMenuItem item)
        {
            if (item.MenuItem is GiftPosition giftPosition)
            {
                var subscriptionCard = LoyaltyModule.Instance.SubscriptionCards.
                            FirstOrDefault(s => s.Uuid == giftPosition.LinkedPresent.PreviewText);

                if (subscriptionCard != null &&
                    giftPosition != null &&
                    giftPosition.IsSubscriptionItem)
                {
                    var removedItems = CurrentOrder.Cart.Entities.
                            Where(e => e.Label == item.Label && e.ModifiersText == item.ModifiersText).
                            ToList();

                    foreach (var removedItem in removedItems)
                    {
                        CurrentOrder.Cart.RemoveCartEntity(removedItem);
                        if (!subscriptionCard.Presents.Contains(giftPosition.LinkedPresent))
                            subscriptionCard.Presents.Add(giftPosition.LinkedPresent);
                    }
                }

                Module.Save();
                LoyaltyModule.Instance.Save();
            }
        }

        public void RestoreSubscriptionFromCartItem(CartMenuItem item)
        {
            var subscriptionCard = LoyaltyModule.Instance.SubscriptionCards.
                FirstOrDefault(s => s.Uuid == (item.MenuItem as GiftPosition).LinkedPresent.PreviewText);

            var giftPos = item.MenuItem as GiftPosition;
            if (subscriptionCard != null && giftPos != null && giftPos.IsSubscriptionItem && !subscriptionCard.Presents.Contains(giftPos.LinkedPresent))
            {
                subscriptionCard.Presents.Add(giftPos.LinkedPresent);
                LoyaltyModule.Instance.Save();
            }
        }

        public async Task TryAddSubscriptionItemToCart(CartMenuItem item)
        {
            var subscriptionCard = LoyaltyModule.Instance.SubscriptionCards.
                            FirstOrDefault(s => s.Uuid == (item.MenuItem as GiftPosition)?.LinkedPresent.PreviewText);

            var giftPos = Module.GetGiftPositionBySubscriptionCard(subscriptionCard, true);
            if (giftPos != null)
                item = new CartMenuItem(giftPos);

            if (await item.TryAdd())
                OnClickedDoItem(item.MenuItem);
            else
                ShowMaxCountErrorMessage(false);
        }

        public void RemoveCartItem(CartMenuItem item, Action successCallback, Action cancelCallback)
        {
            var itemTotalPrice = item.IsGift ? item.GetPresentPriceValue() : item.GetTotalPrice();
            var totalIfItemWillDelete = CurrentOrder.GetTotalWithoutFeesAndsTips() - itemTotalPrice;

            CurrentOrder.IsValidSumOfOrder(out decimal minValue);

            if (totalIfItemWillDelete < minValue)
            {
                CreateDeleteCartPositionMessage(item, successCallback, cancelCallback);
            }
            else if (CurrentOrder.Cart.Entities.Any(item => item.MenuItem is GiftPosition gift && gift.IsCampaighPosition))
            {
                var logStringBuilder = new StringBuilder("Checking campaign presents.");
                var campaighGifts = CurrentOrder.Cart.Entities.
                    Where(item => item.MenuItem is GiftPosition gift && gift.IsCampaighPosition).
                    Select(giftItem => giftItem.MenuItem as GiftPosition).
                    ToList();
                var currentIdsInCart = CurrentOrder.Cart.Entities.Select(entity => entity.Id).ToList();
                currentIdsInCart.Remove(item.Id);

                var notAcceptedGifts = campaighGifts.
                    Where(gift => gift.RequiredItems.Contains(item.Id) && !gift.CheckIsCampaignGiftAvailable(currentIdsInCart, out var _, out var _)).
                    ToList();
                logStringBuilder.AppendLine().Append($"Number of unaccepted gifts: {notAcceptedGifts.Count}");
                if (notAcceptedGifts.Any())
                {
                    logStringBuilder.AppendLine().Append($"IDs of unaccepted gifts: {string.Join(",", notAcceptedGifts.Select(gift => gift.Id).ToArray())}");
                    var dialogMessage = string.Format(I18N.REQUIRED_ITEMS_FOR_DISCOUNT_REMOVED,
                        String.Join(", ", notAcceptedGifts.Select(gift => gift.Label).ToArray()));
                    CreateOkCancelMessage(dialogMessage,
                        () =>
                        {
                            CurrentOrder.Cart.RemoveCartEntity(item);
                            var notAcceptedGiftsIds = notAcceptedGifts.Select(gift => gift.Id).ToList();
                            var giftCartItems = CurrentOrder.Cart.Entities.
                                Where(entity => notAcceptedGiftsIds.Contains(entity.Id)).
                                ToList();
                            giftCartItems.ForEach(cartItem => CurrentOrder.Cart.RemoveCartEntity(cartItem));

                            Module.Save();
                            successCallback();
                        },
                        () =>
                        {
                            cancelCallback?.Invoke();
                        });
                }
                else
                {
                    CreateDeleteCartPositionMessage(item, successCallback, cancelCallback);
                }
                LpLogger.LOG_I(logStringBuilder.ToString());
            }
            else
            {
                CreateDeleteCartPositionMessage(item, successCallback, cancelCallback);
            }
        }

        private void CreateDeleteCartPositionMessage(CartMenuItem item, Action successCallback, Action cancelCallback)
        {
            var messageText = DigitalOrderingTexts.DeletePositionForCart(item);

            CreateOkCancelMessage(messageText,
                () =>
                {
                    CurrentOrder.Cart.RemoveCartEntity(item);
                    Module.Save();
                    successCallback?.Invoke();
                },
                () =>
                {
                    cancelCallback?.Invoke();
                });
        }

        public void CreateRemoveUnavailableEntitiesMessage(List<CartMenuItem> entitiesLabels, Action successCallback, Action cancelCallback)
        {
            var positionTitlesStringList = new List<string>();
            foreach (var entity in entitiesLabels)
            {
                positionTitlesStringList.Add(entity.Label);
            }
            var positionTitlesString = string.Join(", ", positionTitlesStringList);

            var messageText = string.Format(I18N.CHANGE_CART_BECAUSE_OF_TIME_MESSAGE, positionTitlesString);

            CreateOkCancelMessage(messageText,
                () =>
                {
                    foreach (var item in entitiesLabels)
                    {
                        CurrentOrder.Cart.RemoveCartEntity(item);
                    }

                    Module.Save();
                    successCallback();
                },
                () =>
                {
                    cancelCallback?.Invoke();
                });
        }

        public bool CreateCampaighGiftUnavailableDialogIfNeeded(Action positiveAction = null, bool isDialogWasAlreadyShown = false)
        {
            // The case when we try to process something but the cart is not initialized. Also protection from over-messages on iOS;
            if (DigitalOrderingModule.Instance.CurrentOrder?.Cart?.Entities == null || !DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities.Any() || isDialogWasAlreadyShown)
                return true;

            var campaighGiftsList = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities.
                    Where(entity => entity.MenuItem is GiftPosition giftPosition && giftPosition.IsCampaighPosition).
                    Select(gift => gift.MenuItem as GiftPosition).
                    Where(item => item != null).
                    ToList();
            if (!(campaighGiftsList?.Any() ?? false))
                return true;

            var isGiftsAvailable = true;
            var unavailableCampaighsMessage = new StringBuilder();

            foreach (var gift in campaighGiftsList)
            {
                var giftErrorMessage = DigitalOrderingModule.Instance.GetCampaighGiftLimitsErrorMessage(gift, out var isGiftBlocked);
                var isGiftAvailable = string.IsNullOrEmpty(giftErrorMessage);
                if (!isGiftAvailable)
                {
                    if (isGiftBlocked)
                        CreateOkMessage(string.Format(I18N.CAMPAIGN_UNAVAILABLE_GIFT_MESSAGE, gift.Label));
                    else
                        unavailableCampaighsMessage.Append(giftErrorMessage).AppendLine().AppendLine();
                }
                isGiftsAvailable &= isGiftAvailable;
            }

            var commonCampaighMessage = unavailableCampaighsMessage.ToString().TrimEnd('\n');
            if (!string.IsNullOrEmpty(commonCampaighMessage))
                CreateTwoButtonsMessage(commonCampaighMessage,
                    positiveButtonText: I18N.OK,
                    positiveButtonAction: positiveAction,
                    negativeButtonText: I18N.ADD_MORE_ITEMS,
                    negativeButtonAction: async () =>
                    {
                        await DigitalOrderingModule.Instance.Controller.UpdateAndShowMenu();
                    });

            return isGiftsAvailable;
        }

        public bool CreateLotteryDialogIfNeeded(Action negativeAction = null, bool isDialogWasAlreadyShown = false)
        {
            // The case when we try to process something but the cart is not initialized. Also protection from over-messages on iOS;
            if (DigitalOrderingModule.Instance.CurrentOrder?.Cart?.Entities == null 
                || !DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities.Any() 
                || isDialogWasAlreadyShown)
                return false;

            if (!PartnerModule.Instance.IsNeedToShowAchievementsMessage(out _))
                return false;

            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;
            var currentTotal = currentOrder.GetTotal(currentOrder.SelectedTipPercent);

            if (!PartnerModule.Instance.GetLotteryLimits(out var maxPriceForLottery, out var minPriceForLottery))
            {
                LpLogger.LOG_I("DigitalOrderingController.CreateLotteryDialogIfNeeded(): Cannot parse limits. Dialog will not be shown");
                return false;
            }

            if (currentTotal >= maxPriceForLottery)
                return false;

            var dialogText = currentTotal < minPriceForLottery
                ? I18N.LOTTERY_UPSELL_SINGLE
                : I18N.LOTTERY_UPSELL_MULTIPLE;

            var amount = currentTotal < minPriceForLottery
                ? minPriceForLottery - currentTotal
                : maxPriceForLottery - currentTotal;

            var finalDialogString = string.Format(dialogText, Formatter.FormatAmount(Math.Ceiling(amount), shouldRoundToWholeNumber: true));
                
            CreateTwoButtonsMessage(finalDialogString,
                positiveButtonText: I18N.ADD_MORE_ITEMS,
                positiveButtonAction: async () =>
                {
                    await DigitalOrderingModule.Instance.Controller.UpdateAndShowMenu();
                },
                negativeButtonText: I18N.OK,
                negativeButtonAction: negativeAction);

            return true;
        }

        public bool CreateDiscountOnlyDialogIfNeeded(Action okCallback = null)
        {
            bool isDiscountOnlyInCart = Module.CurrentOrder.Cart.IsOnlyDiscountInCart();
            if (isDiscountOnlyInCart)
            {
                LpLogger.LOG_I("Order contains only dicount coupon item. Payment should be cancelled for server fail avoiding.");
                CreateOkMessage(I18N.ONLY_DISCOUNT_IN_CART_ERROR,
                    () => okCallback?.Invoke());
            }
            return isDiscountOnlyInCart;
        }

        public bool OnClickedToGetInOrderButton(Present present)
        {
            var presentId = present.Id;
            var menuCard = Module.Card;
            if (menuCard == null)
            {
                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                return false;
            }

            _presentIdForGettingInDO = presentId;
            _presentsAvailableProcesses[presentId] = GetAvailableProcessesForPresent(present);

            _ = Start();
            return true;
        }

        public void OnOrderCookingApprovedWithMessage(DOPaidOrder order)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return;
            }

            var buttons = new List<BaseChooserMessageItem>();
            buttons.Add(new BaseChooserMessageItem(I18N.YES_APPROVE_ORDER, async () => await OnOrderCookingApproved(order)));
            buttons.Add(new BaseChooserMessageItem(I18N.NO_NOT_APPROVED_YET, null));

            MessagesModule.Instance.CreateActionSheetMessage(I18N.APPROVE_ORDER_QUESTION, buttons);
        }

        public async Task OnOrderCookingApproved(DOPaidOrder order)
        {
            await ApproveOrder(order);
            ShowMainScreen();
        }

        public Task OnSelectProcessOnDoSelectionMessage(DOProcess process)
        {
            return OnSelectDoProcess(process);
        }

        protected async Task OnSelectDoProcess(DOProcess process)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return;
            }

            ShowLoadingIndicator(I18N.LOADING);

            await process.UpdateOutlets();

            HideLoadingIndicator();

            ShowDoMapScreen(process, false);
        }

        public async Task OnChangeOutlet()
        {
            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            await Module.UpdateProcesses(true);

            HideLoadingIndicator();

            var process = Module.Processes.FirstOrDefault(p => p.Id == CurrentOrder.ProcessId);

            if (process == null)
            {
                ShowMessageWhenProssesIsEmpty();
                return;
            }

            ShowDoMapScreen(process, true);
        }

        [MainThread]
        public void ShowDoMapScreen(DOProcess process, bool isReturnBack)
        {
            if (process.Type.IsDeliveryOrCatering())
            {
                LpAnalytics.ShowedScreen("map_do_delivery_screen");
                Frontend.ShowDoDeliveryMapScreen(process);
            }
            else
            {
                if (!process.Outlets.Any())
                {
                    ShowPopup(I18N.SORRY_FUNCTION_UNVAILABLE);
                    return;
                }
                else if (process.Outlets.Count == 1 && process.Outlets.First().IsAvailable)
                {
                    // Сейчас все кейсы ссылаются на данную функцию. Легче это расположить тут
                    SelectProcess(process.Type, process.Outlets.First().Id);
                    LpLogger.LOG_I("PicUp // EatIn process selected when only 1 outlet available: skipping outlet selection step and showing menu.");
                }
                else
                {
                    LpAnalytics.ShowedScreen("map_do_pickup_screen");
                    Frontend.ShowDoPickupMapScreen(process, isReturnBack);
                }
            }
        }

        public void ShowMaxCountErrorMessage(bool isMaxCountOne)
        {
            var errorMessage = isMaxCountOne ?
                            I18N.CANNOT_USE_MORE_THAN_ONE_PRESENT_MULTICODE :
                            I18N.CANNOT_USE_MORE_THAT_PRESENTS_MULTICODE;

            ShowPopup(errorMessage);
        }

        // recommend firebase method / event name https://support.google.com/firebase/answer/6375140
        public async Task<(bool IsSuccess, string ErrorItemId)>AddToCart(CartMenuItem item, bool isUpsellItem = false, Action positiveAction = null)
        {
            if (!CurrentOrder.CurrentZone.IsOrderingAvailable())
            {
                ShowPopup(DigitalOrderingTexts.WeCannotProcessYourOrder(CurrentOrder.CurrentZone));
                return (false, null);
            }

            var result = await CurrentOrder.Cart.TryAdd(item);
            if (!result.IsSuccess)
            {
                if (!result.ErrorMessage.IsNullOrEmpty())
                    ShowPopup(result.ErrorMessage);
                
                return (false, result.GroupModificatorId);
            }
            
            if (Engine.Instance.IsFeatureActive(Feature.DO_NOT_APPLY_DISCOUNTS_TO_COMBO) && item.IsDiscountItem)
            {
                CreateOkMessage(I18N.NO_DISCOUNT_FOR_COMBO_MESSAGE, positiveAction);
            }

            CurrentOrder.Cart.AddCartEntity(item);

            Module.Save();

            if (isUpsellItem)
                LpAnalytics.TrackUpsellItemAddToCart(item.BaseEntity.Label);

            LpAnalytics.TrackAddToCart(DigitalOrderingModule.Instance.GetFormattedDictionaryForAnalytics(item));

            return (true, null);
        }

        public bool SaveCartChanges(CartMenuItem item, int itemPositionInCart, bool isUpsellItem = false, string _initialModifiersText = "")
        {
            var itemsToChange = new List<CartMenuItem>();

            if (item.MenuItem is GiftPosition giftItem && giftItem.IsSubscriptionItem)
            {
                foreach (var cartItem in Module.CurrentOrder.Cart.Entities
                    .Where(entity => entity.MenuItem is GiftPosition giftCartItem
                    && giftCartItem.MenuItemUuid == giftItem.MenuItemUuid
                    && entity.ModifiersText == _initialModifiersText))
                {
                    itemsToChange.Add(cartItem);
                }
            }
            else
            {
                itemsToChange.Add(Module.CurrentOrder.Cart.Entities[itemPositionInCart]);
            }

            foreach (var itemToChange in itemsToChange)
            {
                if (!item.IsApprovedRequiredByCount(out var corruptedModifier))
                {
                    var errorMessage = DigitalOrderingTexts.OrderingMinOptions(corruptedModifier.GroupModifier.Min.Quantity, corruptedModifier.Label);
                    ShowPopup(errorMessage);
                    return false;
                }

                itemToChange.CartGroupModifiers = item.CartGroupModifiers;
            }

            Module.CurrentOrder.Cart.UpdateEntitiesList(item);
            Module.Save();

            if (isUpsellItem)
                LpAnalytics.TrackUpsellItemAddToCart(item.BaseEntity.Label);

            return true;
        }

        public async Task QuickAddItem(MenuItem menuItem, Action openCardAction, Action updateUiAction)
        {
            if (menuItem.GetFullCount() == 0)
            {
                if (menuItem.GroupModifiers != null && menuItem.GroupModifiers.Any())
                {
                    openCardAction?.Invoke();
                }
                else if (menuItem is GiftPosition gift && gift.ChildPositions != null && gift.ChildPositions.Any())
                {
                    openCardAction?.Invoke();
                }
                else
                {
                    var _cartItem = new CartMenuItem(menuItem);
                    await DigitalOrderingModule.Instance.Controller.AddToCart(_cartItem);
                    updateUiAction?.Invoke();
                }
            }
            else
            {
                var alreadyPresentedItem = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities
                    .LastOrDefault(item => item.Id == menuItem.Id);
                if (alreadyPresentedItem == null)
                {
                    if (menuItem is GiftPosition gift)
                    {
                        var idsString = string.Join(" ", gift.ChildPositions.Select(child => child.Id));
                        alreadyPresentedItem = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities
                            .LastOrDefault(item => idsString.Contains(item.Id));
                        await DigitalOrderingModule.Instance.Controller.AddToCart(alreadyPresentedItem.Clone());
                    }
                }
                else
                {
                    if (alreadyPresentedItem.MenuItem is GiftPosition gift)
                        await DigitalOrderingModule.Instance.Controller.AddToCart(alreadyPresentedItem.Clone());
                    else
                        alreadyPresentedItem.Count += 1;
                }
                updateUiAction?.Invoke();
            }
        }

        public void QuickRemoveCard(MenuItem menuItem, Action updateUiAction)
        {
            if (!QuickRemoveActionPreventer.CanUse())
            {
                return;
            }

            var alreadyPresentedItem = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities
                    .LastOrDefault(item => item.Id == menuItem.Id);
            if (alreadyPresentedItem == null && menuItem is GiftPosition gift)
            {
                var idsString = string.Join(" ", gift.ChildPositions.Select(child => child.Id));
                alreadyPresentedItem = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities
                    .LastOrDefault(item => idsString.Contains(item.Id));
            }

            if (alreadyPresentedItem.Count == 1)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.RemoveCartEntity(alreadyPresentedItem);
            }
            else
            {
                alreadyPresentedItem.Count -= 1;
            }
            updateUiAction?.Invoke();
        }

        public bool SelectModifier(CartMenuItem cartMenuItem, GroupModifier item, string modifierId)
        {
            var result = cartMenuItem.SelectModifier(item, modifierId, out var reason);
            if (!result)
            {
                ShowPopup(reason);
            }
            return result;
        }

        [SkipAnalytics]
        public void OnMenuScreenOpenned()
        {
            EnsurePresentInCart();
            EnsureCtaItemInCart();
        }

        private static HashSet<DoProcessType> GetAvailableProcessesForPresent(Present present)
        {
            if (!present.IsExtendedAvailabilityEnabled)
                return new HashSet<DoProcessType>(Enum.GetValues(typeof(DoProcessType)).Cast<DoProcessType>());

            var availableProcesses = new HashSet<DoProcessType>();
            
            if (present.CouponAvailabilityTypes.IsNullOrEmpty())
                return availableProcesses;
            
            foreach (var couponAvailabilityType in present.CouponAvailabilityTypes)
            {
                switch (couponAvailabilityType)
                {
                   case Present.CouponAvailabilityType.Catering:
                       availableProcesses.Add(DoProcessType.Catering);
                       break;
                   
                   case Present.CouponAvailabilityType.Delivery:
                       availableProcesses.Add(DoProcessType.Delivery);
                       break;
                   
                   case Present.CouponAvailabilityType.EatIn:
                       availableProcesses.Add(DoProcessType.EatIn);
                       break;
                   
                   case Present.CouponAvailabilityType.PickUp:
                       availableProcesses.Add(DoProcessType.PickUp);
                       break;
                }
            }
            return availableProcesses;
        }

        public void ResetPresentIdForGettingInDo()
        {
            _presentIdForGettingInDO = 0;
        }

        public List<DOProcess> ExcludeUnavailableProcessesForPresent(List<DOProcess> processes)
        {
            if (_presentIdForGettingInDO <= 0)
                return processes;

            var filteredProcesses = new List<DOProcess>();
            var presentAvailableProcesses = _presentsAvailableProcesses[_presentIdForGettingInDO];
            
            if (presentAvailableProcesses.IsNullOrEmpty())
                return filteredProcesses;

            filteredProcesses.AddRange(processes.Where(process => presentAvailableProcesses.Contains(process.Type)));

            return filteredProcesses;
        }
        
        public List<DOProcess> ExcludeUnavailableProcessesForCta(List<DOProcess> processes)
        {
            if (_ctaAvailableProcessesIds.IsNullOrEmpty())
                return processes;

            return processes.Where(process => _ctaAvailableProcessesIds.Contains(process.Id)).ToList();
        }

        public void ClearCtaAvailableProcessesIds()
        {
            _ctaAvailableProcessesIds = null;
        }

        private void EnsurePresentInCart()
        {
            if (_presentIdForGettingInDO > 0)
            {
                if (CurrentOrder?.GetMenu()?.MenuItems == null)
                {
                    LpLogger.LOG_W("EnsurePresentInCart(): CurrentOrder?.GetMenu()?.MenuItems is null. Skipping flow");
                    return;
                }

                var positions = CurrentOrder.GetMenu().MenuItems.
                    Where((arg) => arg is GiftPosition).Cast<GiftPosition>().
                    Where(g => g.LinkedPresentId == _presentIdForGettingInDO).ToList();

                if (!positions.Any())
                {
                    LpLogger.LOG_W("EnsurePresentInCart(): Cannot find present that was linked to present with id: " +
                                   _presentIdForGettingInDO);
                    
                    if (Engine.Instance.Platform.PlatformNumber == PlatformType.Android)
                        MessagesModule.Instance.CreatePostponedMessage(I18N.OFFER_DO_LINKED_TO_HIDDEN_POSITION, string.Empty, I18N.OK);
                    else
                        CreateOkMessage(I18N.OFFER_DO_LINKED_TO_HIDDEN_POSITION);
                    _presentIdForGettingInDO = 0;
                    return;
                }

                var position = (positions.Count() > 1) ?
                    positions.FirstOrDefault(arg => arg.ChildPositions.Count > 0) :
                    positions.FirstOrDefault();

                if (position != null)
                {
                    OnClickedDOGiftPosition(position.Id);
                    _presentIdForGettingInDO = 0;
                }
            }
        }

        private async Task EnsureCtaItemInCart()
        {
            if (!string.IsNullOrEmpty(_ctaPositionUuidForGettingInDO))
            {
                var position = CurrentOrder.GetMenu().GetMenuItem(_ctaPositionUuidForGettingInDO);
                if (position == null)
                {
                    CreateOkMessage(I18N.OFFER_NO_DO_ITEM_ON_THIS_ADDRESS);
                }
                else if (!position.IsVisible())
                {
                    CreateOkMessage(I18N.OFFER_DO_LINKED_TO_HIDDEN_POSITION);
                }
                else if (!position.IsAvailableToOrder())
                {
                    CreateOkMessage(I18N.OFFER_DO_LINKED_TO_HIDDEN_POSITION);
                }
                else
                {
                    if (position.GroupModifiers.Any())
                    {
                        OnClickedDoItem(position);
                    }
                    else
                    {
                        var cartItem = new CartMenuItem(position);

                        var additionResult = await CurrentOrder.Cart.TryAdd(cartItem);
                        if (!additionResult.IsSuccess)
                        {
                            if (!additionResult.ErrorMessage.IsNullOrEmpty())
                                ShowPopup(additionResult.ErrorMessage);
                            return;
                        }

                        CurrentOrder.Cart.AddCartEntity(cartItem);
                        ShowDoCartScreen();
                    }
                }

                _ctaPositionUuidForGettingInDO = null;
                _ctaAvailableProcessesIds = null;
            }

            if (!string.IsNullOrEmpty(_ctaMenuCategoryUuid))
            {
                var category = CurrentOrder.GetMenu().GetCategory(_ctaMenuCategoryUuid);

                if (category != null && category.IsVisible())
                {
                    OnClickedDoCategory(_ctaMenuCategoryUuid);
                }
                else
                {
                    CreateOkMessage(I18N.DO_CATEGORY_NOT_FOUND_ON_LOCATION);
                    LpLogger.LOG_E($"OnClickedDoCategory(): Clicked on a menu category but can't open it! Can't find category by id: {_ctaMenuCategoryUuid}. Showing an error messsage and doing nothing");
                }

                _ctaMenuCategoryUuid = null;
                _ctaAvailableProcessesIds = null;
            }
        }

        public void OnClickedDoItem(MenuItem menuItem)
        {
            if (menuItem is GiftPosition doGift)
            {
                if (!EnsureMultiPresentSelectionDialog(doGift))
                {
                    Frontend.ShowDoMenuItemScreen(doGift.Id);
                }
            }
            else if (menuItem is MenuPosition menuPosition)
            {
                ShowDOMenuPositionScreen(menuPosition);
            }
        }

        public void OnClickedDOGiftPosition(string giftPositionId)
        {
            var position = Module.CurrentMenu.GetMenuItem(giftPositionId);
            if (position == null)
                return;

            var doGift = position as GiftPosition;
            if (!EnsureMultiPresentSelectionDialog(doGift))
            {
                Frontend.ShowDoMenuItemScreen(position.Id);
            }
        }

        public void OnClickedSubscriptionPosition(SubscriptionCard subscriptionCard)
        {
            var giftPosition = Module.GetGiftPositionBySubscriptionCard(subscriptionCard, true);

            if (giftPosition == null)
                giftPosition = Module.GetGiftPositionBySubscriptionCard(subscriptionCard);

            if (giftPosition.IsSubscriptionItem)
            {
                //HACK чтобы последняя категория, в которую заходили, не учитывалась при добавление подарка в корзину.
                if (CurrentOrder.Cart != null)
                    CurrentOrder.Cart.LastCategoryId = Guid.Empty.ToString();

                OnClickedDOGiftPosition(giftPosition.Id);
            }
            else
                CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
        }

        [SkipAnalytics]
        public bool EnsureMultiPresentSelectionDialog(GiftPosition doGift)
        {
            // if dogift is linked to many items,  chooser will be shown
            if (doGift != null && doGift.ChildPositions.Count > 1)
            {
                OpenMultiPresentSelectionDialog(doGift);
                return true;
            }

            return false;
        }

        public void OpenMultiPresentSelectionDialog(GiftPosition position)
        {
            var message = new ChooserMessage(I18N.DO_SELECT_THE_REWARD);
            var buttons = new List<ChooserMessageItem>();

            foreach (var childPosition in position.ChildPositions)
            {
                var button = new ChooserMessageItem(childPosition.Image, childPosition.Label, () =>
                {
                    Frontend.ShowDoMenuItemScreen(childPosition.Id);
                });
                buttons.Add(button);
            }

            message.Buttons = buttons;
            Frontend.ShowChooserMessage(message);
        }

        public void OnClickedDoCategory(string categoryId)
        {
            if (Module.CurrentMenu == null)
            {
                // TODO изменить текст сообщения
                LpLogger.LOG_E($"OnClickedDoCategory(): Clicked on a menu category but can't open it! Module.CurrentMenu == null! for categoryId={categoryId}. Showing an arror and opening main screen");
                CreateOkMessage(I18N.DIGITAL_ORDERING_CATEGORIES_CLICK_ERROR);
                ShowMainScreen();
                return;
            }

            var category = Module.CurrentMenu.GetCategory(categoryId);

            if (category == null || !category.IsVisible())
            {
                CreateOkMessage(I18N.DO_CATEGORY_NOT_FOUND_ON_LOCATION);
                LpLogger.LOG_E($"OnClickedDoCategory(): Clicked on a menu category but can't open it! Can't find category by id: {categoryId}. Showing an error messsage and doing nothing");
                return;
            }

            if (category.IsParentCategory() && category.GetSubCategories().Any())
            {
                var visibleSubcategories = category.GetSubCategories().Where(item => item.IsVisible()).ToList();

                if (visibleSubcategories?.Count == 1)
                {
                    var subcategory = visibleSubcategories.ElementAt(0);
                    OnClickedDoCategory(subcategory.Id);
                    return;
                }

                Frontend.ShowDigitalOrderingMenuScreen(categoryId);

                return;
            }

            ProceedClickDoCategory(category);
        }

        private void ProceedClickDoCategory(Category category)
        {
            if (CurrentOrder == null || CurrentOrder.Cart == null)
            {
                CreateOkMessage(I18N.DIGITAL_ORDERING_CATEGORIES_CLICK_ERROR);
                LpLogger.LOG_E($"ProceedClickDoCategory() Clicked on a menu category but can't open it! (CurrentOrder == null || CurrentOrder.Cart == null). Showing an error messsage and doing nothing");
                return;
            }

            CurrentOrder.Cart.LastCategoryId = category.Id;

            switch (category.ViewType)
            {
                case CategoryData.CategoryViewType.Popup:
                    LpLogger.LOG_I("ProceedClickDoCategory() - we're having a 'popup' category");
                    CreateDoCategorySelectPositionMessage(category);
                    break;

                default:
                    LpLogger.LOG_I("ProceedClickDoCategory() - we're having a normal category. Proceeding to Frontend.ShowDoCategoryScreen()");
                    Frontend.ShowDoCategoryScreen(category);
                    break;
            }
        }

        public void CreateDoCategorySelectPositionMessage(Category category)
        {
            var categoryName = category.Label.Trim();
            var buttons = new List<ChooserMessageItem>();
            var message = new ChooserMessage(categoryName);

            var positions = category.GetVisibleMenuItems(onlyMenuItems: true).ToList();
            positions.Sort((MenuItem x, MenuItem y) => x.FormattedPriceDecimal.CompareTo(y.FormattedPriceDecimal));

            var isPositionsWithCategoryName = positions.All(p => p.Label.StartsWith(categoryName));
            var minimumPriceInList = positions.
                Select(menuItem => menuItem.FormattedPriceDecimal).
                Min();
            var needToShowRelativePrice = PartnerModule.Instance.IsNeedToShowRelativePrice();

            if (positions.Count == 1)
            {
                Frontend.ShowDoMenuItemScreen(positions.First().Id);
                return;
            }

            foreach (var item in positions)
            {
                var itemName = item.Label.Trim();
                var itemDescription = item.Description?.Trim();

                if (string.IsNullOrEmpty(message.ItemDescription) && !string.IsNullOrEmpty(itemDescription))
                    message.ItemDescription = itemDescription;

                var buttonText = isPositionsWithCategoryName
                    ? DigitalOrderingTexts.FixPositionWithCategoryName(itemName, categoryName)
                    : itemName;

                // APP-5257
                var itemPrice = string.Empty;
                if (needToShowRelativePrice)
                {
                    itemPrice = (item.FormattedPriceDecimal == minimumPriceInList) ?
                        item.FormattedPrice :
                        $"+{Formatter.FormatAmount(item.FormattedPriceDecimal - minimumPriceInList)}";
                }
                else
                    itemPrice = item.FormattedPrice;

                var button = new ChooserMessageItem(item.Image,
                    buttonText,
                    () => Frontend.ShowDoMenuItemScreen(item.Id),
                    itemPrice);
                button.ShowImage = false;
                buttons.Add(button);
            }

            message.Image = category.Image;
            message.Buttons = buttons;
            Frontend.ShowChooserBottomSheetMessage(message);
        }

        //Только для EatIn и Catering (Исключая TableOrdering)
        public bool IsAllowChangeAddress()
        {
            return Engine.Instance.IsFeatureActive(Feature.CHANGE_ADDRESS_CHECKOUT) &&
                    !CurrentOrder.IsDeliveryOrCatering() &&
                    !CurrentOrder.IsCreatedFromQrCode;
        }

        // orders

        [MultiClickProtect]
        public void ShowFullInformationWithoutSync(DOPaidOrder paidOrder)
        {
            Frontend.ShowDoOrderScreen(paidOrder);
        }

        [MultiClickProtect]
        public void ShowOrdersHistory()
        {
            if (!IsDoAvailability())
                return;

            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            Frontend.ShowOrdersHistoryScreen();
        }

        public void ShowAvailabilityInfoHint(AvailabilityInfo info)
        {
            Frontend.ShowAvailabilityInfoHint(info);
        }

        [MultiClickProtect]
        public async Task ShowFullInformation(DOPaidOrder paidOrder)
        {
            if (Engine.Instance.Platform.PlatformNumber == PlatformType.iOS)
            {
                ShowLoadingIndicator(I18N.PLEASE_WAIT);
                await Module.UpdateOrders();
                HideLoadingIndicator();
                Frontend.ShowDoOrderScreen(paidOrder);
            }
            else
            {
                Frontend.ShowDoOrderScreen(paidOrder);
                ShowLoadingIndicator(I18N.PLEASE_WAIT);
                await Module.UpdateOrders();
                HideLoadingIndicator();
            }
        }

        public async Task ApproveOrder(DOPaidOrder order)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return;
            }

            ShowLoadingIndicator(I18N.LOADING);
            var response = await Module.ApproveOrder(order);
            if (!response.Accepted)
            {
                CreateOkMessage(response.Message ?? I18N.SORRY_FUNCTION_UNVAILABLE);
            }

            HideLoadingIndicator();
        }

        // TODO по-хорошему разобраться почему CurrentOrder сюда приходит как null
        public void UpdateAddresFromOutlet()
        {
            if (CurrentOrder == null)
                return;

            try
            {
                if (CurrentOrder.OutletId != 0 && !CurrentOrder.IsDeliveryOrCatering())
                {
                    var outlet = PartnerModule.Instance.Outlets.FirstOrDefault(o => o.Id == CurrentOrder.OutletId);
                    CurrentOrder.Address = outlet.Address;
                }
            }
            catch (Exception ex)
            {
                LpLogger.LOG_W($"UpdateAddresFromOutlet Почему-то кто-то == null", ex);

                ShowMessageWhenProssesIsEmpty();
            }
        }

        private void ShowMessageWhenProssesIsEmpty()
        {
            CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER, () =>
            {
                DigitalOrderingModule.Instance.DeleteAllCreatedOrders();
                ShowMainScreen();
            });
        }

        public void ShowMessageWhenTimeIntervalsForOrderIsEmpty()
        {
            CreateOkMessage(I18N.DO_NO_TIMEINTERVALS, () =>
            {
                DigitalOrderingModule.Instance.DeleteAllCreatedOrders();
                ShowMainScreen();
            });
        }

        public void ShowMessageAboutChangeOrderTime(TimeSpan time)
        {
            var messageText = string.Format(I18N.CHANGE_TIME_FOR_ITEM_WITH_AVAILABILITY_INTERVAL, LpDateTime.TimeToStringCurrentFormat(time));
            ShowPopup(messageText);
        }

        public void ShowCustomCheckoutMessageIfNeeded()
        {
            if (!CurrentOrder.IsDeliveryOrder())
                return;

            var unformattedCustomCheckoutMessage = PartnerModule.Instance.GetCustomMessageShowOnCheckout();
            var customCheckoutMessage = DigitalOrderingTexts.TryToParseMultiLanguageValue(unformattedCustomCheckoutMessage);

            if (!string.IsNullOrWhiteSpace(customCheckoutMessage))
                DigitalOrderingModule.Instance.Controller.CreateOkMessage(customCheckoutMessage);
        }

        public async Task UpdateAndShowMenu()
        {
            try
            {
                if (DoMenuModule.Instance.IsNeedToRefreshTheMenu(CurrentOrder.GetMenu()))
                    ShowLoadingIndicator(I18N.LOADING);

                var menuIsAvailable = await Module.EnsureMenuAsync();

                if (!menuIsAvailable.IsResponseEnsured)
                {
                    if (!menuIsAvailable.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                    return;
                }

                var outletId = string.Empty;
                if (Module.CurrentOrder.IsDeliveryOrCatering())
                    outletId = (Module.CurrentOrder?.CurrentZone as DODistrict)?.AssociatedOutletId.ToString() ?? string.Empty;
                else
                    outletId = Module.CurrentOrder?.OutletId.ToString() ?? string.Empty;

                LpAnalytics.ViewDoMenu(outletId);

                await PrepareAndShowMenu();
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("UpdateAndShowMenu " + e);
            }
            finally
            {
                HideLoadingIndicator();
            }
        }

        private async Task PrepareAndShowMenu()
        {
            CurrentOrder.Cart.DeleteRemovedItems();
            var fillOrderResult = await Module.OrderRepeater.FillOrder();
            if (!fillOrderResult.Success)
            {
                //TODO APP-3412 Необходимо улучшить в будущем
                ShowPopup(fillOrderResult.Reason);
            }

            Module.DownloadImages();
            ShowRootMenu();
        }

        private void ShowRootMenu()
        {
            Category oneCategory = null;
            if (DoMenu.IsOneCategoryInMenu(Module.CurrentMenu.GetRootCategories(), ref oneCategory))
            {
                CurrentOrder.Cart.LastCategoryId = oneCategory.Id;
                Frontend.ShowRootCategory(oneCategory);
            }
            else if (oneCategory != null)
            {
                Frontend.ShowRootMenu(oneCategory.Id);
            }
            else
            {
                Frontend.ShowRootMenu();
            }
        }

        public void OnClickAddMoreItems()
        {
            ShowRootMenu();
        }

        [MultiClickProtect]
        public void OneClickOrder()
        {
            var firstRepeatableOrder = Module.GetFirstRepeatableOrder();
            if (firstRepeatableOrder != null)
            {
                RepeatOrder(firstRepeatableOrder);
                return;
            }

            ShowOrdersHistory();
        }

        public void CallToActionContinueToCreateOrder(string uuid, CallToActionType callToActionType, List<int> availableProcessesIds)
        {
            //if there is a current order being created, ask the user if he wants start a new order or to
            //add stuff to the current order.
            if (Module.CanContinueCurrentOrder())
            {
                CreateClearOrNotActionSheetMessageWhenCTAOrder(uuid, callToActionType, availableProcessesIds);
            }
            else
            {
                _ctaAvailableProcessesIds = availableProcessesIds;
                var firstRepeatableOrder = Module.GetFirstRepeatableOrder();
                if (firstRepeatableOrder != null)
                {
                    ShowCallToActionMessage(uuid, callToActionType);
                    return;
                }
                StartNewDoProcess();
                
                switch (callToActionType)
                {
                    case CallToActionType.Item:
                        _ctaPositionUuidForGettingInDO = uuid;
                        break;
                    case CallToActionType.Category:
                        _ctaMenuCategoryUuid = uuid;
                        break;
                }
            }
        }

        public IList<ChooserActionItem> GetCallToActionItems(CallToActionType callToActionType)
        {
            var items = new List<ChooserActionItem>();
            var orders = Module.GetUniqueRepeatableOrders();
            var processes = Module.GetAvailableProcesses();
            orders = orders
                .Where(order => IsOrderAvailableToRepeat(order, processes))
                .ToList();
            orders = Module.GetThreeRepeatableOrders(orders);

            items = orders.Select(order => new ChooserActionItem(
                order.IsDeliveryOrCatering() ? I18N.CTA_LAST_DELIVERY_ADDRESS : I18N.CTA_LAST_PICKUP_ADDRESS,
                order.GetProcessesDialogName(),
                order.Type,
                async (uuid) =>
                {
                    await CreateNewOrderWithSameAddress(order);

                    switch (callToActionType)
                    {
                        case CallToActionType.Item:
                            _ctaPositionUuidForGettingInDO = uuid;
                            break;
                        case CallToActionType.Category:
                            _ctaMenuCategoryUuid = uuid;
                            break;
                    }
                })).ToList();

            return items;
        }

        /// <summary>
        /// Добавлен враппер. Доп экшн для процессов.
        /// </summary>
        /// <param name="callToActionType">Переданный CTA-type</param>
        /// <returns>Экшн для обновления параметров Digital Ordering</returns>
        public Action<string> GetCTAProcessesAction(CallToActionType callToActionType)
        {
            return (uuid) =>
            {
                switch (callToActionType)
                {
                    case CallToActionType.Item:
                        _ctaPositionUuidForGettingInDO = uuid;
                        break;
                    case CallToActionType.Category:
                        _ctaMenuCategoryUuid = uuid;
                        break;
                }
            };
        }

        public async Task<bool> CreateOrderWithFastOrderType(int outletId, string uuid)
        {
            LpLogger.LOG_I("CreateOrderWithFastOrderType - started");

            ShowLoadingIndicator(I18N.LOADING);

            try
            {
                var updateProcessesResult = await Module.EnsureLoadProcesses();
                if (!updateProcessesResult.IsResponseEnsured)
                {
                    if (!updateProcessesResult.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                DOProcess process = null;
                DOOutlet outlet = null;
                foreach (var zoneByProcess in Module.GetGroupedZoneByProcess(outletId))
                {
                    process = zoneByProcess.Key;
                    outlet = zoneByProcess.FirstOrDefault() as DOOutlet;
                    if (outlet != null && outlet.IsOrderingAvailable())
                    {
                        break;
                    }
                }

                if (outlet == null || process == null)
                {
                    LpLogger.LOG_W($"Fast ordering: Not available outlet {outlet} or process {process}");
                    CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                var available = outlet.IsOrderingAvailable(out var reason);
                if (!available)
                {
                    LpLogger.LOG_W($"Fast ordering to outlet {outlet} is not available");
                    CreateOkMessage(reason);
                    return false;
                }

                Module.DeleteAllCreatedOrders();

                if (!Module.CreateOrder(process, outletId, out var unavailableReason, OrderType.FastOrder))
                {
                    LpLogger.LOG_W($"Fast ordering couldn't create order");
                    CreateOkMessage(unavailableReason);
                    return false;
                }

                var menuAvailable = await Module.EnsureMenuAsync();
                if (!menuAvailable.IsResponseEnsured)
                {
                    if (!menuAvailable.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                var position = CurrentOrder.GetMenu().GetMenuItem(uuid);
                if (position == null)
                {
                    LpLogger.LOG_W($"Fast ordering - position {uuid} isn't available in menu");
                    CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                var cartItem = new CartMenuItem(position);
                var result = await CurrentOrder.Cart.TryAdd(cartItem);
                if (!result.IsSuccess)
                {
                    _ctaPositionUuidForGettingInDO = uuid;
                    await PrepareAndShowMenu();
                    return true;
                }

                CurrentOrder.Cart.AddCartEntity(cartItem);

                return await PerformCheckoutAsync();
            }
            finally
            {
                HideLoadingIndicator();
            }
        }

        public async Task<bool> CreateOrderWithTableNumber(int outletId, string tableNumber)
        {
            LpLogger.LOG_I("CreateOrderWithTableNumber - started");

            ShowLoadingIndicator(I18N.LOADING);

            try
            {
                DOProcess process = null;
                DOOutlet outlet = null;

                var updateProcessesResult = await Module.EnsureLoadProcesses();

                if (!updateProcessesResult.IsResponseEnsured)
                {
                    if (!updateProcessesResult.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                foreach (var zoneByProcess in Module.GetGroupedZoneByProcess(outletId))
                {
                    process = zoneByProcess.Key;
                    if (process.Type == DoProcessType.EatIn)
                    {
                        outlet = zoneByProcess.FirstOrDefault() as DOOutlet;
                        if (outlet != null)
                        {
                            var available = outlet.IsOrderingAvailable(out var reason);
                            if (available)
                            {
                                if (!string.IsNullOrWhiteSpace(reason))
                                {
                                    //we cannot handle order now for some reasons, we need to show warning
                                    CreateOkMessage(reason);
                                }

                                break;
                            }
                        }
                    }
                }

                if (outlet == null || process == null)
                {
                    LpLogger.LOG_W($"TableNumber eat-in: Not available outlet {outlet} or process {process}");
                    CreateOkMessage(I18N.DO_FAST_ORDER_FAIL);
                    return false;
                }

                Module.DeleteAllCreatedOrders();

                if (!Module.CreateOrder(process, outletId, out var unavailableReason))
                {
                    LpLogger.LOG_W($"Qr code couldn't create order");
                    CreateOkMessage(unavailableReason);
                    return false;
                }

                Model.CurrentOrder.OrderType = OrderType.Default;
                Model.CurrentOrder.ScannedTableNumber = tableNumber;
                Model.CurrentOrder.IsCreatedFromQrCode = true;

                var menuAvailable = await Module.EnsureMenuAsync();
                if (!menuAvailable.IsResponseEnsured)
                {
                    if (!menuAvailable.IsConnectionProblemMessageWasShown)
                        // TODO: придумать информативное сообщение:
                        CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                    return false;
                }

                await PrepareAndShowMenu();
                return true;
            }
            finally
            {
                HideLoadingIndicator();
            }
        }

        public async Task<bool> CreateNewOrderWithSameAddress(DOPaidOrder order)
        {
            LpLogger.LOG_I("CreateNewOrderWithSameAddress - started");

            try
            {
                ShowLoadingIndicator(I18N.LOADING);

                var updateProcessesResult = await Module.EnsureLoadProcesses();

                if (!updateProcessesResult.IsResponseEnsured)
                {
                    if (!updateProcessesResult.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER);
                    return false;
                }

                var process = Module.GetAvailableProcesses().FirstOrDefault(p => p.Id == order.ProcessId);
                if (process == null)
                {
                    CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER);
                    return false;
                }

                var updateOutlets = await process.UpdateOutlets();
                if (!updateOutlets)
                {
                    CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER);
                    return false;
                }

                return await SelectProcessToSameAddress(process.Type, order);
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("CreateNewOrderWithSameAddress" + e);
                return false;
            }
            finally
            {
                HideLoadingIndicator();
            }
        }

        private async Task<bool> SelectProcessToSameAddress(DoProcessType processType, DOPaidOrder order, bool isRepeatOrder = false)
        {
            LpLogger.LOG_I("SelectProcessToSameAddress - start");

            if (order.IsDeliveryOrCatering())
            {
                bool selected;
                if (isRepeatOrder)
                    selected = await SelectProcessAsync(processType, order.DeliveryPlace, isRepeatOrder, order.DistrictId);
                else
                    selected = await SelectProcessAsync(processType, order.DeliveryPlace);

                if (selected)
                {
                    CurrentOrder.Address2 = order.AddressApartment;
                    // костыль для мультибренда
                    if (IsNeedToShowMultibrand() && processType == DoProcessType.Delivery)
                    {
                        CurrentOrder.SelectedRegionId = order.DistrictId;
                    }
                }
                return selected;
            }

            return await SelectProcessAsync(processType, order.OutletId);
        }

        [MainThread]
        private void CreateClearOrNotActionSheetMessage()
        {
            LpLogger.LOG_I("CreateClearOrNotActionSheetMessage - started");

            string warningMessageText;
            if (CurrentOrder.IsDeliveryOrCatering())
            {
                warningMessageText = string.Format(I18N.CTA_WARNING_MESSAGE_TEXT_DELIVERY, CurrentOrder.Address);
            }
            else
            {
                var outlet = PartnerModule.Instance.Outlets.FirstOrDefault(o => o.Id == CurrentOrder.OutletId);
                warningMessageText = string.Format(I18N.CTA_WARNING_MESSAGE_TEXT_PICKUP, outlet.Name);
            }

            //You are already have some items in cart. Add them to this order?
            MessagesModule.Instance.CreateActionSheetMessage(warningMessageText,
                new List<BaseChooserMessageItem>()
                {
                    new BaseChooserMessageItem(I18N.CONTINUE_ORDER, async () => {
                        if (IsNeedToShowMultibrand() &&
                            DigitalOrderingModule.Instance.MultibrandDistricts != null)
                        {
                            ShowDoMultibrandScreen(CurrentOrder.DeliveryPlace);
                        }

                        await UpdateAndShowMenu();
                    }),
                    new BaseChooserMessageItem(I18N.CLEAR, () => {
                        StartNewDoProcess();
                    }),
                });
        }

        private void CreateClearOrNotActionSheetMessageWhenCTAOrder(string uuid, CallToActionType ctaType, List<int> availableProcessesIds)
        {
            LpLogger.LOG_I("CreateClearOrNotActionSheetMessageWhenCTAOrder - started");

            string warningMessageText;
            if (CurrentOrder.IsDeliveryOrCatering())
            {
                warningMessageText = string.Format(I18N.CTA_WARNING_MESSAGE_TEXT_DELIVERY, CurrentOrder.Address);
            }
            else
            {
                var outlet = PartnerModule.Instance.Outlets.FirstOrDefault(o => o.Id == CurrentOrder.OutletId);
                warningMessageText = string.Format(I18N.CTA_WARNING_MESSAGE_TEXT_PICKUP, outlet.Name);
            }

            //You are already have some items in cart. Add them to this order?
            MessagesModule.Instance.CreateActionSheetMessage(warningMessageText,
                new List<BaseChooserMessageItem>()
                {
                    new BaseChooserMessageItem(I18N.DONT_CLEAR, async () => {
                        _ctaAvailableProcessesIds = availableProcessesIds;

                        switch (ctaType)
                        {
                            case CallToActionType.Item:
                                _ctaPositionUuidForGettingInDO = uuid;
                                await UpdateAndShowMenu();
                                break;
                            
                            case CallToActionType.Category:
                                _ctaMenuCategoryUuid = uuid;
                                await UpdateAndShowMenu();
                                OnClickedDoCategory(uuid);
                                break;
                        }
                    }),
                    new BaseChooserMessageItem(I18N.CLEAR, () => {
                        _ctaAvailableProcessesIds = availableProcessesIds;
                        StartNewDoProcess();

                        switch (ctaType)
                        {
                            case CallToActionType.Item:
                                _ctaPositionUuidForGettingInDO = uuid;
                                break;
                            case CallToActionType.Category:
                                _ctaMenuCategoryUuid = uuid;
                                break;
                        }
                    }),
                });
        }

        [MultiClickProtect]
        public void SelectProcess(DoProcessType processType, Place place)
        {
            _ = SelectProcessAsync(processType, place);
        }

        [SkipAnalytics]
        public async Task<bool> SelectProcessAsync(DoProcessType processType, Place place, bool isRepeatMultibrandOrder = false, int repeatableMultibrandDistrictId = 0)
        {
            var order = Module.CreateOrder(processType, place, out var unavailableReason, repeatableMultibrandDistrictId);
            if (order)
            {
                if (IsNeedToShowMultibrand() &&
                    processType == DoProcessType.Delivery &&
                    DigitalOrderingModule.Instance.MultibrandDistricts.Count > 1)
                {
                    if (!isRepeatMultibrandOrder)
                    {
                        ShowDoMultibrandScreen(place);
                        return true;
                    }
                }

                await UpdateAndShowMenu();
                return true;
            }

            if (!string.IsNullOrWhiteSpace(unavailableReason))
                CreateOkMessage(unavailableReason);

            return false;
        }

        [MultiClickProtect]
        public void SelectProcess(DoProcessType processType, int outletId)
        {
            _ = SelectProcessAsync(processType, outletId).ConfigureAwait(false);
        }

        [SkipAnalytics]
        private async Task<bool> SelectProcessAsync(DoProcessType processType, int outletId)
        {
            var outlet = Module.GetOutletByIdAndProcessType(processType, outletId, out var process);
            if (outlet == null)
            {
                CreateOkMessage(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA);
                return false;
            }

            async Task<bool> PlaceAnOrder()
            {
                // если у нас смена адреса (на всякий случай еще delivery проверю, хотя должно быть все ок)
                if (CurrentOrder != null && !CurrentOrder.IsDeliveryOrCatering())
                {
                    return await PlaceAnOrderAddressChange(process, outletId);
                }

                var type = (!string.IsNullOrWhiteSpace(_ctaMenuCategoryUuid) || !string.IsNullOrWhiteSpace(_ctaPositionUuidForGettingInDO)) ?
                    OrderType.CallToAction :
                    OrderType.Default;

                if (Module.CreateOrder(process, outletId, out var unavailableReason, type))
                {
                    await UpdateAndShowMenu();
                    return true;
                }
                CreateOkMessage(unavailableReason);
                return false;
            }

            var available = outlet.IsOrderingAvailable(out var reason);
            if (available && !string.IsNullOrWhiteSpace(reason))
            {
                //we cannot handle order now for some reasons, we need to show warning
                CreateTwoButtonsMessage(reason, I18N.MAKE_AN_ORDER, I18N.CANCEL,
                    async () => await PlaceAnOrder());
                return false;
            }
            else if (!available && string.IsNullOrWhiteSpace(reason))
            {
                CreateOneButtonMessage(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA, I18N.OK, () => { });
                return false;
            }
            else if (!available && string.IsNullOrWhiteSpace(reason))
            {
                CreateOneButtonMessage(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA, I18N.OK, () => { });
                return false;
            }
            else
            {
                return await PlaceAnOrder();
            }
        }

        private async Task<bool> PlaceAnOrderAddressChange(DOProcess process, int outletId)
        {
            LpLogger.LOG_I("PlaceAnOrder when address was changed");

            ShowLoadingIndicator(I18N.LOADING);

            var menuId = new MenuIdentificator(outletId, CurrentOrder.ProcessId, CurrentOrder.Process.Type);

            await Module.GetMenuForOutletAsync(menuId);

            var menu = Module.GetMenu(menuId);

            HideLoadingIndicator();

            if (menu == null)
            {
                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                return false;
            }

            var positionTitlesText = "";
            var compareResult = (menu == null) ? MenuCompareResult.CartEmpty : Module.CompareMenu(menu, out positionTitlesText);

            switch (compareResult)
            {
                case MenuCompareResult.PositionMissing:
                    CreateOkCancelMessage(string.Format(I18N.CHANGE_MENU_UNAVAILABLE_POSITIONS, positionTitlesText),
                        async () =>
                        {
                            await ChangeMenuAndCreateNewOrder(menu, process, outletId);
                        }, null);
                    break;
                case MenuCompareResult.CartEmpty:
                    CreateOkCancelMessage(string.Format(I18N.CHANGE_MENU_EMPTY_CART, positionTitlesText),
                        async () =>
                        {
                            await ChangeMenuAndCreateNewOrder(menu, process, outletId);
                        }, null);
                    break;
                case MenuCompareResult.Good:
                    {
                        await ChangeMenuAndCreateNewOrder(menu, process, outletId);
                    }
                    break;
            }

            return true;
        }

        private async Task ChangeMenuAndCreateNewOrder(DoMenu menu, DOProcess process, int outletId)
        {
            // возьмем старые айтемы из корзинки
            var oldItems = Module.GetExistsItems(CurrentOrder.GetMenu());

            // возьмем элементы из нового меню, которые в скрытых категориях 
            var itemsFromDisabledCategories = Module.GetEntitiesFromDisabledCategories(menu, CurrentOrder.Cart.Entities.ToList());

            // удаляем из старой корзины элементы, которые оказались в скрытой категории
            foreach (var hiddenItem in itemsFromDisabledCategories)
            {
                var oldItem = oldItems.FirstOrDefault(o => o.Id == hiddenItem.Id);
                oldItems.Remove(oldItem);
            }

            // обновим меню
            Module.ApplyTargetMenu(menu);

            // создадим новый заказ, если его еще нет. иначе че-то там обновим в нем
            if (!Module.CreateOrder(process, outletId, out var unavailableReason))
            {
                CreateOkMessage(unavailableReason);
                return;
            }

            // почистим корзинку
            CurrentOrder.Cart.ClearCart();

            // заполним корзинку теми позициями, которые у нас из старой корзинки
            // положим их с правильным количеством, модификаторами, ценой и т п
            foreach (var item in oldItems)
            {
                // если ты подарочек
                if (item.MenuItem is GiftPosition giftPosition)
                {
                    var present = menu.MenuItems.Where(p => p is GiftPosition).ToList().
                                       FirstOrDefault(p => (p as GiftPosition).LinkedPresentId == giftPosition.LinkedPresentId);

                    if (present != null)
                    {
                        await AddNewItemInCart(menu, item, giftPosition);
                    }
                }
                // если ты простой
                else
                {
                    var newMenuItem = menu.MenuItems.FirstOrDefault(i => i.Id == item.Id &&
                                i.IsVisible() &&
                                i.IsContainsGroupModifiers(item.MenuItem?.GroupModifiers?.ToList()) &&
                                i.IsContainsCartGroupModifiers(item.CartGroupModifiers));

                    if (newMenuItem != null)
                    {
                        await AddNewItemInCart(menu, item, newMenuItem);
                    }
                }
            }

            // добавим в "путь" страничку с меню
            await UpdateAndShowMenu();

            // если в корзинке что-то есть, то покажем страницу чекаута
            if (CurrentOrder.Cart.Entities.Count > 0)
                ShowDoCartScreen();
        }

        private async Task AddNewItemInCart(DoMenu menu, CartMenuItem item, MenuItem newMenuItem)
        {
            //Получаем модификаторы из нового меню, старые могут отличаться по цене
            var newModifiersGroup = GetModifiersFromMenu(menu, item.CartGroupModifiers);

            var newCartMenuItem = new CartMenuItem(newMenuItem)
            {
                Count = item.Count,
                CartGroupModifiers = newModifiersGroup
            };

            await AddToCart(newCartMenuItem);
        }

        private List<CartGroupModifier> GetModifiersFromMenu(DoMenu menu, List<CartGroupModifier> cartGroupModifiers)
        {
            var modifiersFromMenu = new List<CartGroupModifier>();

            foreach (var oldModifierGroup in cartGroupModifiers)
            {
                var findedModifierInMenu = menu.GetGroupModifier(oldModifierGroup.Id, out string _);

                var cartGroupModifier = new CartGroupModifier(findedModifierInMenu);

                //Обновляем selected состояние новых модификаторов, используя состояние старых модификаторов
                cartGroupModifier.UpdateSelectStateForModifiers(oldModifierGroup.CartModifiers);

                modifiersFromMenu.Add(cartGroupModifier);
            }

            return modifiersFromMenu;
        }

        public IList<ChooserActionItem> GetQuicklyOrderMessageItems(List<DOPaidOrder> repeatableOrders = null)
        {
            var items = new List<ChooserActionItem>();

            var firstRepeatableOrders = repeatableOrders ?? Module.GetThreeRepeatableOrders();
            if (firstRepeatableOrders == null)
            {
                LpLogger.LOG_E("GetQuicklyOrderMessageItems почему-то нет последних доступных для повтора заказов");
                return items;
            }

            items = firstRepeatableOrders.Select(order => new ChooserActionItem(
                order.IsDeliveryOrCatering() ? I18N.CTA_LAST_DELIVERY_ADDRESS : I18N.CTA_LAST_PICKUP_ADDRESS,
                order.GetProcessesDialogName(),
                order.Type,
                null,
                async () =>
                {
                    LpAnalytics.ClickedDoCoupon();
                    await CreateNewOrderWithSameAddress(order);
                }))
                .ToList();
            return items;
        }

        // isNeedToShowProcesses - показать список из процессов (а не сообщение orderNow)
        [MainThread]
        public virtual async Task<bool> Start(bool isNeedToShowProcesses = false)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return false;
            }

            // APP-4088
            if (DigitalOrderingModule.Instance.IsNeedToShowLoadingWhenProcessesUpdating())
                ShowLoadingIndicator(I18N.LOADING);

            // APP-4095
            try
            {
                var result = await Module.EnsureLoadProcesses();

                if (!result.IsResponseEnsured)
                {
                    if (!result.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                    return false;
                }

                //check old order
                if (Module.CanContinueCurrentOrder())
                {
                    if (CurrentOrder.CurrentZone != null && CurrentOrder.CurrentZone.IsOrderingAvailable())
                    {
                        // if мультибренд И дистрикты есть, то добавляем экран выбора рестиков в иерархию экранов
                        if (PartnerModule.Instance.IsNeedToReturnOldFlowForDoCouponClick())
                        {
                            if (IsNeedToShowMultibrand() &&
                                DigitalOrderingModule.Instance.MultibrandDistricts != null)
                            {
                                ShowDoMultibrandScreen(CurrentOrder.DeliveryPlace);
                            }
                            await UpdateAndShowMenu();
                        }
                        else
                        {
                            CreateClearOrNotActionSheetMessage();
                        }

                        return true;
                    }

                    LpLogger.LOG_W($"Current order is not available because of: " +
                                   $"IsOrderingAvailable: {CurrentOrder.CurrentZone?.IsOrderingAvailable()}");
                }

                Module.DeleteAllCreatedOrders();

                // если нет ни одного доступного процесса, то пишем сообщение об этом
                if (!IsProcessesAvailable(out var filteredProcesses))
                    return false;
                
                // если можем получить последние адреса, на которые были последние успешные заказы, то показываем окошко с этими адресами
                var repeatableOrders = Module.GetUniqueRepeatableOrders();

                if (repeatableOrders != null && repeatableOrders.Any() &&
                    !isNeedToShowProcesses &&
                    !PartnerModule.Instance.IsNeedToReturnOldFlowForDoCouponClick())
                {
                    repeatableOrders = repeatableOrders
                        .Where(order => IsOrderAvailableToRepeat(order, filteredProcesses))
                        .ToList();
                    
                    repeatableOrders = Module.GetThreeRepeatableOrders(repeatableOrders) ?? new List<DOPaidOrder>();
                    if (!repeatableOrders.Any())
                    {
                        StartNewDoProcess(true);
                        return true;
                    }

                    // если доступен только 1 процесс и внутри него только 1 тт, то мы не показываем сообщение с выбором адреса
                    // сразу открываем карту с ТТ
                    if (filteredProcesses.Count == 1)
                    {
                        var aloneProcess = filteredProcesses[0];
                        await aloneProcess.UpdateOutlets();

                        // актуально только для пикапа
                        if (aloneProcess.Outlets.Where(o => o.IsAvailable == true).ToList().Count == 1)
                        {
                            ShowDoMapScreen(aloneProcess, false);
                            return true;
                        }
                    }

                    var items = GetQuicklyOrderMessageItems(repeatableOrders);

                    ShowQuicklyOrderToLastAddressMessage(items);
                }
                else
                {
                    LpAnalytics.ClickedDoCoupon();

                    if (Engine.Instance.IsFeatureActive(Feature.MENU_CATALOG_OLD) && filteredProcesses.Count > 0)
                    {
                        var process = filteredProcesses[0];
                        await process.UpdateOutlets();

                        var outlet = process.Outlets.FirstOrDefault(o => o.IsAvailable);
                        if (outlet == null)
                            return false;

                        await SelectProcessAsync(process.Type, outlet.Id);

                        return true;
                    }

                    if (filteredProcesses.Count == 1)
                    {
                        var process = filteredProcesses[0];
                        await process.UpdateOutlets();
                        ShowDoMapScreen(process, false);
                        return true;
                    }

                    ShowDoSelectProcessMessage();
                }
            }
            catch (Exception e)
            {
                LpLogger.LOG_W("Cannot update processes or form", e);
                HideLoadingIndicator();
                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
            }
            finally
            {
                HideLoadingIndicator();
            }
            return true;
        }

        //TODO: Переработать. Но, блин, третий день с процессами. Идей нет
        public virtual async Task<bool> StartForCTA(string uuid, CallToActionType callToActionType)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return false;
            }

            // APP-4088
            if (DigitalOrderingModule.Instance.IsNeedToShowLoadingWhenProcessesUpdating())
                ShowLoadingIndicator(I18N.LOADING);

            // APP-4095
            try
            {
                var result = await Module.EnsureLoadProcesses();

                if (!result.IsResponseEnsured)
                {
                    if (!result.IsConnectionProblemMessageWasShown)
                        CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                    return false;
                }

                //check old order
                if (Module.CanContinueCurrentOrder())
                {
                    if (CurrentOrder.CurrentZone != null && CurrentOrder.CurrentZone.IsOrderingAvailable())
                    {
                        // if мультибренд И дистрикты есть, то добавляем экран выбора рестиков в иерархию экранов
                        if (PartnerModule.Instance.IsNeedToReturnOldFlowForDoCouponClick())
                        {
                            if (IsNeedToShowMultibrand() &&
                                DigitalOrderingModule.Instance.MultibrandDistricts != null)
                            {
                                ShowDoMultibrandScreen(CurrentOrder.DeliveryPlace);
                            }
                            await UpdateAndShowMenu();
                        }
                        else
                        {
                            CreateClearOrNotActionSheetMessage();
                        }

                        return true;
                    }

                    LpLogger.LOG_W($"Current order is not available because of: " +
                                   $"IsOrderingAvailable: {CurrentOrder.CurrentZone?.IsOrderingAvailable()}");
                }

                Module.DeleteAllCreatedOrders();

                // если нет ни одного доступного процесса, то пишем сообщение об этом
                if (!IsProcessesAvailable(out var filteredProcesses))
                    return false;
                
                var process = filteredProcesses[0];
                await process.UpdateOutlets();
                
                var repeatableOrders = Module.GetUniqueRepeatableOrders();

                if (repeatableOrders != null && repeatableOrders.Any())
                {
                    repeatableOrders = repeatableOrders
                        .Where(order => IsOrderAvailableToRepeat(order, filteredProcesses))
                        .ToList();
                    
                    repeatableOrders = Module.GetThreeRepeatableOrders(repeatableOrders) ?? new List<DOPaidOrder>();
                    if (repeatableOrders.Any())
                    {
                        Frontend.ShowCallToActionMessage(uuid, callToActionType);
                        return true;
                    }
                }
                
                if (filteredProcesses.Count == 1)
                {
                    ShowDoMapScreen(process, false);
                    return true;
                }
                Frontend.ShowCallToActionMessage(uuid, callToActionType);
            }
            catch (Exception e)
            {
                LpLogger.LOG_W("Cannot update processes or form", e);
                HideLoadingIndicator();
                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
            }
            finally
            {
                HideLoadingIndicator();
            }
            return true;
        }

        /// <summary>
        /// ТТ “последнего заказа” закрыта и заказы сегодня уже не принимает.
        /// Не показывать данную точку в диалоге
        /// </summary>
        /// <param name="order">Order для проверки</param>
        /// <param name="filteredProcesses">Отфильтрованные (доступные) процессы</param>
        /// <returns><c>True</c>, если точка доступна для заказа</returns>
        private bool IsOrderAvailableToRepeat(DOPaidOrder order, List<DOProcess> filteredProcesses)
        {
            var repeatProcess = filteredProcesses.FirstOrDefault(p => p.Id == order.ProcessId);
            if (repeatProcess == null)
            {
                LpLogger.LOG_I($"{order.OrderId} won't be added to list, because the process is unavailable.");
                return false;
            }

            // если среди точек нет такой точки (смена города) или нет точки, на которую можно сделать заказ (все не принимают заказы)
            if (order.IsDeliveryOrCatering())
            {
                var district = repeatProcess?.Districts?.FirstOrDefault(o => o.Id == order.DistrictId);
                if (district == null || !district.IsOrderingAvailable())
                {
                    LpLogger.LOG_I($"{order.OrderId} won't be added to list, because the district is unavailable.");
                    return false;
                }
            }
            else
            {
                var outlet = repeatProcess?.Outlets?.FirstOrDefault(o => o.Id == order.OutletId);
                if (outlet == null || !outlet.IsOrderingAvailable())
                {
                    LpLogger.LOG_I($"{order.OrderId} won't be added to list, because the OutletId is unavailable.");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Проверяем, доступны ли процессы
        /// Если "нет" - показывает сообщение об этом
        /// </summary>
        /// <param name="availableProcesses">Доступные процессы</param>
        /// <returns><c>True</c>, если процессы доступны</returns>
        private bool IsProcessesAvailable(out List<DOProcess> availableProcesses)
        {
            availableProcesses = Module.GetAvailableProcesses();
            
            if (availableProcesses.Count == 0)
            {
                LpLogger.LOG_W($"Available Processes count {availableProcesses.Count}");
                CreateOkMessage(I18N.DO_NO_PROCESSES_FOR_ORDER);
                return false;
            }

            return true;
        }

        public bool IsNeedToShowAllOfOutletsOnMap()
        {
            return PartnerModule.Instance.IsNeedToShowAllOfOutletsOnMap();
        }

        [MultiClickProtect]
        public void RepeatOrder(DOPaidOrder order)
        {
            _ = RepeatOrderAsync(order);
        }

        // TODO подумать мб не нужен метод, если тут тока фича остается
        public bool IsNeedToShowMultibrand()
        {
            return Engine.Instance.IsFeatureActive(Feature.MULTIBRAND);
        }

        public bool IsNeedToShowOrderStatuses()
        {
            return Engine.Instance.IsFeatureActive(Feature.SHOW_DELIVERY_TRACKING_INFO);
        }

        public async Task RepeatOrderAsync(DOPaidOrder order)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return;
            }

            if (!order.CanRepeatOrder())
            {
                CreateOkMessage(I18N.CANT_REPEAT_LAST_ORDER);
                return;
            }

            ShowLoadingIndicator(I18N.PLEASE_WAIT);
            var result = await Module.EnsureLoadProcesses();
            HideLoadingIndicator();

            if (!result.IsResponseEnsured)
            {
                if (!result.IsConnectionProblemMessageWasShown)
                    CreateOkMessage(I18N.CANT_REPEAT_LAST_ORDER);
                return;
            }

            if (!order.IsRepeatOrderAvailable(out var unavailableReason))
            {
                var text = I18N.CANT_REPEAT_LAST_ORDER +
                           (string.IsNullOrWhiteSpace(unavailableReason) ? "" : "\n" + unavailableReason);
                CreateOkMessage(text);
                return;
            }

            if (!Module.CanContinueCurrentOrder())
            {
                Module.OrderRepeater.SetOrder(order);
                var orderCreated = await ResetCurrentOrderAndMakeNew(order);
                if (orderCreated)
                {
                    ShowDoCartScreen();
                }
            }
            else
            {
                //different process
                if (CurrentOrder.Process.Type != order.Type ||
                    CurrentOrder.Process.Id != order.ProcessId ||
                    (IsNeedToShowMultibrand() && order.IsDeliveryType))
                {
                    //It will remove your current items in the cart. Continue?
                    var buttons = new List<BaseChooserMessageItem>();

                    buttons.Add(new BaseChooserMessageItem(I18N.ORDERS_HISTORY_YES_REMOVE_THEM,
                            async () =>
                            {
                                var orderCreated = await ResetCurrentOrderAndMakeNew(order);
                                if (orderCreated)
                                {
                                    ShowDoCartScreen();
                                }
                            })
                    );

                    MessagesModule.Instance.CreateActionSheetMessage(I18N.ORDERS_HISTORY_REMOVE_CURRENT_ORDER, buttons);
                    return;
                }

                //same process and same outlet or district
                if (CurrentOrder.Process.Id == order.ProcessId)
                {
                    //You are already have some items in cart. Add them to this order?
                    var buttons = new List<BaseChooserMessageItem>();
                    buttons.Add(new BaseChooserMessageItem(I18N.ORDERS_HISTORY_YES_KEEP_THEM, async () =>
                            {
                                if (order.IsDeliveryOrCatering())
                                {
                                    CurrentOrder.DeliveryPlace = order.DeliveryPlace;
                                    CurrentOrder.Address2 = order.AddressApartment;
                                    CurrentOrder.Address = order.AddressBuilding;
                                }
                                else
                                {
                                    CurrentOrder.Address = order.Address;
                                }

                                var fillCartResult = await CurrentOrder.Cart.FillOrdersCartWithPaidOrder(order);
                                if (!fillCartResult.Success)
                                {
                                    CreateOkMessage(fillCartResult.Reason, async () =>
                                    {
                                        await UpdateAndShowMenu();
                                        ShowDoCartScreen();
                                    });
                                }
                                else
                                {
                                    await UpdateAndShowMenu();
                                    ShowDoCartScreen();
                                }
                            }));

                    buttons.Add(new BaseChooserMessageItem(I18N.ORDERS_HISTORY_NO_REMOVE_THEM,
                                async () =>
                                {
                                    var orderCreated = await ResetCurrentOrderAndMakeNew(order);
                                    if (orderCreated)
                                    {
                                        ShowDoCartScreen();
                                    }
                                }));

                    MessagesModule.Instance.CreateActionSheetMessage(I18N.ORDERS_HISTORY_ALREADY_HAVING_ITEMS, buttons);
                    return;
                }
            }
        }

        public void CreateErrorMessageWhenCartIsNotEmptyQr()
        {
            //It will remove your current items in the cart. Continue?
            var buttons = new List<BaseChooserMessageItem>();

            buttons.Add(new BaseChooserMessageItem(I18N.ORDERS_HISTORY_YES_REMOVE_THEM,
                    () =>
                    {
                        Module.DeleteAllCreatedOrders();
                        ShowQrCodeScanner();
                    })
                );

            MessagesModule.Instance.CreateActionSheetMessage(I18N.ORDERS_HISTORY_REMOVE_CURRENT_ORDER, buttons);
        }

        private async Task<bool> ResetCurrentOrderAndMakeNew(DOPaidOrder order)
        {
            Module.DeleteAllCreatedOrders();

            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            var result = await SelectProcessToSameAddress(order.Type, order, isRepeatOrder: true);
            if (!result)
            {
                HideLoadingIndicator();
                return false;
            }

            //fill the cart of the current order
            CurrentOrder.Cart.ClearCart();
            var fillCartResult = await CurrentOrder.Cart.FillOrdersCartWithPaidOrder(order);
            if (!fillCartResult.Success)
            {
                //TODO APP-3412 Необходимо улучшить в будущем
                if (!fillCartResult.Reason.IsNullOrEmpty())
                    ShowPopup(fillCartResult.Reason);
            }
            return true;
        }

        private bool IsNeedToDeleteBankCards()
        {
            if (!PartnerModule.Instance.IsFeatureDeleteBankCards())
            {
                return false;
            }

            var result = PaymentModule.Instance.BankCards.Count > 0;

            if (CurrentOrder.CurrentZone is DODistrict district)
            {
                result &= Module.OutletIdOfLastOrderWithBankCard != district.AssociatedOutletId;
            }
            else
            {
                result &= Module.OutletIdOfLastOrderWithBankCard != CurrentOrder.OutletId;
            }

            return result;
        }

        //[MultiClickProtect]
        public void PerformCheckout()
        {
            // показать сообщение о том что есть недоступные позиции, если надо
            DigitalOrderingModule.Instance.CurrentOrder.ShowUnavailableEntitiesMessageIfNeeded(
                // если заказ ASAP то смотрим текущее время, иначе - OrderTimeFrom
                CurrentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap ? CurrentOrder.CurrentZone.OutletDateTimeNow.TimeOfDay : CurrentOrder.OrderTimeFrom,
                CurrentOrder.OrderDate.DayOfWeek, () =>
                {
                    // почистить корзину и перейти на чекаут, если в корзине больше 1 позиции. иначе оставить чувака в меню
                    if (CurrentOrder.Cart.Entities.Count > 0)
                    {
                        _ = PerformCheckoutAsync();
                    }
                    else
                    {
                        ShowRootMenu();
                    }
                },
                // чел останется на текущей странице
                () => { });
        }

        [SkipAnalytics]
        public async Task<bool> PerformCheckoutAsync()
        {
            var currentZone = CurrentOrder.CurrentZone;
            if (!currentZone.IsOrderingAvailable())
            {
                CreateOkMessage(DigitalOrderingTexts.WeCantAcceptYourOrder(currentZone),
                    () => ShowMainScreen());
                return false;
            }

            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return false;
            }

            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            var totalPrice = CurrentOrder.GetTotal(CurrentOrder.SelectedTipPercent);
            LpAnalytics.TrackCheckout(totalPrice);

            if (CheckAndRemoveExpirationPresents())
            {
                if (CurrentOrder.Cart.Entities.Any())
                {
                    ShowPopup(I18N.EXPIRED_PRESENT_REMOVED);
                }
                else
                {
                    HideLoadingIndicator();
                    DigitalOrderingModule.Instance.DeleteAllCreatedOrders();
                    CreateOkMessage(I18N.DO_PRECHEK_FAIL, ShowMainScreen);
                    return false;
                }
            }

            var isNeedToShowMessageAboutUnavailableItems = DigitalOrderingModule.Instance.ShowMessageAboutUnavailablePositionsIfNeeded(out string messageText);
            if (isNeedToShowMessageAboutUnavailableItems)
            {
                DigitalOrderingModule.Instance.Controller.ShowPopup(messageText);
            }

            //Проверяем заказ, но не очищаем, если есть ошибки с позициями
            //пытаемся удалить недоступные
            var precheck = await GetPrecheckResponse(false, isRunAsFirstTime: true);

            // APP-2849 ограничение. после фикса SetupAsapDateTimeIntervalForDelivery bool не надо будет возвращать
            // часы работы точки настроены таким образом, что точка работает в часы [X ; 00:00], где от X до 00:00 времени <= cookingTime + deliveryTime + startOffset (кейс редкий, только delivery, сейчас не фиксим)
            // делаем перед вторым пречеком
            if (!CurrentOrder.SetupAsapDateTimeIntervalForDelivery())
                return false;

            if (!precheck.IsPrecheckSuccess)
            {
                var isResolve = await ResolveProblemAfterWrongPrecheck(precheck);

                // APP-2849 ограничение. после фикса SetupAsapDateTimeIntervalForDelivery bool не надо будет возвращать
                if (!CurrentOrder.SetupAsapDateTimeIntervalForDelivery())
                    return false;

                if (isResolve == false)
                    return false;
            }

            PaymentModule.Instance.InitPaymentIntent(new DoControllerPaymentObject(this, CurrentOrder));

            var gateSettingsResult = await PaymentModule.Instance.GetGateSettings();
            if (gateSettingsResult)
            {
                if (PaymentModule.Instance.PaymentIntent?.Gate?.Type != Payment.Model.GateType.Pesapal
                    && PaymentModule.Instance.PaymentIntent?.Gate?.Type != Payment.Model.GateType.VivaWallet)
                {
                    var result = await PaymentModule.Instance.GetBankCards();
                    if (!result)
                    {
                        LpLogger.LOG_E("Client hasn't updated bank cards");
                    }
                }

                if (IsNeedToDeleteBankCards())
                {
                    var deletedSuccess = await PaymentModule.Instance.DeleteAllBankCards();
                    if (!deletedSuccess)
                    {
                        HideLoadingIndicator();
                        CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                        return false;
                    }
                }

                await PaymentModule.Instance.CheckCardsVerification();

                PaymentModule.Instance.PaymentIntent.CheckLastPaymentMethod();
                HideLoadingIndicator();

                if (CurrentOrder.OrderType == OrderType.FastOrder)
                    Frontend.ShowSubscriptionCheckoutScreen();
                else
                {
                    var items = Module.GetFormattedDictionaryForAnalytics(CurrentOrder.Cart.Entities);
                    var paymentType = PaymentModule.Instance.PaymentIntent.PaymentMethod.PaymentMethodType
                        .GetStringRepresentation();
                    var taxValue = (double)CurrentOrder.Tax;
                    var shippingCost = (double)CurrentOrder.GetDeliveryFee();
                    
                    LpAnalytics.ViewCartEvent(items, totalPrice);
                    LpAnalytics.BeginCheckoutEvent(items, totalPrice, paymentType, taxValue, shippingCost);
                    
                    _needToShowMinSumForOrderDialogNextTime = true;
                    Frontend.ShowCheckoutScreen();
                }

                if (CurrentOrder.IsNeedToGetAsapTimeFromServer() && !Module.IsClientAndOutletInSameTimezone(out var otletUtcOffsetMilliseconds))
                {
                    var dialogText = string.Format(I18N.DIFFERENT_TIMEZONES_MESSAGE, Module.GetFormattedUtcOffset(otletUtcOffsetMilliseconds));
                    if (Engine.Instance.Platform.PlatformNumber == PlatformType.iOS)
                        CreateOkMessage(dialogText);
                    else
                        MessagesModule.Instance.CreatePostponedMessage(dialogText, string.Empty, I18N.OK);
                }
                
                return true;
            }

            CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
            HideLoadingIndicator();
            return false;
        }

        private bool CheckAndRemoveExpirationPresents()
        {
            var gifts = CurrentOrder.Cart.Entities.Where(e => e.IsGift).ToList();

            bool isRemovedExpiredPresents = false;
            foreach (var gift in gifts)
            {
                var giftItem = gift.MenuItem as GiftPosition;

                if (giftItem.LinkedPresent.IsPresentExpired())
                {
                    isRemovedExpiredPresents = true;
                    CurrentOrder.Cart.RemoveCartEntity(gift);
                }
            }

            return isRemovedExpiredPresents;
        }

        public async Task<bool> ResolveProblemAfterWrongPrecheck(PrecheckResult precheck)
        {
            if (CurrentOrder == null)
            {
                LpLogger.LOG_W("Order is null");
                CreateOkMessage(I18N.DO_PRECHEK_FAIL, ShowMainScreen);
                return false;
            }

            var deletedItemsNames = CurrentOrder.Cart.DeletePrecheckRejectedItems(precheck.RejectedItemsIds);

            //Кажется при таких условиях возникает ситуация когда изменили цену в меню
            //Удалить не получилось, но в корзине элементы есть
            if (precheck.RejectedItemsIds?.Any(x => x == null) == true && CurrentOrder.Cart.ItemCount > 0)
            {
                CurrentOrder.Cart.ClearCart();
            }

            if (CurrentOrder.Cart.ItemCount == 0)
            {
                LpLogger.LOG_W("Cart is empty after clean");
                CreateOkMessage(I18N.DO_PRECHEK_FAIL, ShowMainScreen);
                return false;
            }

            ShowLoadingIndicator(I18N.PLEASE_WAIT);
            await Task.Delay(5 * 1000);
            //Проверяем еще раз после удаления
            var secondPrecheck = await GetPrecheckResponse(isRunAsFirstTime: true, isSecondPrecheck: true);
            HideLoadingIndicator();
            precheck.IsPrecheckSuccess = secondPrecheck.IsPrecheckSuccess;
            precheck.RejectedItemsIds = secondPrecheck.RejectedItemsIds;

            if (!precheck.IsPrecheckSuccess)
            {
                LpLogger.LOG_W("Precheck not pass 2 times");
                CreateOkMessage(I18N.DO_PRECHEK_FAIL);
                return false;
            }

            ShowPopup(DigitalOrderingTexts.OrdersHistorySomeItemsUnavailable(deletedItemsNames));

            return true;
        }

        private string GetMinSumMessage(decimal minOrderSum)
        {
            return PartnerModule.Instance.IsNeedToPayForPresents() ?
                  string.Format(I18N.DELIVERY_UNVAILABLE_MINIMAL_ORDER_WITHOUT_PRESENTS,
                      Formatter.FormatAmount(minOrderSum)) :
                  string.Format(I18N.DELIVERY_UNVAILABLE_MINIMAL_ORDER,
                      Formatter.FormatAmount(minOrderSum));
        }

        //isPrecheckSuccess - результат проверки успех/неуспех,
        //rejectedItemsIds - ID элементов с ошибками валидации
        public async Task<PrecheckResult> GetPrecheckResponse(bool isDeleteAllCreatedOrders = true, bool isRunAsFirstTime = false, bool isSecondPrecheck = false)
        {
            var rejectedItemsIds = new List<string>();
            var preCheckResult = await Module.DoPreCheck();
            if (!preCheckResult.Ok)
            {
                HideLoadingIndicator();

                if (!isSecondPrecheck)
                {
                    if (preCheckResult.IsNoInternetExceptionThrown())
                        CreateOkMessage(I18N.NO_INTERNET);

                    else if (preCheckResult.IsLpServerNotReachable())
                        Module.CreateMessageForConnectionProblems();

                    else
                        CreateOkMessage(I18N.DO_PRECHECK_TIMEOUT);
                }

                return new PrecheckResult(false, null);
            }

            if (!preCheckResult.Accepted)
            {
                if (preCheckResult.ItemIds.Any())
                    rejectedItemsIds = preCheckResult.ItemIds;

                if (preCheckResult.IsDOUnavailable)
                {
                    rejectedItemsIds = rejectedItemsIds.
                        Where(id => id != null).
                        ToList();
                }
                else if (isDeleteAllCreatedOrders)
                    Module.DeleteAllCreatedOrders();

                HideLoadingIndicator();
                //TODO Найти хорошее решение APP-3469
                //CreateOkMessage(I18N.DO_PRECHEK_FAIL, ShowMainScreen);
                return new PrecheckResult(false, rejectedItemsIds);
            }

            CurrentOrder.Tax = preCheckResult.Tax;
            CurrentOrder.PreCheckDeliveryFee = preCheckResult.DeliveryFee;
            CurrentOrder.PreCheckServiceFee = preCheckResult.ServiceFee;
            CurrentOrder.Discount = preCheckResult.Discount;

            var zone = DigitalOrderingModule.Instance.CurrentOrder.CurrentZone as DODistrict;
            if (zone != null && zone.ExternalVendor != null &&
                zone.ExternalVendor is DoPostmatesVendor doPostmatesVendor)
            {
                doPostmatesVendor.ExecutionTimeDuration = preCheckResult.VendorDuration;
            }

            if (CurrentOrder.IsNeedToGetAsapTimeFromServer())
            {
                var result = await GetDeliveryFeeOrderingTimeResponse(isRunAsFirstTime);
                return new PrecheckResult(result.isSuccessResponse, null, result.IsOrderTimeChange);
            }

            return new PrecheckResult(true, null);
        }

        public async Task<(bool isSuccessResponse, bool IsOrderTimeChange)> GetDeliveryFeeOrderingTimeResponse(bool isRunAsFirstTime = false, bool isInitTimerAndroid = false)
        {
            var deliveryFeeOrderingTimeResult = await Module.DeliveryFeeOrderingTime(isRunAsFirstTime, isInitTimerAndroid);
            var deliveryFeeOrderingTimeResponse = deliveryFeeOrderingTimeResult.Response;

            if (!deliveryFeeOrderingTimeResponse.IsValidCheckoutPeriod)
            {
                return (false, deliveryFeeOrderingTimeResult.IsOrderTimeChange);
            }

            if (!deliveryFeeOrderingTimeResponse.Ok)
            {
                LpLogger.LOG_W("deliveryFeeOrderingTimeResponse.Ok = false");
                HideLoadingIndicator();
                CreateOkMessage(I18N.DO_PRECHECK_TIMEOUT);
                return (false, deliveryFeeOrderingTimeResult.IsOrderTimeChange);
            }

            var minAvailableTime = deliveryFeeOrderingTimeResponse.MinimalExecutionTime;
            if (minAvailableTime != null)
            {
                if (CurrentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap)
                {
                    CurrentOrder.AsapTimeFromServer = minAvailableTime;
                }
                else if (CurrentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Custom)
                {
                    CurrentOrder.OrderDate = minAvailableTime.Date;
                    CurrentOrder.OrderTimeFrom = minAvailableTime.TimeOfDay;
                }
            }
            else
            {
                CurrentOrder.AsapTimeFromServer = CurrentOrder.CurrentZone.GetAsap(true);
            }

            CurrentOrder.MinimalOrderDate = deliveryFeeOrderingTimeResponse.MinimalCheckoutTime;
            CurrentOrder.MaximalOrderDate = deliveryFeeOrderingTimeResponse.MaximalCheckoutTime;

            if (CurrentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap)
            {
                CurrentOrder.OrderDate = CurrentOrder.AsapTimeFromServer.Date;
                CurrentOrder.OrderTimeFrom = CurrentOrder.AsapTimeFromServer.TimeOfDay;

                CurrentOrder.SetupAsapDateTimeIntervalForDelivery();
            }

            return (true, deliveryFeeOrderingTimeResult.IsOrderTimeChange);
        }

        [SkipAnalytics]
        public CheckoutStructure GetCheckoutStructure()
        {
            return new CheckoutStructure(CurrentOrder);
        }

        public bool SetDateTimeAndShowErrorMessageIfNeeded(DateTime date, TimeSpan time)
        {
            var result = CurrentOrder.SetDateTimeWithErrorReason(date, time, out var reason);
            if (!result)
            {
                if (CurrentOrder.IsNeedToGetAsapTimeFromServer())
                    MessagesModule.Instance.CreateOkMessage(reason);
                else
                    ShowPopup(reason);
            }
            return result;
        }

        public bool SetDate(DateTime value)
        {
            return SetDateTimeAndShowErrorMessageIfNeeded(value, CurrentOrder.OrderTimeFrom);
        }

        // удивительно, но используется только в android
        public bool SetTime(TimeSpan value)
        {
            return SetDateTimeAndShowErrorMessageIfNeeded(CurrentOrder.OrderDate, value);
        }

        // isApplePay - это ужасный костыль, за который нме очень стыдно, но я не знаю, как лучше
        // А еще на меня давят "быстрее, оно горит" :(
        // сейчас с applePay у нас начинает выполняться MakePayment, показывается крутяшка, потом вызывается PaymentAuthorizationViewControllerDidFinish (выход с окошечка applePay) где скрывается крутяшка. Получается, что makePayment не выполнился до конца, а крутяшка скрыалсь.
        [MultiClickProtect]
        public void Pay(bool isApplePay = false, IErrorPaymentCallback errorPaymentCallback = null)
        {
            var items = Module.GetFormattedDictionaryForAnalytics(CurrentOrder.Cart.Entities);
            var totalPrice = CurrentOrder.GetTotal(CurrentOrder.SelectedTipPercent);
            var paymentType = PaymentModule.Instance.PaymentIntent.PaymentMethod.PaymentMethodType
                .GetStringRepresentation();
            
            LpAnalytics.AddShippingInfoEvent(items, totalPrice);
            LpAnalytics.AddPaymentInfoEvent(items, totalPrice, paymentType);
            
            _ = PayAsync(isApplePay, errorPaymentCallback);
        }

        [SkipAnalytics]
        public async Task<bool> PayAsync(bool isApplePay = false, IErrorPaymentCallback errorPaymentCallback = null)
        {
            // APP-3943
            if (PaymentModule.Instance.IsNeedToMakeOrdersRequest)
            {
                PaymentModule.Instance.IsNeedToMakeOrdersRequest = false;

                await DigitalOrderingModule.Instance.UpdateOrders(false);

                // если заказ с таким orderId как в CurrentOrder приходит, тогда return
                var isOrdersContainsCurrentOrder = DigitalOrderingModule.Instance.Orders.FirstOrDefault(o => o.OrderId == CurrentOrder.OrderId && o.OrderStatus.IsNotFailed()) != null;
                if (isOrdersContainsCurrentOrder)
                {
                    // кинуть человека на SuccessScreen
                    await PaymentModule.Instance.PaymentIntent.PaymentObject.OnMakePaymentCompleted();

                    return false;
                }

                CurrentOrder.ResetUniqueOrderQuoteGuid();
            }

            var isPayAvailable = await IsPayAvailable(isApplePay);
            var payResult = isPayAvailable && await PaymentModule.Instance.PaymentIntent.Pay(errorPaymentCallback: errorPaymentCallback);

            if (!payResult)
            {
                CurrentOrder.ResetUniqueOrderQuoteGuid();
            }

            return payResult;
        }

        [SkipAnalytics]
        public async Task<bool> IsPayAvailable(bool isApplePay = false)
        {
            if (Engine.Instance.Platform.NoInternetConnection)
            {
                CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
                return false;
            }

            if (!DigitalOrderingModule.Instance.Controller.CreateCampaighGiftUnavailableDialogIfNeeded(
                positiveAction: () => DigitalOrderingModule.Instance.Controller.ValidateMinSumForOrder(true)))
                return false;

            if (!DigitalOrderingModule.Instance.Controller.ValidateMinSumForOrder(true))
                return false;

            if (CurrentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap)
            {
                CurrentOrder.SetAsapTime();
            }

            if (!CurrentOrder.CurrentZone.IsOrderingAvailable())
            {
                CreateOkMessage(DigitalOrderingTexts.WeCantAcceptYourOrder(CurrentOrder.CurrentZone),
                    () => ShowMainScreen());
                return false;
            }

            if (!CurrentOrder.IsValidDateTime(CurrentOrder.OrderDate, CurrentOrder.OrderTimeFrom, out var reason))
            {
                ShowPopup(reason);
                return false;
            }

            if (!LoyaltyModule.Instance.IsPointsEnough(CurrentOrder.Cart.GetPointsSummary()))
            {
                ShowPopup(I18N.ORDERING_NOT_ENOUGH_POINTS);
                return false;
            }

            var isAddressApartmentWrong = false;
            if (!PartnerModule.Instance.IsHideApartmentField())
            {
                isAddressApartmentWrong = !CurrentOrder.CheckAddressApartment(out var addressApartmentErrorText);
                if (isAddressApartmentWrong)
                {
                    ShowAddressApartmentError(addressApartmentErrorText);
                }
            }

            var isDoFormWrong = !CurrentOrder.ValidateForm(out reason);
            if (isDoFormWrong)
            {
                ShowPopup(reason);
            }

            if (isDoFormWrong || isAddressApartmentWrong)
            {
                return false;
            }

            SurveysModule.Instance.SaveSurvey(CurrentOrder.Process.Form);
            Module.Save();
            await UserModule.Instance.PrepareUserDataToSaveIfNeeded();

            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            if (!PaymentModule.Instance.Controller.EnsureSelectedPaymentMethod())
            {
                HideLoadingIndicator();
                return false;
            }

            if (!PaymentModule.Instance.Controller.EnsureCardAdded())
            {
                await Module.UpdateUserDataBeforePayment();
                HideLoadingIndicator();
                return false;
            }

            // VivaWallet doesn't require to add bank card so EnsureCardAdded() returns `true` without calling
            // OnAddCard(), but we still need to check if the necessary fields have values and send user data
            // before opening payment webview
            if (PaymentModule.Instance.Controller.PaymentIntent.Gate.Type is GateType.VivaWallet)
            {
                if (!VivaWallet.HasRequiredUserInfo())
                {
                    HideLoadingIndicator();
                    PaymentModule.Instance.Controller.CreateOkMessage(I18N.REQUIRED_USER_INFO_TO_ADD_BANK_CARD);
                    return false;
                }
                await Module.UpdateUserDataBeforePayment();
            }

            if (isApplePay)
                HideLoadingIndicator();

            return true;
        }

        public void ShowStatusScreen(DOPaidOrder paidOrder)
        {
            var commonAction = new CommonAction
            {
                Id = paidOrder.OrderId,
                Header = paidOrder.IsApprovable() ? "" : paidOrder.GetTextToShowInsteadOrderNumber(),
                Description = paidOrder.IsApprovable() ? I18N.ORDER_WAS_PAID_SUCCESSFULLY_HINT : paidOrder.GetSuccessMessage(),
            };

            if (paidOrder.OrderType == OrderType.FastOrder)
                Frontend.ShowSubscriptionStatusScreen(commonAction);
            else
                Frontend.ShowStatusScreen(commonAction);
        }

        private void ShowAddressApartmentError(string errorText)
        {
            Frontend.ShowAddressApartmentError(errorText);
        }

        public void ShowPresentRedeemSelectionMessage(Present present)
        {
            Frontend.ShowPresentRedeemSelectionMessage(present);
        }

        private bool IsDoAvailability()
        {
            if (!Module.Enabled)
            {
                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);
                return false;
            }

            return true;
        }

        // regularly start do via Coupon
        public void OnClickCard()
        {
            _ = Task.Run(async () =>
            {
                var result = await Start();
                if (result)
                {
                    _ctaPositionUuidForGettingInDO = null;
                    _ctaMenuCategoryUuid = null;
                    _ctaAvailableProcessesIds = null;
                    _presentIdForGettingInDO = 0;
                }
            });
        }

        public bool ValidateMinSumForOrder(bool forceValidationDialog = false)
        {
            if (CurrentOrder == null || !(CurrentOrder.Cart?.Entities?.Any() ?? false))
                return false;
            
            var minSumForOrder = CurrentOrder.GetSumForOrder();
            var actualMinSumForOrder = Math.Max(CurrentOrder.CurrentZone.MinimalOrderSum, minSumForOrder);
            var isOrderSumLessThenMinSum = actualMinSumForOrder == minSumForOrder;
            var totalWithoutFeesAndTips = CurrentOrder.GetTotalWithoutFeesAndsTips();

            if (totalWithoutFeesAndTips >= actualMinSumForOrder)
            {
                _needToShowMinSumForOrderDialogNextTime = true;
                return true;
            }

            if (!_needToShowMinSumForOrderDialogNextTime && !forceValidationDialog)
                return false;

            string messageForDialog;
            
            if (isOrderSumLessThenMinSum)
            {
                var presentsThatCantBeAdded = CurrentOrder.GetPresentsForDelete(totalWithoutFeesAndTips);
                var positionTitlesStringList = presentsThatCantBeAdded.Select(entity => entity.Label).ToList();
                var positionTitlesString = string.Join(", ", positionTitlesStringList);
                   
                var textForMessageTitle = positionTitlesStringList.Count > 1
                    ? I18N.MIN_ORDER_AMOUNT_FOR_SEVERAL_PRESENTS_MESSAGE
                    : I18N.MIN_ORDER_AMOUNT_FOR_SINGLE_PRESENT_MESSAGE;
                
                messageForDialog = string.Format(textForMessageTitle, positionTitlesString, Formatter.FormatAmount(actualMinSumForOrder));
            }
            else
                messageForDialog = GetMinSumMessage(actualMinSumForOrder);

            CreateTwoButtonsMessage(messageForDialog,
                positiveButtonText: I18N.OK,
                negativeButtonText: I18N.ADD_MORE_ITEMS,
                negativeButtonAction: async () =>
                {
                    await DigitalOrderingModule.Instance.Controller.UpdateAndShowMenu();
                });
            
            _needToShowMinSumForOrderDialogNextTime = false;
            return false;
        }

        public void CreateMakeOrderInitialAction(InitialMakeOrderActionModel actionModel, QrOrderSource orderSource)
        {
            var initialAction = new Action(async () =>
            {
                LpLogger.LOG_I("DigitalOrderingModule.MainScreenInitialAction execution started.");
                
                LpAnalytics.TrackQrOrder(actionModel.OutletId?.ToString() ?? string.Empty, orderSource.ToString());
                try
                {
                    var doModule = DigitalOrderingModule.Instance;
                    if (doModule.Processes == null
                    || (doModule.Processes.FirstOrDefault(process => process.Type == actionModel.ProcessType)?.Form == null)
                    || doModule.IsNeedToShowLoadingWhenProcessesUpdating())
                    {
                        ShowLoadingIndicator(I18N.PLEASE_WAIT);

                        var processesResult = await doModule.EnsureLoadProcesses();

                        if (!processesResult.IsResponseEnsured)
                        {
                            if (!processesResult.IsConnectionProblemMessageWasShown)
                                CreateOkMessage(I18N.SORRY_FUNCTION_UNVAILABLE);

                            LpLogger.LOG_I("DigitalOrderingModule.CreateMakeOrderInitialAction(): Couldn't get processes. Can't create new order.");
                            return;
                        }
                    }

                    if (!IsProcessesAvailable(out var filteredProcesses))
                        return;

                    var process = doModule.Processes.FirstOrDefault(process => process.Type == actionModel.ProcessType);
                    if (process == null)
                    {
                        LpLogger.LOG_I($"DigitalOrderingModule.CreateMakeOrderInitialAction(): Processes don't contain type {actionModel.ProcessType}. Can't create new order.");
                        return;
                    }

                    if (actionModel.ProcessType.IsDeliveryOrCatering())
                    {
                        // TODO: Create Delivery Order
                    }
                    else
                    {
                        // Create Pickup Order
                        int oultetId;

                        if (actionModel.OutletId != null)
                            oultetId = (int)actionModel.OutletId;
                        else
                        {
                            LpLogger.LOG_I("DigitalOrderingModule.CreateMakeOrderInitialAction(): Didn't receive OutletId for PickUp/EatIn order. Can't create new order.");
                            return;
                        }

                        doModule.DeleteAllCreatedOrders();

                        if (!doModule.CreateOrder(process, oultetId, out var unavailableReason))
                        {
                            LpLogger.LOG_W($"DigitalOrderingModule.CreateMakeOrderInitialAction(): Couldn't create order");
                            CreateOkMessage(unavailableReason);
                            return;
                        }
                        Model.CurrentOrder.OrderType = OrderType.Default;
                        Model.CurrentOrder.IsCreatedFromQrLanding = true;

                        if (actionModel.TableNumber != null && Model.CurrentOrder != null)
                            Model.CurrentOrder.ScannedTableNumber = actionModel.TableNumber;

                        await UpdateAndShowMenu();
                    }
                }
                catch (Exception e)
                {
                    LpLogger.LOG_E($"DigitalOrderingModule.MainScreenInitialAction execution failed with exception: {e.Message}");
                }
                finally
                {
                    HideLoadingIndicator();
                    LpLogger.LOG_W($"DigitalOrderingModule.MainScreenInitialAction execution finished.");
                }
            });

            var backendModule = Engine.Instance.GetModule<IProceedHandler>();
            backendModule?.ScheduleMainScreenInitialAction(new InitialActionModel(InitialActionType.MakeOrder, initialAction));
        }

        /// <summary>
        /// Проверяем на предмет купона по кампании.
        /// Если условия для него НЕ выполнены - показывается диалог
        /// </summary>
        /// <param name="okAction"></param>
        /// <returns><c>True</c>, если все условиям выполнены</returns>
        public bool CampaighItemNotAvailableDialog(Action okAction = null)
        {
            var isValid = IsCampaighItemAvailableToOrder();

            if (!isValid)
            {
                CreateTwoButtonsMessage(I18N.HACKED_PJ_CAMPAIGH_ERROR_MESSGE,
                    positiveButtonText: I18N.OK,
                    positiveButtonAction: okAction,
                    negativeButtonText: I18N.ADD_MORE_ITEMS,
                    negativeButtonAction: async () =>
                    {
                        await DigitalOrderingModule.Instance.Controller.UpdateAndShowMenu();
                    });
            }
            return isValid;
        }

        public bool IsCampaighItemAvailableToOrder()
        {
            if (CurrentOrder == null || !(CurrentOrder.Cart?.Entities?.Any() ?? false))
                return true;

            var campaighItem = CurrentOrder.Cart.Entities.
                Where(item => item.IsGift).
                Select(menuItem => menuItem.MenuItem as GiftPosition).
                FirstOrDefault(gift => gift.IsCampaighPosition);
            if (campaighItem == null)
                return true;

            var currentIds = CurrentOrder.Cart.Entities.
                Select(entity => entity.Id).
                ToList();
            var groups = campaighItem.RequiredItems.GroupBy(item => currentIds.Contains(item));
            var notAddedItems = new List<string>();
            var addedItems = new List<string>();
            foreach (var group in groups)
            {
                if (group.Key)
                    addedItems = group.ToList();
                else
                    notAddedItems = group.ToList();
            }

            return addedItems.Any();
        }

        public bool IsCampaighItemAvailableToOrder(GiftPosition campaighItem, string removedItemId = null)
        {
            if (campaighItem == null)
                return true;

            var currentIds = CurrentOrder.Cart.Entities.
                Select(entity => entity.Id).
                ToList();

            if (removedItemId != null)
                currentIds.Remove(removedItemId);

            var groups = campaighItem.RequiredItems.GroupBy(item => currentIds.Contains(item));
            var notAddedItems = new List<string>();
            var addedItems = new List<string>();
            foreach (var group in groups)
            {
                if (group.Key)
                    addedItems = group.ToList();
                else
                    notAddedItems = group.ToList();
            }

            return addedItems.Any();
        }

        #region MultiClickProtect Region
        [MultiClickProtect]
        private void ShowDoSelectProcessMessage() =>
            Frontend.ShowDoSelectProcessMessage();

        [MultiClickProtect]
        private void ShowQuicklyOrderToLastAddressMessage(IList<ChooserActionItem> chooserActionItems) =>
            Frontend.ShowQuicklyOrderToLastAddressMessage(chooserActionItems);

        [MultiClickProtect]
        private void ShowCallToActionMessage(string uuid, CallToActionType callToActionType)
        {
            Task.Run(async () => await StartForCTA(uuid, callToActionType));
        }
        #endregion
    }
}

