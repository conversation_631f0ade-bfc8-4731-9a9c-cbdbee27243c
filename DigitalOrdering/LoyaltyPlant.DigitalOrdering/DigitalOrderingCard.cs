using System;
using System.Xml.Linq;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Images;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.DigitalOrdering
{
    [Serializable]
	public class DigitalOrderingCard : Card
	{
        public DigitalOrderingCard(int id, long version, string previewText, int sectionId, long priority, string imageUrl)
            : base(id, version, previewText, sectionId, priority, imageUrl)
        {
        }

        public static DigitalOrderingCard Create(XElement element)
        {
            var card = new DigitalOrderingCard(Convert.ToInt32(element.GetAttributeValue("card-id")),
                  long.Parse(element.GetAttributeValue("version")),
                  element.GetCData("previewtext"),
                  Convert.ToInt32(element.GetAttributeValue("section-id")),
                  Convert.ToInt32(element.GetAttributeValueTag("priority")),
                  element.GetAttributeValue("image-url-part"));
            return card;
        }

	    public override string ActionButtonText => I18N.MENU_SLIDESHOW_SECTION_ACTION_TEXT;

        public override string GetCardTypeString()
        {
            return "menu";
        }
        
        public override bool IsSpecial => true;
        
        public override void OnClicked()
        {
            DigitalOrderingModule.Instance.Controller?.OnClickCard();
        }
    }
}
