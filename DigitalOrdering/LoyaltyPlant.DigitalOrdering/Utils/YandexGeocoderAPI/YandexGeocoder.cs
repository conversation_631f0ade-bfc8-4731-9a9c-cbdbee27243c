using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.Languages;

namespace LoyaltyPlant.DigitalOrdering.Utils.YandexGeocoderAPI
{
    [Serializable]
    public class YandexGeocoder : IReverseGeocoder, IDirectGeocoder
    {
        private int PartnerId => Engine.Instance.Configuration.PartnerId;

        private string ApiKey
        {
            get
            {
                switch (PartnerId)
                {
                    // PJ UZ
                    case 3252:
                        return "b5632d3d-4b3e-4ce6-8547-620f6fd88ab0";

                    // PJ KG & PJ KZ
                    case 3262:
                    case 3263:
                        return "4e82ffe2-b721-4982-8c20-e9d12c79941e";
                    
                    default:
                        return "e7b68af5-50fc-4527-88fa-91020aa38c97";
                }
            }
        }

        private string MaskedApi => GetMaskedApi(ApiKey);
        private string _locale => GetLocale();
        
        // IReverseGeocoder method
        public async Task<Place> GetPlaceFromCoordinates(double latitude, double longitude)
        {
            LpLogger.LOG_I("YandexGeocoder: trying to get address from coordinates.");

            try
            {
                LpLogger.LOG_I($"Sending geocoder request with API Key {MaskedApi}");
                var request = new YandexGeocoderRequest(ApiKey, latitude, longitude, _locale);
                var response = await new WebTask<YandexGeocoderResponse>().ExecuteAsync(request);

                if (!response.Ok)
                {
                    var contentError = response.Content;
                    LpLogger.LOG_W($"Yandex geocoder response returned with status code [{response.StatusCode}]:\n{request.Url}\n{contentError}");
                    return null;
                }

                if (response.YandexPlaces.IsNullOrEmpty())
                {
                    LpLogger.LOG_I($"Yandex geocoder response returned with no places. Content:\n{response.Content}");
                    return null;
                }

                var parsedPlace = response.YandexPlaces.First();
                
                if (parsedPlace.Longitude == double.MinValue)
                    parsedPlace.Longitude = longitude;
                
                if (parsedPlace.Latitude == double.MinValue)
                    parsedPlace.Latitude = longitude;
                
                LpLogger.LOG_I("YandexGeocoder: successfully got address from coordinates.");
                return parsedPlace;
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E($"YandexGeocoder request failed caught Exception: {ex.Message}");
                return null;
            }
        }
        
        // IDirectGeocoder methods
        public async Task<HashSet<Place>> GetPlacesFromAddress(string searchInputAddress)
        { 
            LpLogger.LOG_I("YandexGeocoder: trying to get Places set from address search field input.");
            try
            { 
                LpLogger.LOG_I($"Sending geocoder request with API Key {MaskedApi}");
                var request = new YandexGeocoderRequest(ApiKey, searchInputAddress, 5, _locale);
                var response = await new WebTask<YandexGeocoderResponse>().ExecuteAsync(request);

                if (!response.Ok)
                {
                    var contentError = response.Content;
                    LpLogger.LOG_W($"Yandex geocoder response returned with status code [{response.StatusCode}]:\n{request.Url}\n{contentError}");
                    return null;
                }

                var places = response.YandexPlaces;
                
                if (places.IsNullOrEmpty())
                {
                    LpLogger.LOG_I($"Yandex geocoder response returned with no places. Content:\n{response.Content}");
                    return null;
                }
                
                LpLogger.LOG_I("YandexGeocoder: successfully got Places set from address search field input.");
                return response.YandexPlaces.IsNullOrEmpty() ? null : new HashSet<Place>(response.YandexPlaces.Where(place => !place.Address.IsNullOrEmpty()));
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E($"YandexGeocoder request failed caught Exception: {ex.Message}");
                return null;
            }
        }

        public async Task<Place> GetDetailedInfoForPlace(Place place)
        {
            LpLogger.LOG_I("YandexGeocoder: trying to get detailed info for selected Place.");
            return await GetPlaceFromCoordinates(place.Latitude, place.Longitude);
        }

        private static string GetLocale()
        {
            var languageId = LanguagesModule.Instance.GetDefaultLanguageId();
            var languageIsoName = DigitalOrderingTexts.GetIsoNameByLanguegaId(languageId);
            
            var locale = GetLocaleFromLanguageIsoName(languageIsoName);
            LpLogger.LOG_I($"YandexGeocoder.GetLocale(): locale that will be used for request = {locale}");
            return locale;
        }

        private static string GetLocaleFromLanguageIsoName(string isoName)
        {
            switch (isoName)
            {
                case "ru":
                case "az":
                case "hy":
                case "kk":
                case "uz":
                case "ky":
                    return "ru_RU";

                case "uk":
                    return "uk_UA";
                
                default:
                    return "en_US";
            }
        }
        
        // Added for QA testing
        private string GetMaskedApi(string apiKey)
        {
            var firstThreeChars = apiKey.Substring(0, 3);
            var maskedApi = firstThreeChars + "****";
            return maskedApi;
        }
    }
}