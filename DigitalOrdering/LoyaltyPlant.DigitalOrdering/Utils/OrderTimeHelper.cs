using System;
using System.Threading.Tasks;
using static LoyaltyPlant.DigitalOrdering.Model.DOOrder;

namespace LoyaltyPlant.DigitalOrdering.Utils
{
    public static class OrderTimeHelper
    {
        // используется только в iOS
        // TODO рефакторинг? SetTime в DOController используется только для андроида, наверняка их можно объединить как-то
        public static async Task<TimeSpan> GetAvailableTime(DateTime date, TimeSpan time, CompleteOrderTypes orderTypes)
        {
            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;

            if (currentOrder.IsNeedToGetAsapTimeFromServer())
            {
                currentOrder.OrderTimeFrom = time;
                currentOrder.OrderDate = date;
                currentOrder.CompleteOrderType = orderTypes;

                var deliveryFeeOrderingTime = await DigitalOrderingModule.Instance.Controller.GetDeliveryFeeOrderingTimeResponse();

                if (deliveryFeeOrderingTime.isSuccessResponse)
                {
                    return currentOrder.OrderTimeFrom;
                }
            }
            else
            {
                if (!DigitalOrderingModule.Instance.Controller.SetDateTimeAndShowErrorMessageIfNeeded(date, time))
                    return currentOrder.OrderTimeFrom;
            }

            return default(TimeSpan);
        }
    }
}
