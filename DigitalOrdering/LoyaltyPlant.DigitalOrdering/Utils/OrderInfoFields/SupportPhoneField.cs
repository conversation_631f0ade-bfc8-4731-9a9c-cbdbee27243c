using System;
using LoyaltyPlant.View;

namespace LoyaltyPlant.DigitalOrdering.Utils.OrderInfoFields
{
    public class SupportPhoneField : LpView
    {
        public string Title { get; }
        public string Phone { get; }
        public string Hint { get; }
        public string PhoneForCall { get; }
        public Action Action { get; }

        public SupportPhoneField(string title, string phone, string hint, string phoneForCall)
        {
            Title = title;
            Phone = phone;
            Hint = hint;
            PhoneForCall = phoneForCall;
            Action = () =>
            {
                DigitalOrderingModule.Instance.Controller.MakeCall(phoneForCall);
            };
        }
    }
}
