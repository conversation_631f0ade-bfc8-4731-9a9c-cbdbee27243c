using System.Collections.Generic;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.DigitalOrdering.Utils.GoogleApis
{
    [NetExtentions.PreserveAttribute(AllMembers = true)]
    public class PlaceDetailsResponseModel
    {
        public Result result { get; set; }

        [NetExtentions.PreserveAttribute(AllMembers = true)]
        public class Result
        {
            public List<AddressComponent> address_components { get; set; } = new List<AddressComponent>();
            public string adr_address { get; set; }
            public string formatted_address { get; set; }
            public Geometry geometry { get; set; }
            public string icon { get; set; }
            public string id { get; set; }
            public string name { get; set; }
            public string place_id { get; set; }
            public string reference { get; set; }
            public string scope { get; set; }
            public List<string> types { get; set; }
            public string url { get; set; }
            public int utc_offset { get; set; }
            public string vicinity { get; set; }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class AddressComponent
            {
                public string long_name { get; set; }
                public string short_name { get; set; }
                public List<string> types { get; set; } = new List<string>();
            }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class Viewport
            {
                public Northeast northeast { get; set; }
                public Southwest southwest { get; set; }
            }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class Geometry
            {
                public Location location { get; set; }
                public Viewport viewport { get; set; }
            }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class Northeast
            {
                public double lat { get; set; }
                public double lng { get; set; }
            }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class Southwest
            {
                public double lat { get; set; }
                public double lng { get; set; }
            }

            [NetExtentions.PreserveAttribute(AllMembers = true)]
            public class Location
            {
                public double lat { get; set; }
                public double lng { get; set; }
            }
        }
    }
}

