using System;
using System.Collections.Generic;
using System.Linq;
using CoreGraphics;
using Foundation;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Core.Utils;
using UIKit;
using LoyaltyPlant.DigitalOrdering.iOS.Fragments;
using static LoyaltyPlant.App.iOS.Utils.UniversalUtils;

namespace LoyaltyPlant.DigitalOrdering.iOS.CollectionViewSources
{
    public class DoItemModifiersCollectionViewDelegate : UICollectionViewDelegateFlowLayout
    {
        private readonly nfloat _cellsWidth;
        private readonly DoGroupModifierView _parentProcessingView;

        private const float cellMargin = 8;
        private const float checkmarkWidth = 15;
        private const float modifierImageHeight = 68;

        private nfloat _modifierTitleAndPriceMaxHeight;
        private bool _shouldCellsShowImages;
        
        private List<Modifier> _modifiers;
        private List<Modifier> Modifiers
        {
            get => _modifiers;
            set
            {
                _shouldCellsShowImages = !value.IsNullOrEmpty() && value.Any(mod => !mod.BaseData.ImageUrl.IsNullOrEmpty());
                _modifiers = value;
            }
        }

        public DoItemModifiersCollectionViewDelegate(nfloat cellsWidth, List<Modifier> modifiers, DoGroupModifierView parentProcessingView)
        {
            _cellsWidth = cellsWidth;
            Modifiers = modifiers;
            _modifierTitleAndPriceMaxHeight = GetModifierLabelAndPriceMaxHeight();
            _parentProcessingView = parentProcessingView;
        }

        public nfloat GetCellHeight()
        {
            var baseCellHeight = _modifierTitleAndPriceMaxHeight + 3 * cellMargin;
            return _shouldCellsShowImages ? baseCellHeight + modifierImageHeight : baseCellHeight;
        }

        public override CGSize GetSizeForItem(UICollectionView collectionView, UICollectionViewLayout layout, NSIndexPath indexPath)
        {
            return new CGSize(_cellsWidth, GetCellHeight());
        }

        public override void ItemSelected(UICollectionView collectionView, NSIndexPath indexPath)
        {
            var cell = (DoItemModifierCell)collectionView.CellForItem(indexPath);
            var isCellAlreadySelected = cell.IsSelected;
            _parentProcessingView?.ModifierWasTappedAction(Modifiers[indexPath.Row], isCellAlreadySelected);
            collectionView.ReloadData();
        }

        private nfloat GetModifierLabelAndPriceMaxHeight()
        {
            float maxTitleHeight = 0;

            var titleLabel = new UILabel()
            {
                Lines = 0,
                Font = SystemFont(Dimensions.SemiSmallFont)
            };

            foreach (var modifier in Modifiers)
            {
                titleLabel.Frame = new CGRect(0, 0, _cellsWidth - 2 * cellMargin, 200);
                titleLabel.Text = modifier.Label;
                titleLabel.SizeToFit();
                maxTitleHeight = (float)Math.Max(maxTitleHeight, titleLabel.Frame.Height);
            }

            var priceLabel = new UILabel(new CGRect(0, 0, _cellsWidth - 3 * cellMargin - checkmarkWidth, 200))
            {
                Text = "MOCK",
                TextColor = Colors.CardTextColor,
                Font = SystemFont(Dimensions.SemiSmallFont, FontType.Bold)
            };
            priceLabel.SizeToFit();

            var maxPriceHeight = (float)Math.Max(priceLabel.Frame.Height, checkmarkWidth);

            return maxTitleHeight + maxPriceHeight;
        }

        public void UpdateSource(List<Modifier> modifiers)
        {
            Modifiers = modifiers;
        }
    }
}
