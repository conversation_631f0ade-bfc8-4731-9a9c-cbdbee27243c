using System;
using System.Collections.Generic;
using CoreGraphics;
using Foundation;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Backend.iOS.Elements;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using UIKit;

namespace LoyaltyPlant.DigitalOrdering.iOS
{
    public class DoMainCollectionViewDelegate : UICollectionViewDelegateFlowLayout
    {
        private List<IMainScreenElement> _cards;
        private Action _action;
        private Action _scrollaction;

        public DoMainCollectionViewDelegate(List<IMainScreenElement> cards)
        {
            _cards = cards;
        }

        public override CGSize GetSizeForItem(UICollectionView collectionView, UICollectionViewLayout layout, NSIndexPath indexPath)
        {
            var card = _cards[indexPath.Row];

            var screenW = Dimensions.Width;
            var paddedHalfW = Dimensions.CardWidth;
            var cardHeight = Dimensions.CardWidth / 3 * 2 + 2 * Dimensions.PaddingSmall + 36f;

            switch (card.ScreenCardType)
            {
                case MainScreenElementType.BigHeader:
                    var cell = new BigHeaderCell((card as BigHeaderElement).Text, UIColor.Clear);
                    return cell.Frame.Size;

                case MainScreenElementType.SimpleCardElement
                when (card is DoMenuItemElement menuItemElement)
                && (menuItemElement.MenuItem is GiftPosition giftPosition)
                && !Engine.Instance.IsFeatureActive(Loyalty.Feature.HIDE_POINTS_EVERYWHERE):
                    var height = 3 * Dimensions.PaddingSmall + paddedHalfW / 3 * 2 + 36f + 18f;
                    return new CGSize(paddedHalfW, height);

                case MainScreenElementType.SimpleCardElement:
                    return new CGSize(paddedHalfW, cardHeight);

                case MainScreenElementType.DoCategory:
                case MainScreenElementType.DoPresentsCategory:
                case MainScreenElementType.DoSubscriptionElement:
                    return new CGSize(paddedHalfW, Dimensions.CardWidth / 3 * 2 + 2 * Dimensions.PaddingSmall + 42f);

                case MainScreenElementType.DoMenuPosition: 
                case MainScreenElementType.DoPopupCategory:
                    var hh = 3 * Dimensions.PaddingSmall + paddedHalfW / 3 * 2 + 36f + 18f;
                    return new CGSize(paddedHalfW, hh);

                case MainScreenElementType.DoCategoryDescription:
                    {
                        var text = (card is Category) ? (card as Category).Description : (card as SimpleTextElement).Text;

                        var description = new UILabel(new CGRect(Dimensions.Padding, 0, Dimensions.WidthPadding, 0));
                        description.Lines = 0;
                        description.Font = UniversalUtils.SystemFont(Dimensions.SmallFont);
                        description.Text = text;
                        description.SetLineHeight(22f);
                        description.SizeToFit();
                        if (card is DoCategoryDescriptionElement)
                        {
                            return new CGSize(screenW, description.Frame.Height + Dimensions.Padding);
                        }
                        else
                            return new CGSize(screenW, description.Frame.Height);
                    }
            }
            return new CGSize(1, 1);
        }

        public override void ItemSelected(UICollectionView collectionView, NSIndexPath indexPath)
        {
            var card = _cards[indexPath.Row];

            LpLogger.LOG_I("ItemSelected card.ScreenCardType = " + card.ScreenCardType.ToString());

            switch (card.ScreenCardType)
            {
                // TODO аналогично для подарков надо сделать, чтобы было консистентно :)
                case MainScreenElementType.DoCategory:
                case MainScreenElementType.DoPresentsCategory:
                case MainScreenElementType.DoPopupCategory:
                    var categoryId = (card is DoCategoryElement) ? (card as DoCategoryElement).Category.Id : "";

                    LpLogger.LOG_I("ItemSelected DoCategory/DoPresentsCategory case, categoryId = " + categoryId);
                    if (!string.IsNullOrWhiteSpace(categoryId))
                        DigitalOrderingModule.Instance.Controller.OnClickedDoCategory(categoryId);
                    else
                        LpLogger.LOG_E("Something went wrong, categoryId = 0");

                    break;
            }
        }

        public void SetChangeTitleEvent(Action action)
        {
            _action = action;
        }

        public void UpdateScreen(List<IMainScreenElement> cards)
        {
            _cards = cards;
        }

        internal void SetScrollEvent(Action action)
        {
            _scrollaction = action;
        }

        [Export("scrollViewDidScroll:")]
        public void Scrolled(UIScrollView scrollView)
        {
            _action?.Invoke();
            _scrollaction?.Invoke();
        }
    }
}

