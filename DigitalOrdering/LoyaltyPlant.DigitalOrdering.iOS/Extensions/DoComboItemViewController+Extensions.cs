using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoreGraphics;
using Foundation;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.iOS.Fragments;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Texts;
using LoyaltyPlant.DigitalOrdering.iOS.ViewModels;
using UIKit;

namespace LoyaltyPlant.DigitalOrdering.iOS.ViewControllers
{
    public partial class DoComboItemViewController
    {
        private int _indexOfCurrentStep;
        private int _numberOfSteps;
        private NSLayoutConstraint _comboCollectionViewHeightConstraint;
        private List<string> _stepsTitles;
        
        private const string _comboItemSelectionCollectionViewCellIdentifier = "ComboItemSelectionCollectionViewCell";
        private const string _comboModifierSelectionCollectionViewCellIdentifier = "ComboModifierSelectionCollectionViewCell";
        private const string _cellReuseId = "MockCellReuseId";
        
        // Массивы моделей для ячеек коллекции, разбитые по шагам.
        private List<List<IComboStepCollectionViewItem>> _comboStepColletionViewItems;

        // Ключ - индекс шага комбо, значение - массив ViewModel'ей отображаемых в данный момент ячеек на этом шаге.
        private Dictionary<int, List<IComboStepCollectionViewItem>> _filteredCollectionViewItemsDictionary = new Dictionary<int, List<IComboStepCollectionViewItem>>();
        
        // Массив отфильтрованных ячеек коллекции для текущего шага комбо
        private List<IComboStepCollectionViewItem> _currentStepFilteredColletionViewItems => _filteredCollectionViewItemsDictionary[_indexOfCurrentStep];
        
        // Разбитые на шаги массивы Group модификаторов, устанавливается при инициализации
        private List<List<GroupModifier>> _structuredGroupModifiers;
        
        // Ключ - индекс (не номер!) шага, значение - завершен ли полностью шаг
        // Значение меняется после проверки по нажатию на кнопку Next
        private Dictionary<int, bool> _stepsFinishedStateDictionary;
        
        /// <summary>
        /// Creating UICollectionView that represents steps of combo.
        /// CollectionView is created only if combo has more than one step.
        /// </summary>
        private void CreateCellStepCollectionViewIfNeeded()
        {
            if (_numberOfSteps < 2)
                return;

            _stepSelectionView = new ComboItemStepSelectionView()
            {
                BackgroundColor = UIColor.Clear,
                TranslatesAutoresizingMaskIntoConstraints = false,
            };
            
            UpdateStepSelectionViewIfNeeded();
            _collectionContainerView.AddSubview(_stepSelectionView);
        }
        
        /// <summary>
        /// Creates ViewModel items for main collectionView.
        /// </summary>
        private void CreateComboStepColletionViewItems()
        {
            _comboStepColletionViewItems = new List<List<IComboStepCollectionViewItem>>();

            for (var i = 0; i < _structuredGroupModifiers.Count; i++)
            {
                _comboStepColletionViewItems.Add(new List<IComboStepCollectionViewItem>());
                CreateComboStepColletionViewItemsForStep(i);
            }
        }

        private void CreateComboStepColletionViewItemsForStep(int stepIndex)
        {
            _comboStepColletionViewItems[stepIndex] = new List<IComboStepCollectionViewItem>();
            
            var isOnlyOneProductAtThisStep = _structuredGroupModifiers[stepIndex]
                .Count(gm => gm.Modifiers
                    .Any(mod => RegularExpressions.IsSelectingProductOrPizzaBase(mod.PosId))) == 1;
            
            foreach (var groupModifier in _structuredGroupModifiers[stepIndex])
            {
                // Проверяем является ли текущий GroupModifier основным модификатором (posId содержит compound или product)
                // Если да - создаем для него требуемое количество вью моделей
                if (groupModifier.Modifiers.Any(modifier => RegularExpressions.IsSelectingProductOrPizzaBase(modifier.PosId)))
                {
                    for (var j = 0; j < groupModifier.Modifiers.Count; j++)
                    {
                        var modifier = groupModifier.Modifiers[j];

                        var modifierGroupTitle = GetModifierGroupTitle(groupModifier);

                        RegularExpressions.IsSelectingProductOrPizzaBase(modifier.PosId, out var itemPrefix, out var itemProductId);

                        var linkedMenuItem = DigitalOrderingModule.Instance.CurrentMenu.MenuItems
                            .FirstOrDefault(menuItem => menuItem.PosId?.Contains(itemProductId) ?? false);

                        if (linkedMenuItem == null)
                            linkedMenuItem = DigitalOrderingModule.Instance.CurrentMenu.MenuItems
                            .FirstOrDefault(menuItem => menuItem.Label?.Contains(modifierGroupTitle) ?? false);

                        if (linkedMenuItem == null)
                            linkedMenuItem = DigitalOrderingModule.Instance.CurrentMenu.MenuItems
                            .FirstOrDefault(menuItem => menuItem.PosId?.Contains(modifier.PosId) ?? false);

                        var changeButtonClickAction = new Action(() =>
                        {
                            _cartItem.DeselectModifier(groupModifier, modifier, false);
                            _stepsFinishedStateDictionary[stepIndex] = AreAllModifiersSelectedForCurrentStep();
                            UpdateComboStepColletionViewItemsForStep(_indexOfCurrentStep, isOnlyOneProductAtThisStep);
                            InvokeOnMainThread(() =>
                            {
                                UpdateStepSelectionViewIfNeeded();
                                _comboCollectionView.ReloadData();
                                UpdateBottomButtonAppearance(_stepsFinishedStateDictionary[stepIndex]);
                                UpdateScrollViewContentSize();
                            });
                            //https://jira.loyaltyplant.com/browse/APP-5243
                            _cartItem.SelectModifier(groupModifier, modifier.Id, out _);
                        });

                        var primaryModifierItem = new PrimaryGroupModifierItem(
                            groupModifier,
                            modifier,
                            j,
                            GetModifierGroupTitle(groupModifier),
                            GetMinMaxRequiredText(groupModifier),
                            linkedMenuItem?.Image,
                            modifier.Label,
                            linkedMenuItem?.Description,
                            string.IsNullOrEmpty(modifier.FormattedPrice) ? "" : "+" + modifier.FormattedPrice,
                            changeButtonClickAction);

                        UpdatePrimaryGroupItem(primaryModifierItem, stepIndex, isOnlyOneProductAtThisStep);

                        _comboStepColletionViewItems[stepIndex].Add(primaryModifierItem);
                    }
                }
                // ...Если нет - создаем вью модель для ячейки с простыми модификаторами
                else
                {
                    var additionalModifierItem = new AdditionalGroupModifierItem(groupModifier,
                        GetSimpleModifierGroupTitle(groupModifier.Label),
                        groupModifier.GetFreeOptionsString(),
                        GetMinMaxRequiredText(groupModifier),
                        groupModifier.Id,
                        _cartItem,
                        ref _cartModifiersDictionary,
                        ModifierWasTappedAction);

                    UpdateAdditionalGroupItem(additionalModifierItem, stepIndex);

                    _comboStepColletionViewItems[stepIndex].Add(additionalModifierItem);
                }
            }
            _filteredCollectionViewItemsDictionary[stepIndex] = GetFilteredCollectionViewItemsForStepIndex(stepIndex);
        }

        private void UpdateComboStepColletionViewItemsForStep(int stepIndex, bool isOnlyOneProductAtThisStep)
        {
            var thisStepCollectionViewItems = _comboStepColletionViewItems[stepIndex];

            foreach (var stepViewItem in thisStepCollectionViewItems)
            {
                switch (stepViewItem.ComboGroupModifierItemType)
                {
                    case ComboGroupModifierItemType.PrimaryGroupItem:
                        UpdatePrimaryGroupItem((PrimaryGroupModifierItem)stepViewItem, stepIndex, isOnlyOneProductAtThisStep);
                        break;
                    case ComboGroupModifierItemType.AdditionalGroupItem:
                        UpdateAdditionalGroupItem((AdditionalGroupModifierItem)stepViewItem, stepIndex);
                        break;
                    default:
                        break;
                }
            }

            _filteredCollectionViewItemsDictionary[stepIndex] = GetFilteredCollectionViewItemsForStepIndex(stepIndex);
        }
        
        private void UpdatePrimaryGroupItem(PrimaryGroupModifierItem primaryModifierItem, int stepIndex, bool isOnlyOneProductAtThisStep)
        {
            var groupModifier = primaryModifierItem.GroupModifier;
            var modifier = primaryModifierItem.Modifier;
                
            // Ищем posId выбранного модификатора
            var selectedPosId = _cartItem.CartGroupModifiers
                .SelectMany(cartModifier => cartModifier.SelectedModifiers)
                .Select(selectedModifier => selectedModifier.Modifier.PosId)
                .FirstOrDefault(posId => groupModifier.PosIds.Contains(posId));
            
            var isCellSelected = selectedPosId != default && selectedPosId == modifier.PosId;
                
            var cellTapAction = new Action(() =>
            {
                ModifierWasTappedAction(groupModifier, modifier, isCellSelected, isOnlyOneProductAtThisStep);

                if (_structuredGroupModifiers[stepIndex].Count == 1
                    && _stepsFinishedStateDictionary[stepIndex]
                    && stepIndex < _numberOfSteps - 1)
                    BottomButtonWasTapped();
            });

            primaryModifierItem.UpdateItem(isCellSelected, cellTapAction);
        }

        private void UpdateAdditionalGroupItem(AdditionalGroupModifierItem additionalModifierItem, int stepIndex)
        {
            var groupModifier = additionalModifierItem.CellGroupModifier;
            var shownModifiers = groupModifier.Modifiers.Where(mod => _structuredShownModifires[stepIndex]
                .Any(shownMod => shownMod.PosId == mod.PosId)).ToList();
            additionalModifierItem.UpdateItem(shownModifiers, GetMinMaxRequiredText(groupModifier));
        }

        private List<IComboStepCollectionViewItem> GetFilteredCollectionViewItemsForStepIndex(int stepIndex)
        {
            var allPrimaryGroupItems = _comboStepColletionViewItems[stepIndex]
                .Where(item => item.ComboGroupModifierItemType == ComboGroupModifierItemType.PrimaryGroupItem)
                .ToList();

            var selectedItems = allPrimaryGroupItems.Where(item => ((PrimaryGroupModifierItem)item).IsSelected).ToList();

            // Если нет ни одной выбранной пиццы/продукта, то отображаем все ячейки, без фильтрации
            if (selectedItems.IsNullOrEmpty())
                return _comboStepColletionViewItems[stepIndex];

            // Если есть выбранная пицца/продукт, то фильтруем ячейки, отбрасывая невыбранные пиццы
            // в каждой из групп модификаторов
            var selectedItemsDictionary = new Dictionary<GroupModifier, IComboStepCollectionViewItem>();

            foreach (var selectedItem in selectedItems)
            {
                var groupModifier = ((PrimaryGroupModifierItem)selectedItem).GroupModifier;
                selectedItemsDictionary[groupModifier] = selectedItem;
            }

            return _comboStepColletionViewItems[stepIndex]
                .Where(item =>
                    item is AdditionalGroupModifierItem additionalItem && additionalItem.ShownModifiers.Count > 0
                    || item is PrimaryGroupModifierItem primaryItem && (primaryItem.IsSelected || !selectedItemsDictionary.ContainsKey(primaryItem.GroupModifier)))
                .ToList();
        }

        private void CreateComboCollectionView()
        {
            _comboCollectionViewFlowLayout = new UICollectionViewFlowLayout()
            {
                ScrollDirection = UICollectionViewScrollDirection.Vertical,
            };

            _comboCollectionView = new UICollectionView(new CGRect(0, 0, 0, 2000), _comboCollectionViewFlowLayout)
            {
                ScrollEnabled = false,
                AllowsSelection = false,
                BackgroundColor = UIColor.Clear,
                TranslatesAutoresizingMaskIntoConstraints = false,
            };
            _comboCollectionView.DataSource = this;
            _comboCollectionView.Delegate = this;

            _collectionContainerView.AddSubview(_comboCollectionView);

            _comboCollectionView.RegisterClassForCell(typeof(DoComboItemSelectionCollectionViewCell), _comboItemSelectionCollectionViewCellIdentifier);
            _comboCollectionView.RegisterClassForCell(typeof(DoComboModifierSelectionCollectionViewCell), _comboModifierSelectionCollectionViewCellIdentifier);
            _comboCollectionView.RegisterClassForCell(typeof(UICollectionViewCell), _cellReuseId);
        }

        private void SetComboCollectionViewConstraints()
        {
            _comboCollectionViewHeightConstraint = _comboCollectionView.HeightAnchor.ConstraintEqualTo(4000);
            
            if (_stepSelectionView == null)
            {
                NSLayoutConstraint.ActivateConstraints(new NSLayoutConstraint[]
                {
                    _comboCollectionView.TopAnchor.ConstraintEqualTo(_collectionContainerView.TopAnchor),
                    _comboCollectionView.LeadingAnchor.ConstraintEqualTo(_collectionContainerView.LeadingAnchor),
                    _comboCollectionView.TrailingAnchor.ConstraintEqualTo(_collectionContainerView.TrailingAnchor),
                    _comboCollectionViewHeightConstraint
                });
            }
            else
            {
                NSLayoutConstraint.ActivateConstraints(new NSLayoutConstraint[]
                {
                    _stepSelectionView.TopAnchor.ConstraintEqualTo(_collectionContainerView.TopAnchor),
                    _stepSelectionView.LeadingAnchor.ConstraintEqualTo(_collectionContainerView.LeadingAnchor),
                    _stepSelectionView.TrailingAnchor.ConstraintEqualTo(_collectionContainerView.TrailingAnchor),
                    _stepSelectionView.HeightAnchor.ConstraintEqualTo(ComboItemStepSelectionView.ViewHeight),
                    
                    _comboCollectionView.TopAnchor.ConstraintEqualTo(_stepSelectionView.BottomAnchor),
                    _comboCollectionView.LeadingAnchor.ConstraintEqualTo(_collectionContainerView.LeadingAnchor),
                    _comboCollectionView.TrailingAnchor.ConstraintEqualTo(_collectionContainerView.TrailingAnchor),
                    _comboCollectionViewHeightConstraint
                });
            }
        }

        private nfloat GetCurrentStepComboCollectionViewHeight()
        {
            nfloat resultHeight = 0;
            _comboCollectionView.LayoutIfNeeded();
            foreach (var collectionViewItem in _currentStepFilteredColletionViewItems)
                resultHeight += collectionViewItem.GetCellHeight();

            resultHeight += (_currentStepFilteredColletionViewItems.Count - 1) *
                            _comboCollectionViewFlowLayout.MinimumInteritemSpacing;
            
            return resultHeight;
        }

        private List<List<GroupModifier>> GetSegmentedGroupModifiers()
        {
            if (!_item.IsComboItem)
                return new List<List<GroupModifier>>() { _item.GroupModifiers.ToList() };

            var groupModifiers = _item.GroupModifiers;
            var uniquieGroupPrefixes = new Dictionary<string, int>();
            var dividedGroups = new List<List<GroupModifier>>();
            var dividedProductModifiers = new List<List<Modifier>>();
            var currentPrefix = "BLANK PREFIX";
            var currentIndex = 0;
            for (int index = 0; index < groupModifiers.Count; index++)
                foreach (var modifier in groupModifiers[index].Modifiers)
                {
                    if (RegularExpressions.IsSelectingProductOrPizzaBase(modifier.PosId, out var prefix, out _)
                        && !uniquieGroupPrefixes.ContainsKey(prefix))
                    {
                        currentPrefix = prefix;
                        uniquieGroupPrefixes[prefix] = currentIndex;
                        dividedGroups.Add(new List<GroupModifier>());
                        dividedGroups[currentIndex].Add(groupModifiers[index]);
                        dividedProductModifiers.Add(new List<Modifier>());
                        currentIndex++;
                        break;
                    } 
                    
                    if ((modifier.PosId.StartsWith(currentPrefix + "|") || modifier.PosId.StartsWith(currentPrefix + ".")) &&
                        uniquieGroupPrefixes.ContainsKey(currentPrefix))
                    {
                        dividedGroups[uniquieGroupPrefixes[currentPrefix]].Add(groupModifiers[index]);
                        break;
                    }
                    
                    dividedGroups.Add(new List<GroupModifier>());
                    dividedGroups[currentIndex].Add(groupModifiers[index]);
                    currentIndex++;
                    break;
                }

            return dividedGroups;
        }

        private List<List<Modifier>> GetAllModifiersBySteps()
        {
            var list = new List<List<Modifier>>() { };
            foreach (var groupModifiers in _structuredGroupModifiers)
            {
                list.Add(new List<Modifier>());
                foreach (var groupModifier in groupModifiers)
                    list.Last().AddRange(groupModifier.Modifiers);
            }

            return list;
        }

        private Dictionary<int, bool> GetStepsFinishedStateDictionary()
        {
            var dictionary = new Dictionary<int, bool>();

            for (int i = 0; i < _numberOfSteps; i++)
                dictionary[i] = isChangeItemFlow;

            return dictionary;
        }

        private void CombineProducts()
        {
            var stepTitlesDictiondary = new Dictionary<string, int>();

            var combinedStepTitles = new List<string>();
            var combinedStructuredGroupModifiers = new List<List<GroupModifier>>();

            for (var i = 0; i < _structuredGroupModifiers.Count; i++)
            {
                var groupTitle = GetModifierGroupPrefix(i);

                if (_structuredGroupModifiers[i].Count == 1
                    && RegularExpressions.IsSelectingProductOrPizzaBase(_structuredGroupModifiers[i].First()
                        .Modifiers.First().PosId))
                {
                    if (stepTitlesDictiondary.TryGetValue(groupTitle, out var value))
                    {
                        combinedStructuredGroupModifiers[value]
                            .Add(_structuredGroupModifiers[i].First());
                    }
                    else
                    {
                        combinedStepTitles.Add(groupTitle);
                        stepTitlesDictiondary[groupTitle] = combinedStepTitles.Count - 1;
                        combinedStructuredGroupModifiers.Add(_structuredGroupModifiers[i]);
                    }
                }
                else
                {
                    combinedStepTitles.Add(groupTitle);
                    stepTitlesDictiondary[groupTitle] = combinedStepTitles.Count - 1;
                    combinedStructuredGroupModifiers.Add(_structuredGroupModifiers[i]);
                }
            }

            _stepsTitles = combinedStepTitles;
            _structuredGroupModifiers = combinedStructuredGroupModifiers;
            _numberOfSteps = _stepsTitles.Count;
        }

        private string GetModifierGroupPrefix(int index)
        {
            try
            {
                return _structuredGroupModifiers[index].First().Label.Split(":").First().Trim();
            }
            catch
            {
                var label = _structuredGroupModifiers[index].First().Label;
                LpLogger.LOG_E("DoComboItemViewController.GetModifierGroupPrefix(): group modifier label is null or has incorrect format! " +
                               "Group modifier label = " + (label != null ? $"\"{label}\"" : "null"));
                return string.Empty;
            }
        }

        private string GetModifierGroupTitle(GroupModifier groupModifier)
        {
            try
            {
                return groupModifier.Label.Split(":").Last().Trim();
            }
            catch
            {
                var label = groupModifier.Label;
                LpLogger.LOG_E("DoComboItemViewController.GetModifierGroupTitle(): group modifier label is null or has incorrect format! " +
                               "Group modifier label = " + (label != null ? $"\"{label}\"" : "null"));
                return string.Empty;
            }
        }

        private string GetSimpleModifierGroupTitle(string label)
        {
            try
            {
                return label.Split(":").Last().Trim();
            }
            catch
            {
                LpLogger.LOG_E("DoComboItemViewController.GetSimpleModifierGroupTitle(): group modifier label is null or has incorrect format! " +
                               "Group modifier label = " + (label != null ? $"\"{label}\"" : "null"));
                return string.Empty;
            }
        }

        private string GetMinMaxRequiredText(GroupModifier item)
        {
            var minMaxText = item.GetMinMaxText();
            var requiredText = item.Required ? I18N.FULL_DO_REQUIRED : "";
            var separator = string.Empty;

            if (!string.IsNullOrEmpty(minMaxText) && !string.IsNullOrEmpty(requiredText))
                separator = "  ";

            return string.Join(separator, new string[] { minMaxText, requiredText });
        }

        private bool AreAllModifiersSelectedForCurrentStep()
        {
            var stepGroupModifiers = _currentStepFilteredColletionViewItems.Select(item => item.ItemGroupModifier)
                .ToList();
            var allSelectedModifiersPosIds = _cartItem.CartGroupModifiers.SelectMany(groupMod => groupMod.SelectedModifiers).ToList().Select(cartMod => cartMod.Modifier.PosId).ToHashSet();

            foreach (var stepGroupModifier in stepGroupModifiers)
            {
                if (!stepGroupModifier.Required)
                    continue;

                var stepModifiers = stepGroupModifier.Modifiers.Select(mod => mod.PosId).ToList();
                if (stepModifiers.Intersect(allSelectedModifiersPosIds).ToList().Count() < stepGroupModifier.Min.Quantity)
                    return false;
            }

            return true;
        }
        
        private bool AllStepsFinishedBeforeThisStep(int index)
        {
            for (var i = 0; i < index; i++)
                if (!_stepsFinishedStateDictionary[i])
                    return false;

            return true;
        }

        private void ModifierWasTappedAction(GroupModifier groupModifier, Modifier modifier, bool isCellAlreadySelected, bool isOnlyOneProductAtThisStep)
        {
            LpLogger.LOG_D($"Modifier \"{modifier.Label}\" was tapped in ModifierGroup \"{groupModifier.Label}\"");
            var currentCartItem = _cartItem;
            bool allCurrentStepModifiersSelected;
            
            if (groupModifier.SelectionType == GroupModifierType.SelectOne)
            {
                var isSelectionSuccessful = DigitalOrderingModule.Instance.Controller.SelectModifier(currentCartItem, groupModifier, modifier.Id);

                ProcessModifiersViews(modifier, currentCartItem, groupModifier);
                UpdateCurrentStepModifiersSelectionState(out allCurrentStepModifiersSelected);
                
                if (RegularExpressions.IsSelectingProductOrPizzaBase(modifier.PosId))
                    UpdateComboStepColletionViewItemsForStep(_indexOfCurrentStep, isOnlyOneProductAtThisStep);

                InvokeOnMainThread(() =>
                {
                    UpdateStepSelectionViewIfNeeded();
                    _comboCollectionView.ReloadData();
                    _comboCollectionView.LayoutIfNeeded();
                    UpdateBottomButtonAppearance(allCurrentStepModifiersSelected);
                    UpdateScrollViewContentSize();
                });

                if (isSelectionSuccessful && !isCellAlreadySelected && RegularExpressions.IsSelectingProductOrPizzaBase(groupModifier.Modifiers.FirstOrDefault()?.PosId))
                    if (isOnlyOneProductAtThisStep || allCurrentStepModifiersSelected)
                        InvokeOnMainThread(() =>
                        {
                            UIView.Animate(0.3, 0, UIViewAnimationOptions.CurveEaseInOut,
                                () =>
                                {
                                    _scrollView.ResetContentOffset();
                                },
                                ScrollToCurrentStep);
                        });
                    else
                    {
                        ScrollToNextRequiredItem();
                    }
                return;
            }

            if (!isCellAlreadySelected)
            {
                DigitalOrderingModule.Instance.Controller.SelectModifier(currentCartItem, groupModifier, modifier.Id);
            }
            else
                currentCartItem.DeselectModifier(groupModifier, modifier);

            UpdateCurrentStepModifiersSelectionState(out allCurrentStepModifiersSelected);
            
            if (RegularExpressions.IsSelectingProductOrPizzaBase(modifier.PosId))
                UpdateComboStepColletionViewItemsForStep(_indexOfCurrentStep, isOnlyOneProductAtThisStep);
            

            _comboCollectionView.ReloadData();
            UpdateBottomButtonAppearance(allCurrentStepModifiersSelected);
            UpdateScrollViewContentSize();
        }

        private void UpdateCurrentStepModifiersSelectionState(out bool allCurrentStepModifiersSelected)
        {
            allCurrentStepModifiersSelected = AreAllModifiersSelectedForCurrentStep();
            _stepsFinishedStateDictionary[_indexOfCurrentStep] = allCurrentStepModifiersSelected;
        }

        private void ScrollToNextRequiredItem()
        {
            if (!_cartItem.IsApprovedRequiredByCount(out var notAcceptedModifier))
            {
                var indexOfItemToScroll = -1;
                
                for (int i = 0; i < _currentStepFilteredColletionViewItems.Count; i++)
                {
                    if (_currentStepFilteredColletionViewItems[i].ItemGroupModifier.Id ==
                        notAcceptedModifier.GroupModifier.Id)
                    {
                        indexOfItemToScroll = i;
                        break;
                    }
                }

                if (indexOfItemToScroll == -1)
                    return;

                nfloat newContentOffset = 0;

                for (int i = 0; i < indexOfItemToScroll; i++)
                    newContentOffset += _currentStepFilteredColletionViewItems[i].GetCellHeight();

                newContentOffset += indexOfItemToScroll * _comboCollectionViewFlowLayout.MinimumInteritemSpacing;
                
                _scrollView.scrollView.SetContentOffset(new CGPoint(0, newContentOffset), true);
            }
        }

        private async Task BottomButtonWasTapped()
        {
            var isCurrentStepCompleted = _stepsFinishedStateDictionary[_indexOfCurrentStep];

            if (!isCurrentStepCompleted && !_cartItem.IsApprovedRequiredByCount(out var corruptedGroupModifier))
            {
                var errorMessage = DigitalOrderingTexts.OrderingMinOptions(corruptedGroupModifier.GroupModifier.Min.Quantity, corruptedGroupModifier.Label);
                DigitalOrderingModule.Instance.Controller.ShowPopup(errorMessage);
                return;
            }
            
            if (isCurrentStepCompleted && _indexOfCurrentStep < _numberOfSteps - 1)
            {
                _indexOfCurrentStep++;
                UpdateCurrentStepModifiersSelectionState(out var allCurrentStepModifiersSelected);

                InvokeOnMainThread(() =>
                {
                    UpdateStepSelectionViewIfNeeded();
                    _comboCollectionView.ReloadData();
                    UpdateBottomButtonAppearance(allCurrentStepModifiersSelected);
                    UpdateScrollViewContentSize();
                    UIView.Animate(0.3, 0, UIViewAnimationOptions.CurveEaseInOut,
                        () =>
                        {
                            _scrollView.ResetContentOffset();
                        },
                        () =>
                        {
                            ScrollToCurrentStep();
                        });
                });
                return;
            }

            if (isChangeItemFlow)
            {
                if (DigitalOrderingModule.Instance.Controller.SaveCartChanges(_cartItem, positionInOrder, _isUpsellItem))
                    DismissModalViewController(true);
            }
            else
            {
                var result = await DigitalOrderingModule.Instance.Controller.AddToCart(_cartItem, _isUpsellItem);

                if (result.IsSuccess)
                    DismissModalViewController(true);
                // TODO: Здесь нужно еще реализовать скролл к той группе модификаторов, которая не заполнена.
            }
        }

        private void ScrollToCurrentStep()
        {
            _stepSelectionView?.ScrollToCurrentStepIfNeeded();
        }

        private void UpdateBottomButtonAppearance(bool isAllSelected)
        {
            _bottomView.UpdatePrice();
            var isLastStep = _indexOfCurrentStep == _numberOfSteps - 1;
            _bottomView.SetBottomButtonActivity(isAllSelected, isLastStep);
        }

        private void UpdateScrollViewContentSize()
        {
            var comboStepViewHeight = _stepSelectionView == null ? 0 : ComboItemStepSelectionView.ViewHeight;
            var comboCollectionViewHeight = GetCurrentStepComboCollectionViewHeight();
            
            _comboCollectionViewHeightConstraint.Constant = comboCollectionViewHeight;
            _collectionContainerView.SetHeight(comboStepViewHeight + comboCollectionViewHeight);
            _descriptionBottomSheetView.SetHeight(_collectionContainerView.Frame.Bottom + Height);
            _scrollView.SetContentSize(new CGSize(_scrollView.Frame.Width,_descriptionBottomSheetView.Frame.Bottom - Height + 3 * Dimensions.PaddingBig));
            _comboCollectionView.ReloadData();
        }

        private void UpdateStepSelectionViewIfNeeded()
        {
            _stepSelectionView?.Update(_numberOfSteps, _stepsTitles, _indexOfCurrentStep, _stepsFinishedStateDictionary,
                (index) =>
                {
                    if ((index > 0 && AllStepsFinishedBeforeThisStep(index)) || index == 0)
                    {
                        _indexOfCurrentStep = index;
                        UpdateCurrentStepModifiersSelectionState(out var allCurrentStepModifiersSelected);
                        UpdateStepSelectionViewIfNeeded();
                        
                        InvokeOnMainThread(() =>
                        {
                            _comboCollectionView.ReloadData();
                            UpdateBottomButtonAppearance(allCurrentStepModifiersSelected);
                            UpdateScrollViewContentSize();

                            UIView.Animate(0.3, 0, UIViewAnimationOptions.CurveEaseInOut,
                                () => _scrollView.ResetContentOffset(),
                                ScrollToCurrentStep);
                        });
                    }
                });
        }
    }

    #region IUICollectionViewDelegateFlowLayout methods
    public partial class DoComboItemViewController : IUICollectionViewDelegateFlowLayout
    {
        [Export("collectionView:layout:sizeForItemAtIndexPath:")]
        public CGSize GetSizeForItem(UICollectionView collectionView, UICollectionViewLayout layout, NSIndexPath indexPath)
        {
            UICollectionReusableView cell;
            var currentCollectionViewItem = _currentStepFilteredColletionViewItems[indexPath.Row];

            switch (currentCollectionViewItem.ComboGroupModifierItemType)
            {
                case ComboGroupModifierItemType.PrimaryGroupItem:
                    cell = new DoComboItemSelectionCollectionViewCell(new CGRect(0, 0, Dimensions.WidthPadding, 1000));
                    break;

                case ComboGroupModifierItemType.AdditionalGroupItem:
                    cell = new DoComboModifierSelectionCollectionViewCell(new CGRect(0, 0, Dimensions.WidthPadding, 1000));
                    break;

                default:
                    cell = new UICollectionViewCell();
                    break;
            }

            currentCollectionViewItem.UpdateCellContent(cell);

            return new CGSize(Dimensions.WidthPadding, currentCollectionViewItem.GetCellHeight());
        }
    }
    #endregion
    
    #region IUICollectionViewDataSource methods
    public partial class DoComboItemViewController : IUICollectionViewDataSource
    {
        public UICollectionViewCell GetCell(UICollectionView collectionView, NSIndexPath indexPath)
        {
            UICollectionReusableView cell;
            var currentCollectionViewItem = _currentStepFilteredColletionViewItems[indexPath.Row];

            switch (currentCollectionViewItem.ComboGroupModifierItemType)
            {
                case ComboGroupModifierItemType.PrimaryGroupItem:
                    cell = collectionView.DequeueReusableCell(_comboItemSelectionCollectionViewCellIdentifier, indexPath);
                    break;

                case ComboGroupModifierItemType.AdditionalGroupItem:
                    cell = collectionView.DequeueReusableCell(_comboModifierSelectionCollectionViewCellIdentifier, indexPath);
                    break;

                default:
                    cell = new UICollectionViewCell();
                    break;
            }

            currentCollectionViewItem.UpdateCellContent(cell);
            return cell as UICollectionViewCell;
        }

        public nint GetItemsCount(UICollectionView collectionView, nint section)
        {
            return _currentStepFilteredColletionViewItems.Count;
        }
    }
    #endregion
}
