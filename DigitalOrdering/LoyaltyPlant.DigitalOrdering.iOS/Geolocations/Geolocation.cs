using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoreLocation;
using Foundation;
using LoyaltyPlant.Core.Logger;

namespace LoyaltyPlant.DigitalOrdering.iOS.Geolocations
{
    public class Geolocation
    {
        static async Task<Location> PlatformLocationAsync(double accuracy, CancellationToken cancellationToken)
        {
            // the location manager requires an active run loop
            // so just use the main loop
            CLLocationManager manager = null;
            NSRunLoop.Main.InvokeOnMainThread(() => manager = new CLLocationManager());

            var tcs = new TaskCompletionSource<CLLocation>(manager);

            var listener = new SingleLocationListener();
            listener.LocationHandler += HandleLocation;

            manager.DesiredAccuracy = accuracy;
            manager.Delegate = listener;

            manager.PausesLocationUpdatesAutomatically = false;

            manager.StartUpdatingLocation();

            try
            {
                var clLocation = await tcs.Task;

                return clLocation?.ToLocation();
            }
            catch(Exception ex)
            {
                LpLogger.LOG_W("Cannot define user geoposition by xam.essentials method =( ", ex);
            }
            return null;

            void HandleLocation(CLLocation location)
            {
                manager.StopUpdatingLocation();
                tcs.TrySetResult(location);
            }

            void Cancel()
            {
                manager.StopUpdatingLocation();
                tcs.TrySetResult(null);
            }
        }

        public static Task<Location> GetLocationAsync(double accuracy)
        {
            return PlatformLocationAsync(accuracy, CancellationToken.None);
        }
    }

    class SingleLocationListener : CLLocationManagerDelegate
    {
        bool wasRaised = false;

        internal Action<CLLocation> LocationHandler { get; set; }

        public override bool ShouldDisplayHeadingCalibration(CLLocationManager manager) => false;

        public override void LocationsUpdated(CLLocationManager manager, CLLocation[] locations)
        {
            if (wasRaised)
                return;

            wasRaised = true;

            var location = locations?.LastOrDefault();

            if (location == null)
                return;

            LocationHandler?.Invoke(location);
        }
    }
}