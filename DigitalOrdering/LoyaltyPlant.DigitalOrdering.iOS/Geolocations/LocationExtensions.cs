using System;
using System.Collections.Generic;
using System.Linq;
using CoreLocation;
using Foundation;
using LoyaltyPlant.App.iOS;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using UIKit;

namespace LoyaltyPlant.DigitalOrdering.iOS.Geolocations
{
    public static partial class LocationExtensions
    {
        internal static Location ToLocation(this CLPlacemark placemark) =>
            new Location
            {
                Latitude = placemark.Location.Coordinate.Latitude,
                Longitude = placemark.Location.Coordinate.Longitude,
                Altitude = placemark.Location.Altitude,
                Timestamp = DateTimeOffset.UtcNow
            };

        internal static IEnumerable<Location> ToLocations(this IEnumerable<CLPlacemark> placemarks) =>
            placemarks?.Select(a => a.ToLocation());

        internal static Location ToLocation(this CLLocation location) =>
            new Location
            {
                Latitude = location.Coordinate.Latitude,
                Longitude = location.Coordinate.Longitude,
                Altitude = location.VerticalAccuracy < 0 ? default(double?) : location.Altitude,
                Accuracy = location.HorizontalAccuracy,
                Timestamp = location.Timestamp.ToDateTime(),
                Course = location.Course < 0 ? default(double?) : location.Course,
                Speed = location.Speed < 0 ? default(double?) : location.Speed
            };

        private static DateTimeOffset ToDateTime(this NSDate timestamp)
        {
            try
            {
                return new DateTimeOffset((DateTime)timestamp);
            }
            catch
            {
                return DateTimeOffset.UtcNow;
            }
        }
        
        public static Place ConvertToLpPlace(this CLPlacemark placeFromUi)
        {
            var place = new Place
                {
                    MainAddress = placeFromUi.Name,
                    Zip = placeFromUi.PostalCode,
                    City = placeFromUi.PostalAddress?.City,
                    Country = placeFromUi.Country,
                    Latitude = placeFromUi.Location.Coordinate.Latitude,
                    Longitude = placeFromUi.Location.Coordinate.Longitude,
                    Street = placeFromUi.Thoroughfare,
                    StreetNumber = placeFromUi.SubThoroughfare,
                    State = placeFromUi.PostalAddress?.State
                }; 
            
            place.Address = place.GetFormattedAddress(); 
            return place;
        }
    }
}
