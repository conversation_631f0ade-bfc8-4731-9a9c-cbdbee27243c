<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19162" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19144"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="44" id="cxk-wN-pdQ" customClass="OrderItemTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="cxk-wN-pdQ" id="YTl-1W-gDM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="hDf-Gg-KqM">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="32"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="V9y-iO-w2D">
                                <rect key="frame" x="0.0" y="0.0" width="175" height="32"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="фыв" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SKc-rb-uue">
                                        <rect key="frame" x="0.0" y="0.0" width="175" height="32"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kPC-A1-vVA">
                                        <rect key="frame" x="0.0" y="32" width="175" height="0.0"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </stackView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="topRight" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aex-xw-uWY">
                                <rect key="frame" x="183" y="0.0" width="25" height="32"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="25" id="KnW-Dl-cX3"/>
                                </constraints>
                                <fontDescription key="fontDescription" name=".AppleSystemUIFont" family=".AppleSystemUIFont" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="cuA-oD-PqV">
                                <rect key="frame" x="216" y="0.0" width="104" height="32"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="topRight" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k67-xi-QmE">
                                        <rect key="frame" x="0.0" y="0.0" width="50" height="32"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e9q-6W-Gf6">
                                        <rect key="frame" x="54" y="0.0" width="50" height="32"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="e9q-6W-Gf6" firstAttribute="leading" secondItem="k67-xi-QmE" secondAttribute="trailing" id="fO1-m4-xMZ"/>
                                    <constraint firstAttribute="width" constant="104" id="gga-nr-v4p"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="cuA-oD-PqV" firstAttribute="height" secondItem="V9y-iO-w2D" secondAttribute="height" id="Ohw-h5-vqC"/>
                            <constraint firstItem="aex-xw-uWY" firstAttribute="height" secondItem="V9y-iO-w2D" secondAttribute="height" id="VrS-FQ-2KR"/>
                            <constraint firstAttribute="trailing" secondItem="cuA-oD-PqV" secondAttribute="trailing" id="wVk-T1-51C"/>
                        </constraints>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="hDf-Gg-KqM" firstAttribute="top" secondItem="YTl-1W-gDM" secondAttribute="top" id="Q6T-Ik-qo5"/>
                    <constraint firstItem="hDf-Gg-KqM" firstAttribute="leading" secondItem="YTl-1W-gDM" secondAttribute="leading" id="oJ6-mb-zYa"/>
                    <constraint firstAttribute="trailing" secondItem="hDf-Gg-KqM" secondAttribute="trailing" id="rnK-rr-JVT"/>
                    <constraint firstAttribute="bottom" secondItem="hDf-Gg-KqM" secondAttribute="bottom" constant="12" id="zNI-x7-XjT"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="_coinLabel" destination="e9q-6W-Gf6" id="FR6-ZS-BR0"/>
                <outlet property="_countLabel" destination="aex-xw-uWY" id="T0V-hY-04p"/>
                <outlet property="_modifiersLabel" destination="kPC-A1-vVA" id="ZkQ-qy-GV5"/>
                <outlet property="_priceLabel" destination="k67-xi-QmE" id="bdD-wq-Ii2"/>
                <outlet property="_titleLabel" destination="SKc-rb-uue" id="gVe-Tm-WbV"/>
            </connections>
            <point key="canvasLocation" x="57" y="0.0"/>
        </tableViewCell>
    </objects>
</document>
