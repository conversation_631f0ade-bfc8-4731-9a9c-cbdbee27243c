using System;

using Foundation;
using UIKit;

namespace LoyaltyPlant.DigitalOrdering.iOS.Views
{
    public partial class MultibrandItemCollectionViewCell : UICollectionViewCell
    {
        public static readonly NSString Key = new NSString("MultibrandItemCollectionViewCell");
        public static readonly UINib Nib;

        static MultibrandItemCollectionViewCell()
        {
            Nib = UINib.FromName("MultibrandItemCollectionViewCell", NSBundle.MainBundle);
        }

        protected MultibrandItemCollectionViewCell(IntPtr handle) : base(handle)
        {
            // Note: this .ctor should not contain any initialization logic.
        }
    }
}
