<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Stack View standard spacing" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="cZE-iV-UFb" customClass="CheckoutComboCartItemCollectionViewCell">
            <rect key="frame" x="0.0" y="0.0" width="413" height="210"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="413" height="210"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="P8o-A1-YTT">
                        <rect key="frame" x="0.0" y="16" width="413" height="176"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="rxO-op-pEX">
                                <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="70" id="DXU-QT-8rW"/>
                                    <constraint firstAttribute="height" constant="70" id="rQE-Tj-rJd"/>
                                </constraints>
                            </imageView>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="qMy-7W-ojd">
                                <rect key="frame" x="82" y="0.0" width="303" height="83"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vvk-WP-ceJ">
                                        <rect key="frame" x="0.0" y="0.0" width="34.5" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="21" id="1Ot-ud-eJ9"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillProportionally" alignment="top" spacing="12" translatesAutoresizingMaskIntoConstraints="NO" id="nvl-CM-EY0" userLabel="Modifiers StackView">
                                        <rect key="frame" x="0.0" y="33" width="117.5" height="50"/>
                                    </stackView>
                                </subviews>
                            </stackView>
                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="X" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VvK-ql-tyP">
                                <rect key="frame" x="397" y="0.0" width="16" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="Re1-a4-f2g"/>
                                    <constraint firstAttribute="width" constant="16" id="fuh-ji-ukv"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <stackView opaque="NO" contentMode="scaleToFill" alignment="bottom" spacingType="standard" translatesAutoresizingMaskIntoConstraints="NO" id="tsE-PJ-9Yr">
                                <rect key="frame" x="82" y="95" width="331" height="81"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="14" translatesAutoresizingMaskIntoConstraints="NO" id="x8q-UR-7Ji">
                                        <rect key="frame" x="0.0" y="60.5" width="236" height="20.5"/>
                                        <subviews>
                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ecb-qZ-BzH">
                                                <rect key="frame" x="0.0" y="0.0" width="41.5" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" red="0.38039215686274508" green="0.38039215686274508" blue="0.38039215686274508" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XAm-jS-Shv">
                                                <rect key="frame" x="55.5" y="0.0" width="41.5" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" contentMode="left" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mIr-mT-pd0">
                                                <rect key="frame" x="111" y="0.0" width="125" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" red="0.38039215686274508" green="0.38039215686274508" blue="0.38039215686274508" alpha="0.5" colorSpace="custom" customColorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                    </stackView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fnc-w1-cVE" userLabel="DiscountLabel">
                                        <rect key="frame" x="244" y="61.5" width="39.5" height="19.5"/>
                                        <attributedString key="attributedText">
                                            <fragment content="Label">
                                                <attributes>
                                                    <font key="NSFont" metaFont="system" size="16"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="exm-mh-mEU">
                                        <rect key="frame" x="291.5" y="61.5" width="39.5" height="19.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qMy-7W-ojd" firstAttribute="top" secondItem="P8o-A1-YTT" secondAttribute="top" id="09y-LR-tZX"/>
                            <constraint firstAttribute="trailing" secondItem="VvK-ql-tyP" secondAttribute="trailing" id="0kh-B6-6Kg"/>
                            <constraint firstItem="rxO-op-pEX" firstAttribute="leading" secondItem="P8o-A1-YTT" secondAttribute="leading" id="5b3-cG-Ai1"/>
                            <constraint firstItem="VvK-ql-tyP" firstAttribute="leading" secondItem="qMy-7W-ojd" secondAttribute="trailing" constant="12" id="OYN-fc-URy"/>
                            <constraint firstItem="tsE-PJ-9Yr" firstAttribute="leading" secondItem="rxO-op-pEX" secondAttribute="trailing" constant="12" id="Vso-O9-oEf"/>
                            <constraint firstItem="rxO-op-pEX" firstAttribute="top" secondItem="P8o-A1-YTT" secondAttribute="top" id="X4u-wK-ehp"/>
                            <constraint firstItem="VvK-ql-tyP" firstAttribute="trailing" secondItem="tsE-PJ-9Yr" secondAttribute="trailing" id="YZy-cL-QJa"/>
                            <constraint firstItem="qMy-7W-ojd" firstAttribute="leading" secondItem="rxO-op-pEX" secondAttribute="trailing" constant="12" id="dnd-dL-lms"/>
                            <constraint firstAttribute="bottom" secondItem="tsE-PJ-9Yr" secondAttribute="bottom" id="pPU-hU-ML2"/>
                            <constraint firstItem="VvK-ql-tyP" firstAttribute="top" secondItem="P8o-A1-YTT" secondAttribute="top" id="rGa-nL-hlQ"/>
                            <constraint firstItem="tsE-PJ-9Yr" firstAttribute="top" secondItem="qMy-7W-ojd" secondAttribute="bottom" constant="12" id="vYP-Q3-0Ny"/>
                        </constraints>
                    </view>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZWc-mH-rVS">
                        <rect key="frame" x="0.0" y="209" width="413" height="1"/>
                        <color key="backgroundColor" red="0.38039215686274508" green="0.38039215686274508" blue="0.38039215686274508" alpha="0.26000000000000001" colorSpace="custom" customColorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="nOT-mL-ZQn"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="P8o-A1-YTT" secondAttribute="trailing" id="0sD-Ok-rg6"/>
                <constraint firstItem="P8o-A1-YTT" firstAttribute="leading" secondItem="cZE-iV-UFb" secondAttribute="leading" id="2GY-YL-CQ9"/>
                <constraint firstItem="ZWc-mH-rVS" firstAttribute="leading" secondItem="cZE-iV-UFb" secondAttribute="leading" id="30S-5H-58E"/>
                <constraint firstAttribute="bottom" secondItem="P8o-A1-YTT" secondAttribute="bottom" constant="18" id="Owx-DN-tDm"/>
                <constraint firstAttribute="trailing" secondItem="ZWc-mH-rVS" secondAttribute="trailing" id="TFf-oZ-dEE"/>
                <constraint firstItem="P8o-A1-YTT" firstAttribute="top" secondItem="cZE-iV-UFb" secondAttribute="top" constant="16" id="hnL-iA-blB"/>
                <constraint firstAttribute="bottom" secondItem="ZWc-mH-rVS" secondAttribute="bottom" id="ojg-Tm-GSK"/>
            </constraints>
            <size key="customSize" width="413" height="210"/>
            <connections>
                <outlet property="_bottomLine" destination="ZWc-mH-rVS" id="IAF-Yl-SFV"/>
                <outlet property="_countLabel" destination="XAm-jS-Shv" id="onp-1p-WdL"/>
                <outlet property="_crossLabel" destination="VvK-ql-tyP" id="wff-v4-5Qp"/>
                <outlet property="_discountLabel" destination="fnc-w1-cVE" id="2T1-ys-zDZ"/>
                <outlet property="_itemImageView" destination="rxO-op-pEX" id="Nb4-vL-tGW"/>
                <outlet property="_minusLabel" destination="Ecb-qZ-BzH" id="kgL-bN-WpD"/>
                <outlet property="_modifiersStackView" destination="nvl-CM-EY0" id="AD5-df-uSE"/>
                <outlet property="_plusLabel" destination="mIr-mT-pd0" id="WdI-Wq-QhR"/>
                <outlet property="_priceLabel" destination="exm-mh-mEU" id="ouV-Jv-s3a"/>
                <outlet property="_titleLabel" destination="vvk-WP-ceJ" id="AD5-df-uSR"/>
            </connections>
            <point key="canvasLocation" x="250.00000000000003" y="53.571428571428569"/>
        </collectionViewCell>
    </objects>
</document>
