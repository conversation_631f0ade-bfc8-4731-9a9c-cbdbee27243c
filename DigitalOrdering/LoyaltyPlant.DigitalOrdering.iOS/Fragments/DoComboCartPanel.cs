using CoreGraphics;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.iOS.ViewControllers;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.Utils;
using LoyaltyPlant.Texts;
using UIKit;
using Foundation;
using System;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.TieredLoyalty;

namespace LoyaltyPlant.DigitalOrdering.iOS.Fragments
{
    public class DoComboCartPanel : UIView
    {
        private readonly CartMenuItem _cartItem;
        private readonly UIView _bottomButton;
        private readonly DoComboItemViewController _parent;
        private readonly MenuItem _position;

        private UILabel _presentPriceView;
        private UILabel _cartIcon;
        private UILabel _addToCartLabel;
        private UILabel _availabilityIfNeededLabel;
        private UILabel _priceLabel;
        private UILabel _priceBeforeDiscountLabel;
        private bool _isAvailableForCurrentGrade = true;
        
        private const float LockIconWidth = 30;

        private AddRemoveControl _addRemoveControl;

        public DoComboCartPanel(MenuItem position, CartMenuItem cartItem, DoComboItemViewController parent, bool isUpsellItem)
        {
            _cartItem = cartItem;
            _parent = parent;
            _position = position;
            this.BackgroundColor = Colors.SecondaryBackgroundColor;
            this.SetWidth(UIScreen.MainScreen.Bounds.Width);
            this.SetHeight(84f);
            
            if (_cartItem == null )
                _cartItem = new CartMenuItem(position);

            if (position is GiftPosition giftPosition && giftPosition.LinkedPresent != null )
                    _isAvailableForCurrentGrade = !TieredLoyaltyModule.Instance?.IsCardBlockedByTieredLoyaltyV2(giftPosition.LinkedPresent) ?? true;

            _bottomButton = CreateButton();
            _bottomButton.SetLeft(Dimensions.Padding); 
            _bottomButton.SetWidth(Dimensions.WidthPadding);

            var addToCartLabelText = I18N.ADD_TO_CART;
            if (!_isAvailableForCurrentGrade && position is GiftPosition giftPos)
                addToCartLabelText = TieredLoyaltyModule.Instance.GetActionButtonTextForLockedCard(giftPos.LinkedPresent.RequiredTierGrades);
            else if (parent.isChangeItemFlow)
                addToCartLabelText = I18N.SAVE;

            CreateAddToCartLabel(_bottomButton, addToCartLabelText);
            CreateAvailabilityLabelIfNeeded();

            CreateLabelAboutMinSumForOrderWithPresentIfNeeded();

            _bottomButton.AccessibilityIdentifier = "addToCartButton";

            SetupPriceAndDiscountLabels();
            
            _bottomButton.IsAccessibilityElement = true;
            _bottomButton.AccessibilityLabel = I18N.ADD_TO_CART + " " + _cartItem.GetTotalPriceString();
            _bottomButton.AccessibilityTraits = UIAccessibilityTrait.Button;

            AdjustViewsForRtlIfNeeded();
        }

        private void AdjustViewsForRtlIfNeeded()
        {
            if (Settings.IsLtrSelected || !_isAvailableForCurrentGrade)
                return;
            
            _bottomButton.Transform = CGAffineTransform.MakeScale(-1, 1);
            
            foreach (var view in _bottomButton.Subviews)
                view.Transform = CGAffineTransform.MakeScale(-1, 1);
        }

        private void SetupPriceAndDiscountLabels()
        {
            if (!_isAvailableForCurrentGrade)
                return;
            
            if (_priceLabel == null)
            {
                _priceLabel = new UILabel()
                {
                    TextColor = Colors.AccentTextColor,
                    TextAlignment = UITextAlignment.Right
                };
                _priceLabel.SetFont(UniversalUtils.FontStyle.Default);
                _priceLabel.Frame = _bottomButton.Bounds;
                _bottomButton.AddSubview(_priceLabel);
            }

            _priceLabel.SetWidth(_bottomButton.Bounds.Width);
            _priceLabel.Text = _cartItem.GetTotalPriceString();
            _priceLabel.SizeWidthToFit();
            _priceLabel.SetRight(_bottomButton.Bounds.Right - Dimensions.Padding);
            _priceLabel.SetHeight(16f);
            _priceLabel.SetTop(_bottomButton.Frame.Height / 2 - _priceLabel.Frame.Height / 2);

            if (_priceBeforeDiscountLabel == null)
            {
                _priceBeforeDiscountLabel = new UILabel()
                {
                    TextColor = Colors.AccentTextColor,
                };
                _priceBeforeDiscountLabel.SetFont(UniversalUtils.FontStyle.Default);
                _bottomButton.AddSubview(_priceBeforeDiscountLabel);
            }

            if (_cartItem.GetTotalItemDiscountValue() <= 0)
            {
                _priceBeforeDiscountLabel.Hidden = true; 
            }
            else
            {
                var crossedLabelRightEdge = _priceLabel.Frame.Left - Dimensions.Padding;
                
                _priceBeforeDiscountLabel.Hidden = false;
                _priceBeforeDiscountLabel.Text = Formatter.FormatAmount(_cartItem.GetTotalPriceBeforeDiscount());
                _priceBeforeDiscountLabel.SizeWidthToFit();
                _priceBeforeDiscountLabel.SetRight(crossedLabelRightEdge);
                _priceBeforeDiscountLabel.AddStrikeAttributeToLabel();
                _priceBeforeDiscountLabel.SetHeight(16f);
                _priceBeforeDiscountLabel.SetTop(_bottomButton.Frame.Height / 2 - _priceLabel.Frame.Height / 2);
            }
            
            if (_position is GiftPosition && _isAvailableForCurrentGrade 
                && (PartnerModule.Instance.IsNeedToPayForPresents() || _cartItem.GetTotalPrice() > 0))
            {
                _presentPriceView = new UILabel();

                _presentPriceView = new UILabel();
                _presentPriceView.Text = _cartItem.GetPresentPriceString();
                _presentPriceView.MakePresentPriceLabel(Colors.AccentTextColor);
                _presentPriceView.SetTop(_bottomButton.Frame.Height / 2 - _presentPriceView.Frame.Height / 2);
                _presentPriceView.SetRight(_bottomButton.Frame.Width - Dimensions.Padding);
                _bottomButton.AddSubview(_presentPriceView);

                _cartIcon = new UILabel();
                _cartIcon.MakeCoinLabel(Dimensions.MiddleFont, Colors.AccentTextColor);
                _cartIcon.SetTop(_bottomButton.Frame.Height / 2 - _cartIcon.Frame.Height / 2);

                if (_presentPriceView != null)
                    _cartIcon.SetRight(_presentPriceView.Frame.Left - 4f);
                else
                    _cartIcon.SetRight(_bottomButton.Frame.Width - Dimensions.Padding);

                _bottomButton.AddSubview(_cartIcon);
                _priceLabel.SetRight(_cartIcon.Frame.Left - 2f);
            }
        }

        private UIView CreateButton()
        {
            var button = new UIView(new CGRect(0, 0, 0, 52f));
            button.BackgroundColor = _isAvailableForCurrentGrade ? Colors.AccentColor : Colors.UnavailbleForCurrentTierColor;
            button.Layer.CornerRadius = Dimensions.CardRadius;
            button.SetTop(this.Frame.Height / 2 - button.Frame.Height / 2);
            this.AddSubview(button);
            return button;
        }

        private void CreateAvailabilityLabelIfNeeded()
        {
            _availabilityIfNeededLabel = new UILabel(new CGRect(Dimensions.Padding, 0, Dimensions.WidthPadding, 0));

            if (_cartItem.AvailabilityIntervalsInfo != null &&
                !_cartItem.MenuItem.IsNeedAvailabilityWholeDayToday())
            {
                try
                {
                    var availabilityText = DigitalOrderingModule.Instance.GetAvailabilityText(_cartItem.MenuItem);

                    _availabilityIfNeededLabel.Text = availabilityText;
                    _availabilityIfNeededLabel.TextColor = Colors.BackgroundTextColor.ColorWithAlpha(0.3f);
                    _availabilityIfNeededLabel.SetFont(UniversalUtils.FontStyle.Default);
                    _availabilityIfNeededLabel.TextAlignment = UITextAlignment.Center;
                    _availabilityIfNeededLabel.Lines = 0;

                    if (availabilityText.Contains(I18N.MORE_INFO_LINK))
                    {
                        var underlineAttriString = new NSMutableAttributedString(availabilityText);
                        underlineAttriString.BeginEditing();
                        underlineAttriString.AddAttribute(UIStringAttributeKey.Font, UIFont.SystemFontOfSize(17), new NSRange(0, underlineAttriString.Length));

                        var range = GetRangeOfString(availabilityText, I18N.MORE_INFO_LINK);
                        underlineAttriString.AddAttribute(UIStringAttributeKey.UnderlineStyle, NSNumber.FromInt32((int)NSUnderlineStyle.Single), range);
                        underlineAttriString.EndEditing();
                        _availabilityIfNeededLabel.UserInteractionEnabled = true;
                        _availabilityIfNeededLabel.AttributedText = underlineAttriString;

                        UITapGestureRecognizer tapGesture = new UITapGestureRecognizer((tap) =>
                        {
                            if (DidTapAttributedTextInLabel(tap, _availabilityIfNeededLabel, range, underlineAttriString))
                            {
                                DigitalOrderingModule.Instance.Controller.ShowAvailabilityInfoHint(_cartItem.AvailabilityIntervalsInfo);
                            }
                        });
                        if (_availabilityIfNeededLabel.GestureRecognizers == null)
                            _availabilityIfNeededLabel.AddGestureRecognizer(tapGesture);
                    }

                    _availabilityIfNeededLabel.SizeHeightToFit();

                    this.AddSubview(_availabilityIfNeededLabel);

                    if (_cartItem.MenuItem.IsUnavailableToOrder())
                    {
                        _availabilityIfNeededLabel.SetBottom(this.Frame.Height - 2 * Dimensions.Padding);

                        _bottomButton.Hidden = true;

                        if (_addRemoveControl != null)
                            _addRemoveControl.Hidden = true;
                    }
                    else
                    {
                        var available = DigitalOrderingModule.Instance.CurrentOrder.CurrentZone.IsOrderingAvailable(out string unavailableReason);
                        if (available)
                        {
                            this.SetHeight(84f + _availabilityIfNeededLabel.Frame.Height + Dimensions.PaddingSmall);

                            _bottomButton.SetBottom(this.Frame.Bottom - Dimensions.Padding);
                            _addRemoveControl?.SetBottom(this.Frame.Bottom - Dimensions.Padding);

                            _availabilityIfNeededLabel.SetBottom(_bottomButton.Frame.Top - Dimensions.PaddingSmall);
                        }
                    }
                }
                catch (Exception ex)
                {
                    LpLogger.LOG_E("_cartItem.AvailabilityIntervalsInfo != null exception. ex.StackTrace = " + ex.StackTrace);
                }
            }
        }

        private void CreateLabelAboutMinSumForOrderWithPresentIfNeeded()
        {
            _availabilityIfNeededLabel = new UILabel(new CGRect(Dimensions.Padding, 0, Dimensions.WidthPadding, 0));

            if (_cartItem.BaseEntity is GiftPositionData giftPositionData &&
                    giftPositionData.MinDoOrderAmount > 0)
            {
                var availabilityText = DigitalOrderingModule.Instance.GetTextForLimitedMessage(giftPositionData.MinDoOrderAmount);

                _availabilityIfNeededLabel.Text = availabilityText;
                _availabilityIfNeededLabel.TextColor = Colors.BackgroundTextColor.ColorWithAlpha(0.3f);
                _availabilityIfNeededLabel.SetFont(UniversalUtils.FontStyle.Default);
                _availabilityIfNeededLabel.TextAlignment = UITextAlignment.Center;
                _availabilityIfNeededLabel.Lines = 0;

                _availabilityIfNeededLabel.SizeHeightToFit();

                this.AddSubview(_availabilityIfNeededLabel);

                this.SetHeight(84f + _availabilityIfNeededLabel.Frame.Height + Dimensions.PaddingSmall);

                _bottomButton.SetBottom(this.Frame.Bottom - Dimensions.Padding);
                _addRemoveControl?.SetBottom(this.Frame.Bottom - Dimensions.Padding);

                _availabilityIfNeededLabel.SetBottom(_bottomButton.Frame.Top - Dimensions.PaddingSmall);
            }
        }

        private NSRange GetRangeOfString(string str, string containStr)
        {
            return new NSRange(str.IndexOf(containStr), containStr.Length);
        }

        private bool DidTapAttributedTextInLabel(UITapGestureRecognizer tap, UILabel label, NSRange targetRange,
            NSMutableAttributedString attribute)
        {
            if (attribute == null)
                return false;

            var layoutManager = new NSLayoutManager();
            var textContainer = new NSTextContainer(CGSize.Empty);
            var textStorage = new NSTextStorage();

            textStorage.SetString(attribute);
            layoutManager.AddTextContainer(textContainer);
            textStorage.AddLayoutManager(layoutManager);

            textContainer.LineFragmentPadding = 0;
            textContainer.LineBreakMode = label.LineBreakMode;
            textContainer.MaximumNumberOfLines = (nuint)label.Lines;
            var labelSize = label.Bounds.Size;
            textContainer.Size = labelSize;

            var locationOfTouchInLabel = tap.LocationInView(label.Superview);
            var textBoundingBox = layoutManager.GetUsedRectForTextContainer(textContainer);
            var textContainerOffset = new CGPoint((labelSize.Width - textBoundingBox.Size.Width) * 0.5 - textBoundingBox.Location.X,
                            (labelSize.Height - textBoundingBox.Size.Height) * 0.5 - textBoundingBox.Location.Y);

            var locationOfTouchInTextContainer = new CGPoint(locationOfTouchInLabel.X - textContainerOffset.X,
                            locationOfTouchInLabel.Y - textContainerOffset.Y);

            nfloat partialFraction = 1;
            var indexOfCharacter = (nint)layoutManager.GetCharacterIndex(locationOfTouchInTextContainer, textContainer);

            return (indexOfCharacter >= targetRange.Location) && (indexOfCharacter <= targetRange.Location + targetRange.Length);
        }

        private void CreateAddToCartLabel(UIView button, string text)
        {
            _addToCartLabel = new UILabel(new CGRect(Dimensions.Padding, 0, 0, 0));
            _addToCartLabel.Text = text;
            _addToCartLabel.SetFont(UniversalUtils.FontStyle.Default);
            _addToCartLabel.TextColor = _isAvailableForCurrentGrade ? Colors.AccentTextColor : Colors.BackgroundTextColor;
            _addToCartLabel.SizeToFit();
            button.AddSubview(_addToCartLabel);
            _addToCartLabel.SetTop(button.Frame.Height / 2 - _addToCartLabel.Frame.Height / 2);
            
            if (_isAvailableForCurrentGrade)
                return;
            
            _addToCartLabel.SetWidth(button.Frame.Width - 2 * Dimensions.Padding - LockIconWidth - Dimensions.PaddingSmall);
                
            var lockImage = UIImage.FromFile("lock_icon.png")
                .ImageWithRenderingMode(UIImageRenderingMode.AlwaysTemplate);
                    
            var lockImageView = new UIImageView(new CGRect(0 , 0, LockIconWidth, LockIconWidth))
            {
                ClipsToBounds = true,
                ContentMode = UIViewContentMode.ScaleAspectFill,
                Image = lockImage,
                TintColor = Colors.BackgroundTextColor
            };
            button.AddSubview(lockImageView);
            lockImageView.SetCenterY(_addToCartLabel.Center.Y);

            if (Settings.IsLtrSelected)
            {
                _addToCartLabel.TextAlignment = UITextAlignment.Left;
                lockImageView.SetRight(button.Frame.Width - Dimensions.Padding);
            }
            else
            {
                _addToCartLabel.TextAlignment = UITextAlignment.Right;
                _addToCartLabel.SetRight(button.Frame.Width - Dimensions.Padding);
                lockImageView.SetLeft(Dimensions.Padding);
            }
        }

        public CartMenuItem GetCartItem()
        {
            return _cartItem;
        }

        public void UpdatePrice()
        {
            SetupPriceAndDiscountLabels();
        }

        public void SetBottomButtonActivity(bool isActive, bool isLastStep)
        {
            if (!_isAvailableForCurrentGrade)
                return;
            
            _bottomButton.BackgroundColor = isActive ? Colors.AccentColor : Colors.DividerTextColor;
            _addToCartLabel.Text = isLastStep ? (_parent.isChangeItemFlow ? I18N.SAVE : I18N.ADD_TO_CART) : I18N.MENU_COMBO_NEXT_BUTTON;
            _addToCartLabel.SizeWidthToFit();
        }
        public void SetBottomButtonAction(Action action)
        {
            if (_isAvailableForCurrentGrade)
                _bottomButton.SetMaterialSelection(action);
        }
    }
}

