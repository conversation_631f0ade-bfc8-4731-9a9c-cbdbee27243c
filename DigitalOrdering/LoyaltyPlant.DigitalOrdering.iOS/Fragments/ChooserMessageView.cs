using System;
using System.Linq;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Messages.ChooserMessages;
using LoyaltyPlant.Texts;
using UIKit;

namespace LoyaltyPlant.DigitalOrdering.iOS.Fragments
{
    public class ChooserMessageView : UIView
    {
        private readonly UIView _messageView;

        public ChooserMessageView(ChooserMessage message)
        {
            this.SetSize(UIScreen.MainScreen.Bounds.Size);
            this.BackgroundColor = UIColor.Black.ColorWithAlpha(0.25f);

            this.SetMaterialSelection(() => Dismiss());

            _messageView = new UIView(new CGRect(Dimensions.Padding, 0, Dimensions.WidthPadding, 0));
            _messageView.BackgroundColor = UIColor.White;
            _messageView.Layer.CornerRadius = Dimensions.CardRadius;
            _messageView.ClipsToBounds = true;

            nfloat y = 0f;

            if (message.Text != null)
            {
                var header = new UILabel(new CGRect(Dimensions.Padding, Dimensions.PaddingBig, _messageView.Frame.Width - 2 * Dimensions.Padding, 0));
                header.Lines = 0;
                header.SetFont(UniversalUtils.FontStyle.Subheader, UniversalUtils.FontType.Bold);
                header.TextColor = UIColor.Black.ColorWithAlpha(0.8f);
                header.Text = message.Text;
                header.SizeHeightToFit();
                _messageView.AddSubview(header);

                y = header.Frame.Bottom + Dimensions.Padding;
            }

            var scrollView = new UIScrollView(new CGRect(0, y, Dimensions.WidthPadding, 0));
            scrollView.ShowsVerticalScrollIndicator = false;
            _messageView.AddSubview(scrollView);
            y = 0;

            var buttons = message.Buttons.Cast<ChooserMessageItem>();
            foreach (var button in buttons)
            {
                y = CreateOptionButton(y, button, scrollView);
            }

            var max = 380f;
            if (UIScreen.MainScreen.Bounds.Height <= 568)
                max = 280f;

            scrollView.SetHeight(NMath.Min(y, max));
            scrollView.ContentSize = new CGSize(scrollView.Frame.Width, y);
            y = scrollView.Frame.Bottom;

            var line = new UIView(new CGRect(0, y, _messageView.Frame.Width, 0.5f));
            line.BackgroundColor = UIColor.Black.ColorWithAlpha(0.25f);
            _messageView.AddSubview(line);

            var cancelView = new UIView(new CGRect(0, y, _messageView.Frame.Width, 58f));
            _messageView.AddSubview(cancelView);

            _messageView.SetHeight(cancelView.Frame.Bottom);
            this.AddSubview(_messageView);
            _messageView.SetBottom(this.Frame.Height + _messageView.Frame.Height);

            var cancelText = new UILabel(new CGRect(0, 0, cancelView.Frame.Width, cancelView.Frame.Height));
            cancelText.TextColor = Colors.MessageboxAccentTextColor;
            cancelText.TextAlignment = UITextAlignment.Center;
            cancelText.SetFont(UniversalUtils.FontStyle.Default);
            cancelText.Text = I18N.CANCEL;
            cancelView.AddSubview(cancelText);
            cancelView.SetMaterialSelection(() =>
            {
                Dismiss();
            });
        }

        public nfloat CreateOptionButton(nfloat y, ChooserMessageItem item, UIView rootView)
        {
            var button = new UIView(new CGRect(0, y, _messageView.Frame.Width, 0));
            var x = Dimensions.Padding;

            button.SetHeight(80f);

            if (item.ShowImage && item.Image != null)
            {
                var icon = new UIImageView(new CGRect(x, Dimensions.Padding,
                                                  50f * Dimensions.Multiplier, 50 * Dimensions.Multiplier));
                icon.BackgroundColor = UIColor.LightGray;
                icon.ContentMode = UIViewContentMode.ScaleAspectFill;
                icon.Layer.CornerRadius = icon.Frame.Height / 2;
                icon.ClipsToBounds = true;
                item.Image.NeedToShowPlaceholder = true;
                icon.SetImage(item.Image);
                button.AddSubview(icon);
                x = icon.Frame.Right + Dimensions.PaddingBig;
                icon.SetInCenterVertical(button);
            }

            var header = new UILabel(new CGRect(x, 0, button.Frame.Width - Dimensions.Padding - x, 0));
            header.Lines = 0;
            header.SetFont(UniversalUtils.FontStyle.Subheader);
            header.TextColor = UIColor.Black.ColorWithAlpha(0.8f);
            header.Text = item.Text;
            header.SizeHeightToFit();
            header.SetInCenterVertical(button);
            button.AddSubview(header);

            if (header.Frame.Bottom + Dimensions.Padding > button.Frame.Height)
                button.SetHeight(header.Frame.Bottom + Dimensions.Padding);

            if (!string.IsNullOrWhiteSpace(item.ExtraText))
            {
                var extra = new UILabel(new CGRect(x, header.Frame.Top, 0, 0));
                extra.SetFont(UniversalUtils.FontStyle.Subheader);
                extra.TextColor = UIColor.Black.ColorWithAlpha(0.5f);
                extra.Text = item.ExtraText;
                extra.SizeToFit();
                button.AddSubview(extra);
                extra.SetRight(button.Frame.Width - Dimensions.Padding);
                header.SetWidth(header.Frame.Width - extra.Frame.Width - Dimensions.PaddingCardsView);
                header.SizeToFit();

                if (header.Frame.Bottom + Dimensions.Padding > button.Frame.Height)
                    button.SetHeight(header.Frame.Bottom + Dimensions.Padding);

                header.SetInCenterVertical(button);
                extra.SetTop(header.Frame.Top);
            }
            else
            {
                header.SetInCenterVertical(button);
            }

            var line = new UIView(new CGRect(0, button.Frame.Height - 0.5f, button.Frame.Width, 0.5f));
            line.BackgroundColor = UIColor.Black.ColorWithAlpha(0.25f);
            button.AddSubview(line);

            button.SetMaterialSelection(() =>
            {
                item.Action();
                Dismiss();
            }, UIColor.Black.ColorWithAlpha(0.1f));
            rootView.AddSubview(button);
            return button.Frame.Bottom;
        }

        public void Dismiss()
        {
            UIView.Animate(0.25, 0, UIViewAnimationOptions.CurveEaseOut, () =>
            {
                _messageView.Alpha = 0.3f;
                _messageView.SetBottom(this.Frame.Height + _messageView.Frame.Height);
            }, () =>
            {
                this.RemoveFromSuperview();
            });
        }

        public void Show(UIWindow window)
        {
            window.Add(this);

            UIView.Animate(0.25, 0, UIViewAnimationOptions.CurveEaseOut, () =>
            {
                _messageView.Alpha = 1f;
                _messageView.SetBottom(this.Frame.Bottom - Dimensions.Padding);
            }, null);
        }
    }
}
