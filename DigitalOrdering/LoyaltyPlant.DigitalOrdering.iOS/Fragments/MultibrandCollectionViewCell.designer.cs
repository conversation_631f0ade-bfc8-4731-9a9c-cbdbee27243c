// WARNING
//
// This file has been generated automatically by Visual Studio to store outlets and
// actions made in the UI designer. If it is removed, they will be lost.
// Manual changes to this file may not be handled correctly.
//
using Foundation;
using System.CodeDom.Compiler;

namespace LoyaltyPlant.DigitalOrdering.iOS.Fragments
{
	[Register ("MultibrandCollectionViewCell")]
	partial class MultibrandCollectionViewCell
	{
		[Outlet]
		UIKit.UILabel _hoursIconLabel { get; set; }

		[Outlet]
		UIKit.UIImageView _multibrandItemImageView { get; set; }

		[Outlet]
		UIKit.UILabel _multibrandItemTitleLabel { get; set; }

		[Outlet]
		UIKit.UILabel _multibrandItemWorkingHoursLabel { get; set; }
		
		void ReleaseDesignerOutlets ()
		{
			if (_hoursIconLabel != null) {
				_hoursIconLabel.Dispose ();
				_hoursIconLabel = null;
			}

			if (_multibrandItemImageView != null) {
				_multibrandItemImageView.Dispose ();
				_multibrandItemImageView = null;
			}

			if (_multibrandItemTitleLabel != null) {
				_multibrandItemTitleLabel.Dispose ();
				_multibrandItemTitleLabel = null;
			}

			if (_multibrandItemWorkingHoursLabel != null) {
				_multibrandItemWorkingHoursLabel.Dispose ();
				_multibrandItemWorkingHoursLabel = null;
			}
		}
	}
}
