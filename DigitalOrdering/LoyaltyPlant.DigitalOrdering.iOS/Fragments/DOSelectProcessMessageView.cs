using CoreGraphics;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Drawing;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.DigitalOrdering.Utils.OrderInfoFields;
using LoyaltyPlant.Texts;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.Messages.iOS;
using UIKit;
using LoyaltyPlant.Core.Analytics;
using System.Diagnostics;

namespace LoyaltyPlant.DigitalOrdering.iOS.Fragments
{
    public class DoSelectProcessMessageView : UIView
    {
        public readonly UIView messageView;
        #region Private Fields
        private readonly string _uuid;
        private List<Model.DOProcess> _processes;
        private IList<ChooserActionItem> _chooserActionItems;
        private Action<string> _ctaAction = null;

        private nfloat _usedDimension = Dimensions.PaddingBig;
        #endregion

        #region Constructors
        private DoSelectProcessMessageView()
        {
            this.SetSize(UIScreen.MainScreen.Bounds.Size);
            BackgroundColor = UIColor.Black.ColorWithAlpha(0.25f);
            this.SetMaterialSelection(Close, UIColor.Clear);

            _processes = DigitalOrderingModule.Instance.GetAvailableProcesses();
        }

        public DoSelectProcessMessageView(string uuid, CallToActionType callToActionType) : this()
        {
            _uuid = uuid;
            _chooserActionItems = DigitalOrderingModule.Instance.Controller.GetCallToActionItems(callToActionType);

            _ctaAction = DigitalOrderingModule.Instance.Controller.GetCTAProcessesAction(callToActionType);

            nfloat y = 0f;
            messageView = CreateMessageView(out y);
            CreateCommonView(y);
        }

        public DoSelectProcessMessageView(IList<ChooserActionItem> chooserActionItems) : this()
        {
            _chooserActionItems = chooserActionItems ?? new List<ChooserActionItem>();

            nfloat y = 0f;
            messageView = CreateMessageView(out y);
            CreateCommonView(y);
        }
        #endregion

        private void CreateCommonView(nfloat y)
        {
            if (_chooserActionItems.Any())
            {
                y = CreateTitleButton(y, I18N.ORDER_TO_A_PREVIOUS_ADDRESS_LABEL, false);
                foreach (var lastAddress in _chooserActionItems)
                {
                    var isLastItemInList = _chooserActionItems.Last() == lastAddress;
                    y = CreateLastAddressOption(y, lastAddress, isLastItemInList);
                }
                y = CreateTitleButton(y, I18N.ORDER_TO_A_NEW_ADDRESS_LABEL, false);
            }

            foreach (var process in _processes)
            {
                y = CreateProcessOption(y, process);
            }

            var cancelView = new UIView(new CGRect(0, y, messageView.Frame.Width, 44f));
            messageView.AddSubview(cancelView);

            messageView.SetHeight(cancelView.Frame.Bottom);
            AddSubview(messageView);
            messageView.SetBottom(Frame.Height + messageView.Frame.Height);

            var cancelText = new UILabel(new CGRect(0, 0, cancelView.Frame.Width, cancelView.Frame.Height))
            {
                TextColor = Colors.MessageboxAccentTextColor,
                TextAlignment = UITextAlignment.Center,
                Font = UniversalUtils.SystemFont(Dimensions.MiddleFont, UniversalUtils.FontType.Semibold),
                Text = I18N.CANCEL
            };
            cancelView.AddSubview(cancelText);
            cancelView.SetMaterialSelection(Close);

            cancelView.IsAccessibilityElement = true;
            cancelView.AccessibilityLabel = I18N.CANCEL;
            cancelView.AccessibilityTraits = UIAccessibilityTrait.Button;
        }

        private nfloat CreateProcessOption(nfloat y, DOProcess process) => CreateOptionButton(y,
            process.GetIconFontSymbol(), process.Title,
            process.Description, () =>
            {
                DigitalOrderingModule.Instance.Controller.OnSelectProcessOnDoSelectionMessage(process);
                _ctaAction?.Invoke(_uuid);
                LpAnalytics.TrackSelectDoProcess(process.Title);
                Close();
            });

        private nfloat CreateLastAddressOption(nfloat y, ChooserActionItem lastAddress, bool isLastItemInList) => CreateLastAddressButton(y,
            lastAddress.OrderType?.GetIconFontSymbol(),
            lastAddress.ExtraText, () =>
            {
                if (string.IsNullOrEmpty(_uuid))
                {
                    lastAddress.EmptyAction?.Invoke();
                }
                else
                {
                    lastAddress.WithUuidAction?.Invoke(_uuid);
                }
                LpAnalytics.TrackSelectDoProcess("previous_address");
                Close();
            }, isLastItemInList);

        private UILabel GetIconLabel(string iconText, bool isAccent) =>
            new UILabel(new CGRect(0, Dimensions.Padding - 3f,
                45f * Dimensions.Multiplier, 45 * Dimensions.Multiplier))
            {
                Font = UIFont.FromName("icon_font", 40f * Dimensions.Multiplier),
                Text = iconText,
                TextColor = isAccent ? Colors.MessageboxAccentTextColor : UIColor.Black.ColorWithAlpha(0.8f),
                IsAccessibilityElement = false
            };

        private UILabel GetHeaderLabel(string headerText, bool isAccent) =>
            new UILabel(new CGRect(0, Dimensions.Padding, 0, 0))
            {
                Lines = 0,
                Font = UniversalUtils.SystemFont(Dimensions.ProcessesDialogSubheaderFont, UniversalUtils.FontType.Normal),
                TextColor = isAccent ? Colors.MessageboxAccentTextColor : UIColor.Black.ColorWithAlpha(0.8f),
                Text = headerText,
                TextAlignment = Settings.IsLtrSelected ? UITextAlignment.Left : UITextAlignment.Right
            };

        private UIView GetLineView(CGRect buttonFrame) =>
            new UIView(new CGRect(_usedDimension, buttonFrame.Height - 0.5f, buttonFrame.Width - 2 * _usedDimension, 0.5f))
            {
                BackgroundColor = UIColor.Black.ColorWithAlpha(0.25f)
            };

        private nfloat CreateOptionButton(nfloat y, string iconText, string headerText, string descText, Action action)
        {
            var button = new UIView(new CGRect(0, y, messageView.Frame.Width, 0));

            var icon = GetIconLabel(iconText, true);
            button.AddSubview(icon);

            var header = GetHeaderLabel(headerText, false);
            button.AddSubview(header);

            var width = button.Frame.Width - icon.Frame.Width - 2 * _usedDimension - Dimensions.PaddingSmall;

            header.SetWidth(width);
            header.SizeHeightToFit();
            
            var desc = new UILabel(new CGRect(0, header.Frame.Bottom + Dimensions.PaddingSmall, 0, 0))
            {
                Lines = 0,
                Font = UniversalUtils.SystemFont(Dimensions.ProcessesDialogDescriptionFont),
                TextColor = UIColor.Black.ColorWithAlpha(0.8f).ColorWithAlpha(0.5f),
                Text = descText,
            };

            desc.SetWidth(width);
            desc.SizeHeightToFit();
            button.AddSubview(desc);

            if (Settings.IsLtrSelected)
            {
                icon.SetLeft(_usedDimension);

                var padding = icon.Frame.Right + Dimensions.PaddingSmall;
                header.SetLeft(padding);
                desc.SetLeft(padding);
            }
            else
            {
                icon.SetRight(messageView.Frame.Width - 2 * Dimensions.PaddingSmall);
                icon.Transform = CGAffineTransform.MakeScale(-1, 1);
                header.SetLeft(_usedDimension);
                desc.SetLeft(_usedDimension);
                desc.TextAlignment = UITextAlignment.Right;
            }

            button.SetHeight(NMath.Max(desc.Frame.Bottom, icon.Frame.Bottom) + Dimensions.Padding);
            button.LayoutIfNeeded();

            var line = GetLineView(button.Frame);
            button.AddSubview(line);

            button.IsAccessibilityElement = true;
            button.AccessibilityLabel = headerText + " " + descText;
            button.AccessibilityTraits = UIAccessibilityTrait.Button;

            button.SetClickAction(action);
            messageView.AddSubview(button);
            return button.Frame.Bottom;
        }

        private nfloat CreateLastAddressButton(nfloat y, string iconText, string headerText, Action action, bool isLastItemInList)
        {
            var button = new UIView(new CGRect(0, y, messageView.Frame.Width, 0));

            var icon = GetIconLabel(iconText, true);
            button.AddSubview(icon);

            var header = GetHeaderLabel(headerText, false);
            button.AddSubview(header);

            var width = button.Frame.Width - icon.Frame.Width - 2 * _usedDimension - Dimensions.PaddingSmall;

            header.SetWidth(width);
            header.SizeHeightToFit();

            if (Settings.IsLtrSelected)
            {
                icon.SetLeft(Dimensions.PaddingBig);

                var padding = icon.Frame.Right + Dimensions.PaddingSmall;
                header.SetLeft(padding);
            }
            else
            {
                icon.SetRight(messageView.Frame.Width - 2 * Dimensions.PaddingSmall);
                icon.Transform = CGAffineTransform.MakeScale(-1, 1);
                header.SetLeft(_usedDimension);
            }

            button.SetHeight(NMath.Max(header.Frame.Bottom, icon.Frame.Bottom) + Dimensions.Padding);
            button.LayoutIfNeeded();
            header.SetInCenterVertical(button);

            if (isLastItemInList)
            {
                var line = GetLineView(button.Frame);
                button.AddSubview(line);
            }

            button.IsAccessibilityElement = true;
            button.AccessibilityLabel = headerText;
            button.AccessibilityTraits = UIAccessibilityTrait.Button;

            button.SetClickAction(action);
            messageView.AddSubview(button);
            return button.Frame.Bottom;
        }

        private nfloat CreateTitleButton(nfloat y, string headerText, bool isAccent)
        {
            var header = new UILabel(new CGRect(_usedDimension, y + Dimensions.Padding,
                messageView.Frame.Width - 2 * _usedDimension, 0))
            {
                Lines = 2,
                Font = UniversalUtils.SystemFont(Dimensions.ProcessesDialogSubheaderFont, UniversalUtils.FontType.Medium),
                TextColor = isAccent ? Colors.MessageboxAccentTextColor : UIColor.Black.ColorWithAlpha(0.8f),
                Text = headerText
            };
            header.SizeHeightToFit();

            if (!Settings.IsLtrSelected)
                header.TextAlignment = UITextAlignment.Right;

            messageView.AddSubview(header);
            return header.Frame.Bottom;
        }

        private UIView CreateMessageView(out nfloat y)
        {
            var messageView = new UIView(new CGRect(_usedDimension, 0, Dimensions.WidthPaddingBig, 0))
            {
                BackgroundColor = UIColor.White,
                ClipsToBounds = true
            };
            messageView.Layer.CornerRadius = Dimensions.CardRadius;

            var headerLabel = new UILabel(new CGRect(_usedDimension, Dimensions.Padding, messageView.Frame.Width - 2 * _usedDimension, 0))
            {
                Lines = 0,
                Text = I18N.DO_PROCESS_CHOOSE_MESSAGE_TITLE_V2,
                TextColor = Colors.MessageboxAccentTextColor,
                Font = UniversalUtils.SystemFont(Dimensions.NewSubheaderFont, UniversalUtils.FontType.Semibold)
            };
            headerLabel.SizeHeightToFit();

            if (!Settings.IsLtrSelected)
                headerLabel.TextAlignment = UITextAlignment.Right;

            messageView.AddSubview(headerLabel);
            y = headerLabel.Frame.Height + Dimensions.Padding;
            return messageView;
        }

        private void Close()
        {
            Animate(0.25, 0, UIViewAnimationOptions.CurveEaseOut, () =>
            {
                messageView.Alpha = 0.3f;
                messageView.SetBottom(Frame.Height + messageView.Frame.Height);
            }, RemoveFromSuperview);
        }
    }
}
