using System;
using System.Collections.Generic;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Payment.iOS;
using LoyaltyPlant.Surveys.iOS;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.DigitalOrdering.iOS.ViewControllers
{
    public class DoSubscriptionCheckoutViewController : BaseSubscriptionCheckoutViewController
    {
        private readonly DOOrder _order;
        private readonly MultiplyActionPreventer _actionPreventer = new MultiplyActionPreventer(1500);

        private List<IFieldView> _list;

        public DoSubscriptionCheckoutViewController()
        {
            _list = new List<IFieldView>();
            _order = DigitalOrderingModule.Instance.CurrentOrder;
        }

        protected override ApplicationScreenType GetApplicationScreenType()
        {
            return ApplicationScreenType.DoSubscriptionCheckoutScreen;
        }

        public override void SetupPrePaymentViews(ref nfloat y)
        {
            ScrollView.Scrolled += (sender, e) =>
            {
                NavigationItem.Title = ScrollView.ContentOffset.Y > 50f ? I18N.CHECKOUT_HEADER : "";
            };
        }

        public override void ViewDidAppear(bool animated)
        {
            base.ViewDidAppear(animated);

            MessagesModule.Instance.ShowNextMessage();
        }

        public override void SetupPostPaymentViews(ref nfloat y)
        {
            if (_order.Form != null)
            {
                var fields = new FieldsView(_order.Form.GetSortedFields(), ScrollView, y, Dimensions.WidthPadding, this, true);
                _list = fields.FieldViews;
                ScrollView.AddSubview(fields);
                y = fields.Frame.Bottom;
            }
        }

        public override void Pay(bool isApplePay = false)
        {
            if (!_actionPreventer.CanUse())
                return;

            foreach (var view in _list)
            {
                view.ResignFirstResponder();
            }

            DigitalOrderingModule.Instance.Controller.Pay();
        }
    }
}

