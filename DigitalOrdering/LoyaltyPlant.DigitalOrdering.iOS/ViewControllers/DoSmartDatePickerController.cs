using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoreAnimation;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Backend.iOS.CustomViews;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Texts;
using UIKit;
using static LoyaltyPlant.DigitalOrdering.Model.DOOrder;

namespace LoyaltyPlant.DigitalOrdering.iOS.ViewControllers
{
    public class DoSmartDatePickerController : BaseViewController
    {
        private readonly string _header;

        private Dictionary<string, DateTime> _dates;
        private Dictionary<string, TimeInterval> _variants;

        private readonly List<UIView> _views = new List<UIView>();

        private UIView _hiddenView;
        private FloatLabeledTextField _datePickerField;
        private FloatLabeledTextField _timePickerField;
        private UIDatePicker _timePickerView;

        private CompleteOrderTypes _type;

        public DoSmartDatePickerController(string header)
        {
            _header = header;
        }

        protected override ApplicationScreenType GetApplicationScreenType()
        {
            return ApplicationScreenType.DoSmartDatePickerScreen;
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();

            SetGradientForBackground(View, Colors.SecondaryBackgroundGradientUpColor, Colors.SecondaryBackgroundGradientDownColor,
                                     Colors.SecondaryBackgroundColor);
            NavigationItem.Title = _header;

            var scrollView = new UIScrollView(View.Bounds);
            View.AddSubview(scrollView);
            scrollView.ShowsVerticalScrollIndicator = false;

            var processType = DigitalOrderingModule.Instance.CurrentOrder.Process.Type;

            nfloat y = 0f;

            var datePickerView = CreateDatePicker(scrollView);
            var timePickerView = CreateTimePicker(scrollView, processType);

            _datePickerField.SetNextCancelKeyboard(_timePickerField);
            _timePickerField.SetNextCancelKeyboard(null);
            _type = DigitalOrderingModule.Instance.CurrentOrder.CompleteOrderType;

            if (!processType.IsDeliveryOrCatering())
            {
                y = CreateCell(y, scrollView, CompleteOrderTypes.Asap, DigitalOrderingModule.Instance.CurrentOrder.GetTimeAsapStr());
                y = CreateCell(y, scrollView, CompleteOrderTypes.Custom, I18N.ORDERING_TIME_LATER);

                _hiddenView = new UIView(new CGRect(0, y - 0.5f, View.Frame.Width, datePickerView.Frame.Height + timePickerView.Frame.Height + Dimensions.PaddingSmall));
                datePickerView.RemoveFromSuperview();
                timePickerView.RemoveFromSuperview();
                scrollView.AddSubview(_hiddenView);

                var padding = new UIView(new CGRect(0, 0, _hiddenView.Frame.Width, Dimensions.PaddingSmall));
                padding.BackgroundColor = Colors.DividerTextColor;
                _hiddenView.AddSubview(padding);

                datePickerView.SetTop(padding.Frame.Bottom);
                timePickerView.SetTop(datePickerView.Frame.Bottom);
                _hiddenView.AddSubviews(datePickerView, timePickerView);

                if (DigitalOrderingModule.Instance.CurrentOrder.CompleteOrderType == CompleteOrderTypes.Asap)
                {
                    _hiddenView.Alpha = 0;
                    _views.ElementAt(0).Alpha = 1f;
                    _views.ElementAt(1).Alpha = 0f;

                    _timePickerField.Text = LpDateTime.TimeToStringCurrentFormat(DigitalOrderingModule.Instance.CurrentOrder.AsapTime);
                    _timePickerView.Date = _timePickerField.Text.ToNsTimeLpStringFormat();
                }
                else
                {
                    _hiddenView.Alpha = 1;
                    _views.ElementAt(0).Alpha = 0f;
                    _views.ElementAt(1).Alpha = 1f;

                    _timePickerField.Text = LpDateTime.TimeToStringCurrentFormat(DigitalOrderingModule.Instance.CurrentOrder.OrderTimeFrom);
                    _timePickerView.Date = _timePickerField.Text.ToNsTimeLpStringFormat();
                }
            }
            else if (processType.IsDeliveryOrCatering())
            {
                datePickerView.SetTop(0f);
                timePickerView.SetTop(datePickerView.Frame.Bottom);
            }
        }

        private UIView CreateDatePicker(UIView pr)
        {
            var view = new UIView(new CGRect(0, 0, Dimensions.Width, 60f));
            var width = view.Frame.Width - 2 * Dimensions.Padding;

            var line = new UIView(new CGRect(Dimensions.Padding, view.Frame.Height - 0.5f, width, 0.5f));
            line.BackgroundColor = Colors.DividerTextColor;
            view.AddSubview(line);

            var label = new UILabel(new CGRect(Dimensions.Padding, 0, width, view.Frame.Height));
            label.Text = I18N.ORDERING_DATE;
            label.TextColor = Colors.DefaultTextColor;
            label.Font = UniversalUtils.SystemFont(Dimensions.BigFont);
            label.Lines = 0;
            view.AddSubview(label);

            _datePickerField = new FloatLabeledTextField(view.Bounds, false);
            _datePickerField.FloatingLabelActiveTextColor = UIColor.Clear;
            _datePickerField.FloatingLabelTextColor = Colors.SecondaryTextColor;
            _datePickerField.border.Hidden = true;
            _datePickerField.SetTop(label.Frame.Top - 10f);
            _datePickerField.TextColor = Colors.SecondaryTextColor;
            _datePickerField.Font = UniversalUtils.SystemFont(Dimensions.BigFont);
            view.AddSubview(_datePickerField);

            if (Settings.IsLtrSelected)
            {
                _datePickerField.SetRight(view.Frame.Width - Dimensions.Padding);
                _datePickerField.TextAlignment = UITextAlignment.Right;
            }
            else
            {
                _datePickerField.SetLeft(Dimensions.Padding);
                _datePickerField.TextAlignment = UITextAlignment.Left;
                label.TextAlignment = UITextAlignment.Right;
            }

            _dates = DigitalOrderingModule.Instance.CurrentOrder.GetDateControlValues(out int selectedDateIndex);
            _datePickerField.CreateListPicker(_dates.Keys.ToList());
            _datePickerField.Text = _dates.ElementAt(selectedDateIndex).Key;
            _datePickerField.Ended += (sender, e) =>
            {
                //TODO: Мне не нравится. Попробовать пересмотреть логику в сторону сравнения аргументов.
                if (!_dates.ContainsKey(I18N.TODAY) || _dates[_datePickerField.Text] != _dates[I18N.TODAY])
                {
                    DigitalOrderingModule.Instance.CurrentOrder.ShowUnavailableEntitiesMessageIfNeeded(new TimeSpan(0, 0, 0),
                        _dates[_datePickerField.Text].DayOfWeek,
                        () =>
                        {
                            SuccessAction();
                        },
                        () =>
                        {
                            _datePickerField.Text = I18N.TODAY;
                        });
                }
                else
                {
                    SuccessAction();
                }
            };

            pr.AddSubview(view);
            return view;

            void SuccessAction()
            {
                if (DigitalOrderingModule.Instance.CurrentOrder.IsDeliveryOrCatering())
                {
                    UpdateTimeIntervalsForDeiveryOrCateringOrder();
                }
                else
                {
                    var currentDate = _dates[_datePickerField.Text];
                    var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;

                    if (currentOrder.IsNeedToGetAsapTimeFromServer())
                        currentOrder.OrderDate = currentDate;
                    else
                        DigitalOrderingModule.Instance.Controller.SetDate(currentDate);

                    _timePickerField.Text = LpDateTime.TimeToStringCurrentFormat(currentDate);
                    _timePickerView = _timePickerField.CreateTimePicker();
                }
            }
        }

        private UIView CreateTimePicker(UIView pr, DoProcessType type)
        {
            var view = new UIView(new CGRect(0, 0, Dimensions.Width, 60f));
            var width = view.Frame.Width - 2 * Dimensions.Padding;

            var line = new UIView(new CGRect(Dimensions.Padding, view.Frame.Height - 0.5f, width, 0.5f));
            line.BackgroundColor = Colors.DividerTextColor;
            view.AddSubview(line);

            var label = new UILabel(new CGRect(Dimensions.Padding, 0, width, view.Frame.Height));
            label.Text = I18N.ORDERING_TIME;
            label.TextColor = Colors.DefaultTextColor;
            label.Font = UniversalUtils.SystemFont(Dimensions.BigFont);
            label.Lines = 0;
            view.AddSubview(label);

            _timePickerField = new FloatLabeledTextField(view.Bounds, false);
            _timePickerField.FloatingLabelActiveTextColor = UIColor.Clear;
            _timePickerField.FloatingLabelTextColor = Colors.SecondaryTextColor;
            _timePickerField.border.Hidden = true;
            _timePickerField.TextColor = Colors.SecondaryTextColor;
            _timePickerField.Font = UniversalUtils.SystemFont(Dimensions.BigFont);
            _timePickerField.SetTop(label.Frame.Top - 10f);
            view.AddSubview(_timePickerField);

            if (Settings.IsLtrSelected)
            {
                _timePickerField.SetRight(view.Frame.Width - Dimensions.Padding);
                _timePickerField.TextAlignment = UITextAlignment.Right;
            }
            else
            {
                _timePickerField.SetLeft(Dimensions.Padding);
                _timePickerField.TextAlignment = UITextAlignment.Left;
                label.TextAlignment = UITextAlignment.Right;
            }

            switch (type)
            {
                case DoProcessType.PickUp:
                case DoProcessType.EatIn:
                    {
                        var asap = DigitalOrderingModule.Instance.CurrentOrder.AsapTime;
                        _timePickerField.Text = LpDateTime.TimeToStringCurrentFormat(asap);
                        _timePickerView = _timePickerField.CreateTimePicker();

                        _timePickerField.Ended += (sender, e) => OnTimeSelectHandlerForPickupOrEatInOrder();
                        break;
                    }

                case DoProcessType.Delivery:
                case DoProcessType.Catering:
                    UpdateTimeIntervalsForDeiveryOrCateringOrder();
                    break;
            }

            pr.AddSubview(view);
            return view;
        }

        private void OnTimeSelectHandlerForPickupOrEatInOrder()
        {
            var date = _dates[_datePickerField.Text];
            var time = _timePickerView.Date.NSDateToDateTime().TimeOfDay;

            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;

            var isAsap = (date == currentOrder.AsapTime.Date) && (time <= currentOrder.AsapTime.TimeOfDay.Add(new TimeSpan(0, 0, 59)));
            currentOrder.CompleteOrderType = isAsap ? CompleteOrderTypes.Asap : CompleteOrderTypes.Custom;

            DigitalOrderingModule.Instance.CurrentOrder.ShowUnavailableEntitiesMessageIfNeeded(
                time,
                date.DayOfWeek,
                async () =>
                {
                    await SetTimeIfTimeIsAvailable(date, time);
                },
                () => { });
        }

        private async Task SetTimeIfTimeIsAvailable(DateTime date, TimeSpan time)
        {
            var isAvailableTime = await OrderTimeHelper.GetAvailableTime(date, time, _type);
            if (isAvailableTime != default)
            {
                BeginInvokeOnMainThread(() =>
                {
                    var currentDateTime = LpDateTime.TimeToStringCurrentFormat(isAvailableTime);

                    _timePickerField.Text = currentDateTime;
                    _timePickerView.Date = currentDateTime.ToNsTimeLpStringFormat();

                });
            }
        }

        private void UpdateTimeIntervalsForDeiveryOrCateringOrder()
        {
            // TODO по идеи лишняя проверка, потому что перед этим методом итак оно проверяется
            // пусть остается на всякий случай
            if (!DigitalOrderingModule.Instance.CurrentOrder.IsDeliveryOrCatering())
                return;

            _variants = DigitalOrderingModule.Instance.CurrentOrder.GetTimeControlValuesForOrder(_dates[_datePickerField.Text], out int selectedTimeInterval);
            _timePickerField.CreateListPicker(_variants.Keys.ToList());
            _timePickerField.Text = _variants.Keys.ElementAt(selectedTimeInterval);

            _timePickerField.Ended += (sender, e) =>
            {
                var selectedDate = _dates[_datePickerField.Text];
                var isAsapDate = selectedDate == DigitalOrderingModule.Instance.CurrentOrder.AsapTime.Date;

                var selectedIndex = ((sender as FloatLabeledTextField).InputView as UIPickerView).SelectedRowInComponent(0);

                // если дата выбрана как асап и первый интервал времени, то асап
                _type = (selectedIndex == 0 && isAsapDate) ? CompleteOrderTypes.Asap : CompleteOrderTypes.Custom;

                TimeSpan time = DigitalOrderingModule.Instance.CurrentOrder.AsapTime.TimeOfDay;

                if (_type == CompleteOrderTypes.Custom)
                {
                    if (!DigitalOrderingModule.Instance.CurrentOrder.IsDeliveryOrCatering())
                        time = _timePickerView.Date.NSDateToDateTime().TimeOfDay;
                    else
                        time = _variants[_timePickerField.Text].StartTime;
                }

                DigitalOrderingModule.Instance.CurrentOrder.ShowUnavailableEntitiesMessageIfNeeded(
                time,
                selectedDate.DayOfWeek,
                () =>
                {
                    DigitalOrderingModule.Instance.CurrentOrder.CompleteOrderType = _type;
                    ChangedTimeValueForDeiveryOrCateringOrder(selectedDate, time);
                },
                () => { });
               
            };
        }

        private async void ChangedTimeValueForDeiveryOrCateringOrder(DateTime selectedDate, TimeSpan selectedTime)
        {
            var currentOrder = DigitalOrderingModule.Instance.CurrentOrder;
            if (currentOrder.CompleteOrderType == DOOrder.CompleteOrderTypes.Asap)
            {
                if (currentOrder.IsNeedToGetAsapTimeFromServer())
                {
                    var precheck = await DigitalOrderingModule.Instance.Controller.GetPrecheckResponse(isRunAsFirstTime: true);

                    if (!precheck.IsPrecheckSuccess)
                    {
                        await DigitalOrderingModule.Instance.Controller.ResolveProblemAfterWrongPrecheck(precheck);
                        LpLogger.LOG_W("Precheck not pass for select DateTime");
                        return;
                    }
                }

                selectedDate = currentOrder.AsapTime.Date;
                selectedTime = currentOrder.AsapTime.TimeOfDay;
            }

            var result = DigitalOrderingModule.Instance.Controller.SetDateTimeAndShowErrorMessageIfNeeded(selectedDate, selectedTime);

            if (result)
            {
                // TODO не придумала что тут делать :)
            }
        }

        private nfloat CreateCell(nfloat y, UIView pr, CompleteOrderTypes itemType, string itemTitle)
        {
            var view = new UIView(new CGRect(0, y, Dimensions.Width, 60f));
            var width = view.Frame.Width - 2 * Dimensions.Padding;

            var line = new UIView(new CGRect(Dimensions.Padding, view.Frame.Height - 0.5f, width, 0.5f));
            line.BackgroundColor = Colors.DividerTextColor;
            view.AddSubview(line);

            var label = new UILabel(new CGRect(Dimensions.Padding, 0, width, view.Frame.Height));
            label.Text = itemTitle;
            label.TextColor = Colors.DefaultTextColor;
            label.Font = UniversalUtils.SystemFont(Dimensions.BigFont);
            label.Lines = 0;
            view.AddSubview(label);

            var checkElement = new UIView(new CGRect(0, 0, 30, view.Frame.Height));
            checkElement.Alpha = 0f;

            var group = new CGRect(0, 0, 30, 30);
            var bezierPath = new UIBezierPath();
            bezierPath.MoveTo(new CGPoint(group.GetMinX() + 0.27083 * group.Width, group.GetMinY() + 0.54167 * group.Height));
            bezierPath.AddLineTo(new CGPoint(group.GetMinX() + 0.41667 * group.Width, group.GetMinY() + 0.68750 * group.Height));
            bezierPath.AddLineTo(new CGPoint(group.GetMinX() + 0.75000 * group.Width, group.GetMinY() + 0.35417 * group.Height));
            bezierPath.LineCapStyle = CGLineCap.Square;
            bezierPath.Stroke();

            var lines = new CAShapeLayer();
            lines.Path = bezierPath.CGPath;
            lines.Bounds = group;
            lines.StrokeColor = Colors.AccentColor.CGColor;
            lines.FillColor = UIColor.Clear.CGColor;
            lines.LineWidth = 2f;
            lines.Position = new CGPoint(group.Width / 2.0, group.Height / 2.0);
            lines.AnchorPoint = new CGPoint(.5, .5);

            var view1 = new UIView(group);
            _views.Add(checkElement);
            view1.Layer.AddSublayer(lines);
            view1.SetTop(checkElement.Frame.Height / 2 - view1.Frame.Height / 2);
            checkElement.SetRight(view.Frame.Width - Dimensions.Padding);
            checkElement.AddSubview(view1);
            view.AddSubview(checkElement);

            if (Settings.IsLtrSelected)
                checkElement.SetRight(view.Frame.Width - Dimensions.Padding);
            else
            {
                checkElement.SetLeft(Dimensions.Padding);
                label.TextAlignment = UITextAlignment.Right;
            }

            view.SetMaterialSelection(() =>
            {
                foreach (var v in _views)
                    if (v != checkElement)
                        v.Alpha = 0f;

                if (checkElement.Alpha <= 0f)
                {
                    UIView.Animate(0.15, () => { checkElement.Alpha = 1; }, null);
                    if (_views.IndexOf(checkElement) == _views.Count - 1)
                    {
                        if (_hiddenView != null)
                            _hiddenView.Alpha = 1f;
                        _timePickerField.ResignFirstResponder();
                        _datePickerField.ResignFirstResponder();
                    }
                    else
                    {
                        if (_hiddenView != null)
                            _hiddenView.Alpha = 0f;
                        _timePickerField.ResignFirstResponder();
                        _datePickerField.ResignFirstResponder();
                    }

                    _type = itemType;
                    if (_type == CompleteOrderTypes.Asap)
                    {
                        DigitalOrderingModule.Instance.CurrentOrder.ShowUnavailableEntitiesMessageIfNeeded(
                            DigitalOrderingModule.Instance.CurrentOrder.AsapTime.TimeOfDay,
                            DigitalOrderingModule.Instance.CurrentOrder.AsapTime.DayOfWeek,
                            () =>
                            {
                                UpdateAsapTime(label);
                            },
                            () => { });
                    }
                }

            }, UIColor.Clear);

            pr.AddSubview(view);
            return view.Frame.Bottom;
        }

        // кажется что тут _type всегда ASAP должен быть судя по названию метода ?
        // _type учитывается там внутри тока в запросе deliveryFee...
        private void UpdateAsapTime(UILabel label)
        {
            BeginInvokeOnMainThread(async () =>
            {
                var date = _dates[_datePickerField.Text];
                var time = _timePickerView.Date.NSDateToDateTime().TimeOfDay;

                var availableTime = await OrderTimeHelper.GetAvailableTime(date, time, _type);
                if (availableTime != default)
                {
                    var currentDateTime = LpDateTime.TimeToStringCurrentFormat(availableTime);
                    _timePickerField.Text = currentDateTime;
                    _timePickerView.Date = currentDateTime.ToNsTimeLpStringFormat();
                    label.Text = DigitalOrderingModule.Instance.CurrentOrder.GetTimeAsapStr();
                }

                // костыль "на всякий случай" чтобы точно ASAP тип заказа был (иначе он не всегда асап)
                if (DigitalOrderingModule.Instance.CurrentOrder != null)
                    DigitalOrderingModule.Instance.CurrentOrder.CompleteOrderType = CompleteOrderTypes.Asap;
            });
        }
    }
}
