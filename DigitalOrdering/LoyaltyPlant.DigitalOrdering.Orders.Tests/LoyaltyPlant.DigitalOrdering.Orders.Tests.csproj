<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netcoreapp6.0</TargetFramework>
        <IsPackable>false</IsPackable>
        <!--workaround for windows https://github.com/dotnet/project-system/issues/3568#issuecomment-465323579-->
        <DisableFastUpToDateCheck>true</DisableFastUpToDateCheck>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="coverlet.msbuild" Version="2.8.0">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="xunit" Version="2.4.1" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.1" />
        <DotNetCliToolReference Include="dotnet-xunit" Version="2.3.1" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\Base\LoyaltyPlant.Base.Tests\LoyaltyPlant.Base.Tests.csproj" />
      <ProjectReference Include="..\..\Base\LoyaltyPlant.CoreImpl\LoyaltyPlant.CoreImpl.csproj" />
      <ProjectReference Include="..\LoyaltyPlant.DigitalOrdering.Tests\LoyaltyPlant.DigitalOrdering.Tests.csproj" />
    </ItemGroup>
    <ItemGroup>
      <None Update="StubCachedOrders\do_orders_1">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
