using System;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using Microsoft.VisualStudio.TestPlatform.ObjectModel;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class ParsingDoEnums
    {
        [Fact]
        public void DriveThru_Parsing_NoAttribute()
        {
            var jsonSource = $"{{ KakaytoHernya: \"fgregwegtw\" }}";
            var defaultRoot = JObject.Parse(jsonSource);
            var district = new DODistrict(0);
            district.ShowDriveThruMessage = !defaultRoot.Value<bool>("driveThruEnabled");
            Assert.True(district.ShowDriveThruMessage);
        }

        [Fact]
        public void TestParseDate()
        {
            var source = "{\"lastModified\": \"2017-10-17T09:04:11.264Z\"}";
            var jsonObj = JObject.Parse(source);   
            var doMenu = new DoMenu(new MenuIdentificator(141, 1441, DoProcessType.Catering));
            doMenu.LastModified = jsonObj.Value<DateTime>("lastModified");
            Assert.Equal(new DateTime(2017, 10, 17, 9, 4, 11, 264), doMenu.LastModified);
        }

        [Fact]
        public void ConvertDateTimeToStr()
        {
            var dateTime = new DateTime(2017, 10, 17, 9, 4, 11, 264);
            var obj = dateTime.ToString(DigitalOrderingModule.IsoDateTimeFormat);
            Assert.Equal("2017-10-17T09:04:11.264000Z", obj);
        }
        
        [Theory]
        [InlineData("DELIVERY", DoProcessType.Delivery)]
        [InlineData("PICKUP", DoProcessType.PickUp)]
        [InlineData("EAT_IN", DoProcessType.EatIn)]
        public void Parse_ProcessType(string fromJsonString, DoProcessType exceptedType)
        {
            var jsonSource = $"{{ type: \"{fromJsonString}\" }}";
            var defaultRoot = JObject.Parse(jsonSource);
            var menuType = defaultRoot.GetObject<DoProcessType>("type");
            Assert.Equal(menuType, exceptedType);
        }

        [Theory]
        [InlineData("DELIVERY", DoProcessType.Delivery)]
        [InlineData("PICKUP", DoProcessType.PickUp)]
        [InlineData("EAT_IN", DoProcessType.EatIn)]
        public void ProcessType_To_String_Json(string exceptedText, DoProcessType processType)
        {
            string jsonResultText = JToken.FromObject(processType).ToString();
            Assert.Equal(exceptedText, jsonResultText);
        }
        
        [Theory]
        [InlineData("delivery", DoProcessType.Delivery)]
        [InlineData("pickUp", DoProcessType.PickUp)]
        [InlineData("eatIn", DoProcessType.EatIn)]
        [InlineData("catering", DoProcessType.Catering)]
        public void ProcessType_To_String(string exceptedText, DoProcessType processType)
        {
            var resultText = processType.GetQueryName();
            Assert.Equal(exceptedText, resultText);
        }
    }
}