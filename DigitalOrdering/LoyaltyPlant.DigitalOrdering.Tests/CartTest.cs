using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.Tests.Utils;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Partner.Utils;
using Xunit;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    // Naming convension
    // UnitOfWork_StateUnderTest_ExpectedBehavior]
    
    
    public class CartTest : DigitalOrderingTest
    {
        private const string RadioGroupModifierId = "RadioGroupModifier1";

        public CartTest()
        {
            SetCurrentOrder(12, 12);
        }

        [Fact]
        public void Categories_ItemsInCart_CountItemsInCategory()
        {
            var tree = new TreeNode("Root")
            {
                new TreeNode("Category 1")
                {
                    new TreeNode("Category 2")
                    {
                        new TreeNode("Item 1"),
                        new TreeNode("Item 2"),
                    },
                    new TreeNode("Category 3")
                    {
                        new TreeNode("Item 3"),
                        new TreeNode("Item 4"),
                    },
                },
            };
            
            DoMenu.BuildMenu(tree);

            DoModule.CurrentOrder.AddToCart("Item 1");

            var category2 = DoMenu.GetCategory("Category 2");
            Assert.Equal(1, category2.GetFullCount());
            
            var category1 = DoMenu.GetCategory("Category 1");
            Assert.Equal(1, category1.GetFullCount());
            
            DoModule.CurrentOrder.AddToCart("Item 3");
            Assert.Equal(2, category1.GetFullCount());
        }

        [Theory]
        [InlineData(true, 10D, 10D)]
        [InlineData(false, 10D, 12D)]
        public void CurrentOrder_TotalTaxIncludedInPrice_TotalIncludeTax(bool taxIncludedInPrice, decimal subtotal, decimal total)
        {
            var menuItem1 = new MenuPositionData()
            {
                Id = "menuItem1",
                Prices = {Price = 5.0m}
                
            };

            var menuItem2 = new MenuPositionData()
            {
                Id = "menuItem2",
                Prices = {Price = 5.0m}
                
            };

            var menuItems = new List<MenuItemData>
            {
                menuItem1,
                menuItem2
            };
            DoMenu.SetMenuItems(menuItems);
            DoMenu.MetaData.MetaDataSettings.TaxIncludedInPrice = taxIncludedInPrice;

            // add all exists items in cart
            foreach (var menuItem in DoMenu.MenuItems)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(menuItem));
            }

            DigitalOrderingModule.Instance.CurrentOrder.Tax = 2m;
            
            Assert.Equal(subtotal, DigitalOrderingModule.Instance.CurrentOrder.GetSubtotal());
            Assert.Equal(2m, DigitalOrderingModule.Instance.CurrentOrder.GetTax());
            Assert.Equal(total, DigitalOrderingModule.Instance.CurrentOrder.GetTotal(0));
        }
        
        [Fact]
        public void TestModifiersSort()
        {
            var modifiersMenuList = new List<ModifierData>
            {
                new ModifierData
                {
                    Prices = {Price = 0},
                    Id = "1"
                },
                new ModifierData
                {
                    Prices = {Price = 0.1m},
                    Id = "2"
                },
                new ModifierData
                {
                    Prices = {Price = 1.0m},
                    Id = "3"
                },
                new ModifierData
                {
                    Prices = {Price = 5},
                    Id = "4"
                }
            };

            DoMenu.SetModifiers(modifiersMenuList);

            var radioGroupModifier = new GroupModifierData
            {
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",
                        SortOrder = 1
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                        SortOrder = 3
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "3",
                        SortOrder = 2
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "4",
                        SortOrder = 0
                    },
                }
            };

            var radioGroupModifierDao = new GroupModifier(radioGroupModifier, DoMenu.MenuIdentificator);
            var cartGroupModifier = new CartGroupModifier(radioGroupModifierDao);
            cartGroupModifier.CartModifiers.Sort(Comparators.PriorityComparatorPrice);
            {
                var listIds = cartGroupModifier.CartModifiers.Select(item => item.Id).ToArray();
                var exceptedListIds = new[] {"1", "2", "3", "4"};
                Assert.Equal(exceptedListIds, listIds);
            }
            {
                var listIds = radioGroupModifierDao.Modifiers.Select(item => item.Id).ToArray();
                var exceptedListIds = new[] {"4", "1", "3", "2"};
                Assert.Equal(exceptedListIds, listIds);
            }
            
        }

        [Fact]
        public void Sort_GroupModifiers_ServerSort()
        {
            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Id = "1"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Id = "2"
            });
            
            DoMenu.SetModifiers(modifiersMenuList);

            var groupModifiers = new List<GroupModifierData>();
            for (int i = 0; i < 6; i++)
            {
                var groupModifier = new GroupModifierData
                {
                    SortOrder = i,
                    Id = i.ToString(),
                    ModifierSettings = new List<GroupModifierData.ModifierSetting>
                    {
                        new GroupModifierData.ModifierSetting
                        {
                            ModifierId = "1",
                        },
                        new GroupModifierData.ModifierSetting
                        {
                            ModifierId = "2",
                        },
                    }
                };
                groupModifiers.Add(groupModifier);
            }

            const string menuItemId = "1";

            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                GroupModifiers = groupModifiers,
            };
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);

            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier("2"), DoMenu.GetModifier("1"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier("0"), DoMenu.GetModifier("1"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier("3"), DoMenu.GetModifier("1"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier("5"), DoMenu.GetModifier("1"));

            var gmIds = cartMenuItem.CartGroupModifiers.Where(gm => gm.CountOfSelectedModifiers > 0)
                .Select(gm => gm.Id);

            Assert.Equal(gmIds.ToArray(), new []{"0", "2", "3", "5"});
        }

        [Fact]
        public void MenuItem_GmRequired_FormattedPrice()
        {            
            var modifiersMenuList = new List<ModifierData>();

            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 1.0m},
                Id = "1"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 2.0m},
                Id = "2"
            });

            DoMenu.SetModifiers(modifiersMenuList);

            var radioGroupModifier = new GroupModifierData
            {
                Id = "MultyGroupModifier",
                Min = {Quantity = 1},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",                        
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                    },
                }
            };

            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = {Price = 5},
                GroupModifiers = new List<GroupModifierData>
                {
                    radioGroupModifier,
                }
            };

            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);

            var item = DoMenu.GetMenuItem(menuItemId);
            Assert.Equal(item.FormattedPrice, DigitalOrderingTexts.DoFromPrice(Formatter.FormatAmount(6)));
        }

        [Fact]
        public async Task TestAddToCartMenuItem()
        {
            var modifiersMenuList = new List<ModifierData>();

            modifiersMenuList.Add(new ModifierData
            {
                Id = "1"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Id = "2"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Id = "3"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Id = "4"
            });
            
            DoMenu.SetModifiers(modifiersMenuList);

            var radioGroupModifier = new GroupModifierData
            {
                Id = RadioGroupModifierId,
                Max = {Quantity = 1},
                Min = {Quantity = 1},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "3",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "4",
                    },
                }
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                GroupModifiers = new List<GroupModifierData>
                {
                    radioGroupModifier,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);

            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            
            var cartMenuItem = new CartMenuItem(doMenuItem);

            var result = await DigitalOrderingModule.Instance.AddCartItem(cartMenuItem);
            Assert.False(result);
            var gm = doMenuItem.GetGroupModifier(RadioGroupModifierId);
            cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("1"));
            
            result = await DigitalOrderingModule.Instance.AddCartItem(cartMenuItem);
            Assert.True(result);
        }
        
        [Fact]
        public void TestSelectRadioModifier()
        {
            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0},
                Id = "1"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.1m},
                Id = "2"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 1.0m},
                Id = "3"
            });
            modifiersMenuList.Add(new ModifierData()
            {
                Prices = {Price = 5},
                Id = "4"
            });
            
            DoMenu.SetModifiers(modifiersMenuList);

            var radioGroupModifier = new GroupModifierData()
            {
                Id = RadioGroupModifierId,
                Max = {Quantity = 1},
                Min = {Quantity = 1},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "3",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "4",
                    },
                }
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData()
            {
                Id = menuItemId,
                GroupModifiers = new List<GroupModifierData>
                {
                    radioGroupModifier,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);

            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            
            var cartMenuItem = new CartMenuItem(doMenuItem);
            
            var gm = doMenuItem.GetGroupModifier(RadioGroupModifierId);
            Assert.True(cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("1")));
            Assert.Equal(1, cartMenuItem.GetModifiersCountInModifiersGroup(RadioGroupModifierId));
            Assert.True(cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("2")));
            Assert.Equal(1, cartMenuItem.GetModifiersCountInModifiersGroup(RadioGroupModifierId));
        }
        
        [Fact]
        public void TestSelectMultyModifier()
        {
            const string multyGroupModifierId = "MultyGroupModifier2";
            
            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0},
                Id = "1"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.1m},
                Id = "2"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 1.0m},
                Id = "3"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 5},
                Id = "4"
            });
            
            DoMenu.SetModifiers(modifiersMenuList);
            
            var multyGroupModifier = new GroupModifierData
            {
                Id = multyGroupModifierId,
                Max = {Quantity = 2},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "3",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "4",
                    },
                }
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData()
            {
                Id = menuItemId,
                GroupModifiers = new List<GroupModifierData>
                {
                    multyGroupModifier,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);

            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            
            var cartMenuItem = new CartMenuItem(doMenuItem);

            var gm = doMenuItem.GetGroupModifier(multyGroupModifierId);
            Assert.True(cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("1")));
            Assert.True(cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("2")));
            Assert.False(cartMenuItem.SelectModifier(gm, DoMenu.GetModifier("3")));
            Assert.Equal(2, cartMenuItem.GetModifiersCountInModifiersGroup(multyGroupModifierId));
        }

        [Fact]
        public void TestCalculateSumCartItem()
        {
            const string multiGroupModifierId = "MultyGroupModifier2";
            const string radioGroupModifierId = "radioGroupModifier";
            
            var multiGroupModifier = new GroupModifierData()
            {
                Id = multiGroupModifierId,
                Max = {Quantity = 2},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                }
            };

            var modifiersMenuList = new List<ModifierData>
            {
                new ModifierData
                {
                    Prices = {Price = 0},
                    Id = "11"
                },
                new ModifierData
                {
                    Prices = {Price = 0},
                    Id = "14"
                },
                new ModifierData
                {
                    Prices = {Price = 1.0m},
                    Id = "12"
                },
                new ModifierData
                {
                    Prices = {Price = 1.0m},
                    Id = "13"
                }
            };

            DoMenu.SetModifiers(modifiersMenuList);
            
            var radioGroupModifier = new GroupModifierData
            {
                Id = radioGroupModifierId,
                Max = {Quantity = 1},
                Min = {Quantity = 1},
                Free = {Quantity = 1},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                        SortOrder = 1
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                        SortOrder = 3
                    },
                }
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = {Price = 3},
                GroupModifiers = new List<GroupModifierData>
                {
                    multiGroupModifier,
                    radioGroupModifier,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);
            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);

            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multiGroupModifierId), DoMenu.GetModifier("11"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multiGroupModifierId), DoMenu.GetModifier("14"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(radioGroupModifierId), DoMenu.GetModifier("12"));
            
            Assert.Equal(3, cartMenuItem.GetTotalPrice());
        }
        
        [Fact]
        public void SumCartItem_FreeQuantityGroupModifier()
        {
            const string multyGroupModifierId = "MultyGroupModifier2";
            const string radioGroupModifierId = "radioGroupModifier";
            
            var groupModifier1 = new GroupModifierData
            {
                Id = multyGroupModifierId,
                Free = {Quantity = 1},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                }
            };
            
            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.4m},
                Id = "11"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.5m},
                Id = "12"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.5m},
                Id = "13"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = {Price = 0.5m},
                Id = "14"
            });
            
            DoMenu.SetModifiers(modifiersMenuList);
            
            var groupModifier2 = new GroupModifierData
            {
                Id = radioGroupModifierId,
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                }
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = {Price = 4},
                GroupModifiers = new List<GroupModifierData>
                {
                    groupModifier1,
                    groupModifier2,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);
            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);

            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multyGroupModifierId), DoMenu.GetModifier("12"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(radioGroupModifierId), DoMenu.GetModifier("13"));
            
            Assert.Equal(4.5m, cartMenuItem.GetTotalPrice());
        }        
        [Fact]
        public void SubTotal_GroupModifierWithFreeQuantity()
        {
            const string multyGroupModifierId1 = "MultyGroupModifier1";
            const string multyGroupModifierId2 = "MultyGroupModifier2";
            const string multyGroupModifierId3 = "MultyGroupModifier3";
            
            var groupModifier1 = new GroupModifierData
            {
                Id = multyGroupModifierId1,
                Free = {Quantity = 5},
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "1",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "2",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "3",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "4",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "5",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "6",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "7",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "8",
                    },
                },
                SortOrder = 122,
            };

            var modifiersMenuList = new List<ModifierData>
            {
                //groupModifier1
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "1"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "2"
                },
                new ModifierData
                {
                    Prices = {Price = 7m},
                    Id = "3"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "4"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "5"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "6"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "7"
                },
                new ModifierData
                {
                    Prices = {Price = 0.25m},
                    Id = "8"
                },
                
                // groupModifier3
                new ModifierData
                {
                    Prices = {Price = 0.0m},
                    Id = "11"
                },
                
                // groupModifier2
                new ModifierData
                {
                    Prices = {Price = 0.0m},
                    Id = "12"
                },
                new ModifierData
                {
                    Prices = {Price = 0.0m},
                    Id = "13"
                },
                new ModifierData
                {
                    Prices = {Price = 0.5m},
                    Id = "14"
                }
            };

            DoMenu.SetModifiers(modifiersMenuList);
            
            var groupModifier2 = new GroupModifierData
            {
                Id = multyGroupModifierId2,
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                },
                SortOrder = 119,
            }; 
            
            var groupModifier3 = new GroupModifierData
            {
                Id = multyGroupModifierId3,
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                    },
                },
                SortOrder = 120,
            };
            
            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = {Price = 8.5m},
                GroupModifiers = new List<GroupModifierData>
                {
                    groupModifier1,
                    groupModifier2,
                    groupModifier3,
                }
            };
            
            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);
            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);

            foreach (var groupModifier in doMenuItem.GroupModifiers)
            {
                foreach (var modifier in groupModifier.Modifiers)
                {
                    cartMenuItem.SelectModifier(groupModifier, modifier);
                    cartMenuItem.GetTotalPrice();
                }
            }
            
            // 8.5 + 0.5 + 0.25 + 0.25 + 7 = 16.5
            Assert.Equal(16.5m, cartMenuItem.GetTotalPrice());
        }

        [Fact]
        public void SumCartItem_FreePriceGroupModifier_TotalPrice()
        {
            const string multyGroupModifierId = "MultyGroupModifier1";

            var groupModifier1 = new GroupModifierData
            {
                Id = multyGroupModifierId,
                Free = { CostMoney = 1.5m },
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                }
            };

            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "11"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 7.5m },
                Id = "12"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.0m },
                Id = "10"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.0m },
                Id = "9"
            });

            DoMenu.SetModifiers(modifiersMenuList);

            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = { Price = 7.5m },
                GroupModifiers = new List<GroupModifierData>
                {
                    groupModifier1,
                }
            };

            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);
            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);

            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multyGroupModifierId), DoMenu.GetModifier("11"));
            cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multyGroupModifierId), DoMenu.GetModifier("12"));

            Assert.Equal(15.0m, cartMenuItem.GetTotalPrice());
            Assert.Equal(15.0m, cartMenuItem.GetTotalPrice());
        }
        
        [Fact]
        public void SumCartItem_FreePriceEqualsModifiersPrice_TotalPrice()
        {
            const string multyGroupModifierId = "MultyGroupModifier1";

            var groupModifier1 = new GroupModifierData
            {
                Id = multyGroupModifierId,
                Free = { CostMoney = 1.5m },
                ModifierSettings = new List<GroupModifierData.ModifierSetting>
                {
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "7",
                        Selected = true,
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "8",
                        Selected = true,
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "9",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "10",
                        Selected = true,
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "11",
                        Selected = true,
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "12",
                        Selected = true
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "13",
                    },
                    new GroupModifierData.ModifierSetting
                    {
                        ModifierId = "14",
                    },
                }
            };

            var modifiersMenuList = new List<ModifierData>();
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "7"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "8"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "9"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "10"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "11"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "12"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "13"
            });
            modifiersMenuList.Add(new ModifierData
            {
                Prices = { Price = 0.25m },
                Id = "14"
            });

            DoMenu.SetModifiers(modifiersMenuList);

            const string menuItemId = "abracadabra";
            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = { Price = 8.5m },
                GroupModifiers = new List<GroupModifierData>
                {

                    groupModifier1,
                }
            };

            var menuItems = new List<MenuItemData>();
            menuItems.Add(menuItem);
            DoMenu.SetMenuItems(menuItems);
            var doMenuItem = DoMenu.GetMenuItem(menuItemId);
            var cartMenuItem = new CartMenuItem(doMenuItem);

            Assert.True(cartMenuItem.SelectModifier(doMenuItem.GetGroupModifier(multyGroupModifierId), DoMenu.GetModifier("9")));
            Assert.Equal(6, cartMenuItem.GetCartGroupModifier(multyGroupModifierId).SelectedModifiers.Count);
            Assert.Equal(8.5m, cartMenuItem.GetTotalPrice());
            Assert.Equal(8.5m, cartMenuItem.GetTotalPrice());
        }

        [Fact(DisplayName = "Remove menu items when we have it in cart.")]
        public void Cart_RemovedItemFromMenu_RemoveItFromCart()
        {
            const string menuItemId1 = "1";
            const string menuItemId2 = "2";
            const string menuItemId3 = "3";

            var menuItems = new List<MenuItemData>
            {
                new MenuPositionData
                {
                    Id = menuItemId1,
                },
                new MenuPositionData
                {
                    Id = menuItemId2,
                },
                new MenuPositionData
                {
                    Id = menuItemId3,
                },
            };
            DoMenu.SetMenuItems(menuItems);

            foreach (var menuItem in DoMenu.MenuItems)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(menuItem));
            }

            menuItems.RemoveAt(0);
            DoMenu.SetMenuItems(menuItems);
            
            DigitalOrderingModule.Instance.CurrentOrder.Cart.DeleteRemovedItems();

            var cartEntities = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities;
            Assert.Equal(2, cartEntities.Count());
            Assert.Equal(menuItemId2, cartEntities.ElementAt(0).Id);
            Assert.Equal(menuItemId3, cartEntities.ElementAt(1).Id);
        }

        [Fact(DisplayName = "APP-1946 Фича Приветственный подарок после валидации не работает в доставке")]
        public async Task StartPresent_CanUseStartPresentNowIsFalse_CantAddToCart()
        {
            const string menuItemId = "2";
            const int startPresentInt = 1;
            
            var presents = new List<Present>();
            {
                var presentId = startPresentInt;
                var present = new Present(presentId, 1000, "preview tex " + presentId, 10, presentId, "", "short description " + presentId)
                {
                    DigitalOrderingLinkedItems = new List<DigitalOrderingLinkedItem>
                    {
                        new DigitalOrderingLinkedItem(menuItemId, Present.OrderingTypes.OnlyDigitalOrdering),
                    },
                };
                present.SetPriceInPoints(0);
                presents.Add(present);
            }

            var loyaltyModel = presents.PutToLoyaltyModel();
            loyaltyModel.StartPresentId = startPresentInt;
            loyaltyModel.CanUseStartPresentNow = false;
            loyaltyModel.CantUseStartPresentReason = "Some reason";

            var menuItems = new List<MenuItemData>
            {
                new MenuPositionData
                {
                    Id = menuItemId,
                },
            };
            DoMenu.SetMenuItems(menuItems);
            DoMenu.CreateRootGiftCategory();
            DoMenu.CreateLinkedGifts();
            
            var category = DoMenu.GetCategory(DoMenu.RootGiftCategoryId);
            var giftPosition = category.GetVisibleMenuItems().First() as GiftPosition;
            var result = await DigitalOrderingModule.Instance.AddCartItem(new CartMenuItem(giftPosition));
            Assert.False(result);
        }

        [Fact(DisplayName = "MD-31375 Если к приветственному подарку привязано несколько айтемов, можно получить их все")]
        public async Task Cart_GiftChilds_CantAddSecondGift()
        {
            const string menuItemId2 = "2";
            const string menuItemId3 = "3";
            
            var presents = new List<Present>();
            {
                var presentId = 1;
                var present = new Present(presentId, 1000, "preview tex " + presentId, 10, presentId, "", "short description " + presentId)
                {
                    DigitalOrderingLinkedItems = new List<DigitalOrderingLinkedItem>
                    {
                        new DigitalOrderingLinkedItem(menuItemId2, Present.OrderingTypes.OnlyDigitalOrdering),
                        new DigitalOrderingLinkedItem(menuItemId3, Present.OrderingTypes.OnlyDigitalOrdering),
                    },
                    MoveToHiddenAfterUse = true,
                };
                present.SetPriceInPoints(0);
                presents.Add(present);
            }

            presents.PutToLoyaltyModel();

            var menuItems = new List<MenuItemData>
            {
                new MenuPositionData
                {
                    Id = menuItemId2,
                },
                new MenuPositionData
                {
                    Id = menuItemId3,
                },
            };
            DoMenu.SetMenuItems(menuItems);
            DoMenu.CreateRootGiftCategory();
            DoMenu.CreateLinkedGifts();

            var category = DoMenu.GetCategory(DoMenu.RootGiftCategoryId);
            
            Assert.Single(category.GetVisibleMenuItems());
            var giftPosition = category.GetVisibleMenuItems().First() as GiftPosition;
            Assert.Equal(2, giftPosition.ChildPositions.Count);

            var menuItem1 = giftPosition.ChildPositions[0];
            var result = await DigitalOrderingModule.Instance.AddCartItem(new CartMenuItem(menuItem1));
            Assert.True(result);
            
            var menuItem2 = giftPosition.ChildPositions[1];
            result = await DigitalOrderingModule.Instance.AddCartItem(new CartMenuItem(menuItem2));
            Assert.False(result);
        }

        [Fact(DisplayName = "Present from Sync state in hidden, when cart has MenuItem linked with this present\n" +
                            "https://youtrack.loyaltyplant.com/issue/MD-18115")]
        public void Cart_StateLinkedGiftHidden_RemoveItFromCart()
        {
            const string menuItemId2 = "2";
            const string menuItemId3 = "3";
            
            var presents = new List<Present>();
            {
                var presentId = 1;
                var present = new Present(presentId, 1000, "preview tex " + presentId, 10, presentId, "", "short description " + presentId)
                {
                    DigitalOrderingLinkedItems = new List<DigitalOrderingLinkedItem>
                    {
                        new DigitalOrderingLinkedItem(menuItemId3, Present.OrderingTypes.OnlyDigitalOrdering),
                    }
                };
                present.SetPriceInPoints(0);
                presents.Add(present);
            }

            presents.PutToLoyaltyModel();

            var menuItems = new List<MenuItemData>
            {
                new MenuPositionData
                {
                    Id = menuItemId2,
                },
                new MenuPositionData
                {
                    Id = menuItemId3,
                },
            };
            DoMenu.SetMenuItems(menuItems);
            DoMenu.CreateLinkedGifts();

            foreach (var menuItem in DoMenu.MenuItems)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(menuItem));
            }

            LoyaltyModule.Instance.Presents[0].State = Card.States.Hidden;
            
            Assert.Equal(3, DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities.Count());

            DigitalOrderingModule.Instance.CurrentOrder.Cart.DeleteRemovedItems();
            
            var cartEntities = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities;
            Assert.Equal(2, cartEntities.Count());
        }
        
        [Fact(DisplayName = "MD-31971 Подарок вываливается из корзины при перезаходе в ДО")]
        public void Cart_GiftInCart_AlsoHaveInCart()
        {
            const string menuItemId3 = "3";
            
            var presents = new List<Present>();
            {
                var presentId = 1;
                var present = new Present(presentId, 1000, "preview tex " + presentId, 10, presentId, "", "short description " + presentId)
                {
                    DigitalOrderingLinkedItems = new List<DigitalOrderingLinkedItem>
                    {
                        new DigitalOrderingLinkedItem(menuItemId3, Present.OrderingTypes.OnlyDigitalOrdering),
                    }
                };
                present.SetPriceInPoints(0);
                presents.Add(present);
            }

            presents.PutToLoyaltyModel();
            
            var menuItems = new List<MenuItemData>
            {
                new MenuPositionData
                {
                    Id = menuItemId3,
                },
            };
            DoMenu.SetMenuItems(menuItems);
            DoMenu.CreateLinkedGifts();

            foreach (var menuItem in DoMenu.MenuItems)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(menuItem));
            }
            
            Assert.Equal(2, DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities.Count());
            
            DoMenu.CreateLinkedGifts();

            DigitalOrderingModule.Instance.CurrentOrder.Cart.DeleteRemovedItems();
            
            var cartEntities = DigitalOrderingModule.Instance.CurrentOrder.Cart.Entities;
            Assert.Equal(2, cartEntities.Count());
        }
    }
}