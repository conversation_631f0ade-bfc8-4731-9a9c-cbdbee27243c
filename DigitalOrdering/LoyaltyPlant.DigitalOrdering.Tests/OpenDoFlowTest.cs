using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Tasks;
using LoyaltyPlant.DigitalOrdering.Tests.Utils;
using LoyaltyPlant.Partner.Model;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Surveys.Tasks;
using LoyaltyPlant.Texts;
using Moq;
using Xunit;
using WebResponse = LoyaltyPlant.Core.Web.WebResponse;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class OpenDoFlowTest : DigitalOrderingTest
    {
        private const string AddressWithCountry = "Russia, Saint-Petersburg Bolshoy prospect 15";
        private const string ApartmentNumber = "12";
        
        public OpenDoFlowTest()
        {
            SetupSurvey();
            
            MockWebClient.AddResponse(".*", new WebResponse
            {
                StatusCode = HttpStatusCode.OK
            });

            DoModule.DeleteAllCreatedOrders();
        }

        //[Fact]
        //public async void FewProcesses_ShowDoProcesses()
        //{
        //    await DigitalOrderingModule.Instance.Controller.Start();

        //    Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
        //    Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));

        //    MockDoFrontend.Verify(f => f.ShowDoSelectProcessMessage(), Times.Once);
        //}
        
        //[Fact]
        //public async void SavedOrder_FromDeletedProcesses_CreateNewOrder()
        //{
        //    // make current order from unavailable process
        //    DoModel.CurrentOrder = new DOOrder(PickUpProcessId, 12);
        //    DoModel.CurrentOrder.ProcessId = 11111;

        //    await DigitalOrderingModule.Instance.Controller.Start();

        //    Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
        //    Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            
        //    MockDoFrontend.Verify(f => f.ShowDoSelectProcessMessage(), Times.Once);
        //}
        
        //[Fact]
        //public async void OneProcess_ShowDoMap()
        //{
        //    // keep one type of process Pick Up
        //    DoModel.Processes.RemoveAll(p => p.Type != DoProcessType.PickUp);
            
        //    await DigitalOrderingModule.Instance.Controller.Start();

        //    Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
        //    Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
        //    Assert.True(MockWebClient.IsCallCount<GetPartnerInfoRequest>(1));

        //    MockDoFrontend.Verify(f => 
        //        f.ShowDoPickupMapScreen(It.IsAny<DOProcess>(), false), Times.Once);
        //}

        [Fact]
        public async void PickUp_PaidOrder_ToSameAddress()
        {
            const int outletId = 12;
            
            // menu must be with root category and items 
            AddMenuWithRootCategory(outletId, PickUpProcessId, DoProcessType.PickUp);
            
            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline);

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            await DigitalOrderingModule.Instance.Controller.CreateNewOrderWithSameAddress(paidOrder);
            
            Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
            Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            Assert.True(MockWebClient.IsCallCount<MenuRequest>(1));

            MockDoFrontend.Verify(f => f.ShowRootCategory(It.IsAny<Category>()), Times.Once);
            
            Assert.Equal(outletId, DigitalOrderingModule.Instance.CurrentOrder.OutletId); 
        }
        
        [Fact(DisplayName = "APP-2051 Exists process, but not exists district")]
        public async void Delivery_RepeatOrder_ToRemovedDistrict()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();
            
            // known district
            const int districtId = 123;

            var paidOrder = new DOPaidOrder(new DOOrder(DeliveryProcessId, new Place
            {
                Address = AddressWithCountry
            }, districtId), PaymentMethodType.CashOffline);
            paidOrder.AddressApartment = ApartmentNumber;

            // We have order and remove district.
            var process = DoModule.Processes.First(p => p.Id == DeliveryProcessId);
            process.Districts.RemoveAll(d => d.Id == districtId);
            
            // repeat order to unknown district
            var result = await DigitalOrderingModule.Instance.Controller.CreateNewOrderWithSameAddress(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(I18N.DELIVERY_SERVICE_IS_CLOSED, null), Times.Once);
            Assert.False(result);
            VerifyDontOpenDo();
        }
        
        [Fact(DisplayName = "APP-2051 Exists process, but not exists outlet")]
        public async void PickUp_RepeatOrder_ToRemovedOutlet()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();
            
            const int outletId = 11;

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline);

            // We have order and remove outlet.
            var process = DoModule.Processes.First(p => p.Id == PickUpProcessId);
            process.Outlets.RemoveAll(d => d.Id == outletId);
            
            // repeat order to unknown outlet
            var result = await DigitalOrderingModule.Instance.Controller.CreateNewOrderWithSameAddress(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA, null), Times.Once);
            Assert.False(result);
            VerifyDontOpenDo();
        }
        
        [Fact(DisplayName = "APP-2051 Exists process, but outlet now unavailable")]
        public async void PickUp_RepeatOrder_ToUnavailableOutlet()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();
            
            const int outletId = 11;

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline);

            // We have order, but now outlet is unavailable.
            var process = DoModule.Processes.First(p => p.Id == PickUpProcessId);
            var outlet = process.Outlets.First(d => d.Id == outletId);
            outlet.IsAvailable = false;
            
            // repeat order
            var result = await DigitalOrderingModule.Instance.Controller.CreateNewOrderWithSameAddress(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA, null), Times.Once);
            Assert.False(result);
            VerifyDontOpenDo();
        }

        [Fact(DisplayName = "APP-2051 Repeat order to exists process, but outlet now unavailable")]
        public async void PickUp_RepeatOrderAndMakeNew_ToUnavailableOutlet()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();
            
            const int outletId = 11;
            
            // menu must be with root category and items 
            AddMenuWithRootCategory(outletId, PickUpProcessId, DoProcessType.PickUp);
            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };

            // We have order, but now outlet is unavailable.
            var process = DoModule.Processes.First(p => p.Id == PickUpProcessId);
            var outlet = process.Outlets.First(d => d.Id == outletId);
            outlet.IsAvailable = false;
            
            // repeat order to unavailable outlet
            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(It.IsRegex(I18N.ORDERING_NOT_AVAILABLE_THIS_AREA), null), Times.Once);
            VerifyDontOpenDo();
        }

        [Fact(DisplayName = "APP-2051 Exists process, but district for another territory")]
        public async void Delivery_RepeatOrderAndMakeNew_ToUnavailableAddress()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();

            // known district
            const int districtId = 123;

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            // menu must be with root category and items 
            AddMenuWithRootCategory(districtId, DeliveryProcessId, DoProcessType.Delivery);
            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(DeliveryProcessId, new Place
            {
                Address = AddressWithCountry,
            }, districtId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };
            paidOrder.AddressApartment = ApartmentNumber;

            // district has not territory
            var process = DoModule.Processes.First(p => p.Id == DeliveryProcessId);
            var district = process.Districts.First(d => d.Id == districtId);
            district.Points.Clear();
            
            DoModule.DeleteAllCreatedOrders();
            
            // repeat order to unknown district
            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(I18N.DELIVERY_SERVICE_IS_CLOSED, null), Times.Once);
            VerifyDontOpenDo();
        }

        [Fact(DisplayName = "APP-2051 Exists process, but district was disabled")]
        public async void Delivery_RepeatOrderAndMakeNew_ToDisabledDistrict()
        {
            var mockMessageHandler = TestUtils.SetupMessageHandler();

            // known district
            const int districtId = 123;

            // menu must be with root category and items 
            AddMenuWithRootCategory(districtId, DeliveryProcessId, DoProcessType.Delivery);
            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(DeliveryProcessId, new Place
            {
                Address = AddressWithCountry,
            }, districtId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };
            paidOrder.AddressApartment = ApartmentNumber;

            // district has not territory
            var process = DoModule.Processes.First(p => p.Id == DeliveryProcessId);
            var district = process.Districts.First(d => d.Id == districtId);
            district.SetForAllPlanet();
            district.AssociatedOutletId = 0;

            DoModule.DeleteAllCreatedOrders();
            
            // repeat order to unknown district
            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            mockMessageHandler.Verify(mh => mh.CreateOkMessage(I18N.CANT_REPEAT_LAST_ORDER, null), Times.Once);
            VerifyDontOpenDo();
        }
        
        [Fact]
        public async void Delivery_RepeatOrder_ToSameAddress()
        {
            const int districtId = 123;

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            // any place in the district
            var doProcess = DigitalOrderingModule.Instance.Processes.First(p => p.Id == DeliveryProcessId);
            var district = doProcess.Districts.First(d => d.Id == districtId);
            district.SetForAllPlanet();
            
            AddMenuWithRootCategory(districtId, DeliveryProcessId, DoProcessType.Delivery);
            
            var paidOrder = new DOPaidOrder(new DOOrder(DeliveryProcessId, new Place
            {
                Address = AddressWithCountry
            }, districtId), PaymentMethodType.CashOffline);
            paidOrder.AddressApartment = ApartmentNumber;

            await DigitalOrderingModule.Instance.Controller.CreateNewOrderWithSameAddress(paidOrder);
            
            Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
            Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            // don't need refresh outlets GetPartnerInfoRequest 
            Assert.True(MockWebClient.IsCallCount<MenuRequest>(1));
            
            MockDoFrontend.Verify(f => f.ShowRootCategory(It.IsAny<Category>()), Times.Once);

            Assert.Equal(districtId, DigitalOrderingModule.Instance.CurrentOrder.SelectedRegionId); 
            Assert.Equal(AddressWithCountry, DigitalOrderingModule.Instance.CurrentOrder.Address); 
            Assert.Equal(ApartmentNumber, DigitalOrderingModule.Instance.CurrentOrder.Address2);
        }

        [Fact]
        public async void RepeatOrder_MakeNewOrder_ShowCartScreen()
        {
            const int outletId = 12;
            
            // menu must be with root category and items 
            AddMenuWithRootCategory(outletId, PickUpProcessId, DoProcessType.PickUp);

            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
            Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            Assert.True(MockWebClient.IsCallCount<MenuRequest>(1));
            
            MockDoFrontend.Verify(f => f.ShowDoCartScreen(), Times.Once);
        }
        
        [Fact]
        public async void RepeatOrder_OutletSoonClose_ShowWarningDialog()
        {
            // Tuesday
            MockDateTime.SetMockNow(new DateTime(2019, 10, 29, 22, 0, 0));
            const int outletId = 12;
            
            // outlet is EnableOrderWhileNotWorking
            var pickUpProcess12 = DigitalOrderingModule.Instance.Processes.First(p => p.Id == PickUpProcessId);
            var outlet12 = pickUpProcess12.Outlets.First(o => o.Id == PickUpProcessId);
            outlet12.EnableOrderWhileNotWorking = true;
            
            // working only for monday
            outlet12.WorkingDays.Clear();
            var wd = new WorkingDay("mon", 0, 60, 65, 65);
            wd.Intervals.Add(new WorkingDayInterval("mon", 600, "10:00")); //end work at 20:00
            outlet12.WorkingDays.Add(wd);

            var mockMessageHandler = TestUtils.SetupMessageHandler();

            // menu must be with root category and items 
            AddMenuWithRootCategory(outletId, PickUpProcessId, DoProcessType.PickUp);

            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;
            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
            Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            
            mockMessageHandler.Verify(f => f.CreateTwoButtonsMessage(
                    It.IsAny<string>(), 
                    It.IsAny<string>(), 
                    It.IsAny<string>(),
                    It.IsAny<Action>(),
                    It.IsAny<Action>()), Times.Once);
        }
        
        [Fact]
        public async void RepeatOrder_OutletMovedToAnotherProcess_ShowCartScreen()
        {
            const int newProcessIdWithOutlet = 15;
            const int outletId = 12;

            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            // menu must be with root category and items 
            AddMenuWithRootCategory(outletId, newProcessIdWithOutlet, DoProcessType.PickUp);

            var menuPosition = DoMenu.GetMenuItem(ItemId);
            var cartMenuItem = new CartMenuItem(menuPosition);

            var paidOrder = new DOPaidOrder(new DOOrder(PickUpProcessId, outletId), PaymentMethodType.CashOffline)
            {
                Entities = new List<CartMenuItem>
                {
                    cartMenuItem,
                },
            };

            // move outlet 12 from process 12 to process 15
            var pickUpProcess12 = DigitalOrderingModule.Instance.Processes.First(p => p.Id == PickUpProcessId);
            var outlet12 = pickUpProcess12.Outlets.First(o => o.Id == PickUpProcessId);
            pickUpProcess12.Outlets.Remove(outlet12);
            DigitalOrderingModule.Instance.Processes.First(p => p.Id == newProcessIdWithOutlet).Outlets.Add(outlet12);
            
            await DigitalOrderingModule.Instance.Controller.RepeatOrderAsync(paidOrder);
            
            Assert.True(MockWebClient.IsCallCount<ProcessesRequest>(1));
            Assert.True(MockWebClient.IsCallCount<GetSurveyRequest>(1));
            Assert.True(MockWebClient.IsCallCount<MenuRequest>(1));
            
            MockDoFrontend.Verify(f => f.ShowDoCartScreen(), Times.Once);
            
            Assert.Equal(newProcessIdWithOutlet, DigitalOrderingModule.Instance.CurrentOrder.ProcessId);
            Assert.Equal(outletId, DigitalOrderingModule.Instance.CurrentOrder.CurrentZone.Id);
            Assert.Equal(DoProcessType.PickUp, DigitalOrderingModule.Instance.CurrentOrder.Process.Type);
        }
        
        private void VerifyDontOpenDo()
        {
            MockDoFrontend.Verify(f => f.ShowDoCartScreen(), Times.Never);
            MockDoFrontend.Verify(f => f.ShowRootCategory(It.IsAny<Category>()), Times.Never);
            MockDoFrontend.Verify(f => f.ShowRootMenu(), Times.Never);
            MockDoFrontend.Verify(f => f.ShowRootMenu(It.IsAny<string>()), Times.Never);
            MockDoFrontend.Verify(f => f.ShowDoSelectProcessMessage(), Times.Never);
        }
    }
}