using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Data;
using LoyaltyPlant.DigitalOrdering.Tasks;
using LoyaltyPlant.DigitalOrdering.Tests.Utils;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Partner.Model;
using LoyaltyPlant.Payment;
using LoyaltyPlant.Payment.Gates.Best2PayGate;
using LoyaltyPlant.Payment.Gates.NGeniusGate;
using LoyaltyPlant.Payment.Gates.StripeGate;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Payment.Tasks;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Surveys.Tasks;
using LoyaltyPlant.Texts;
using LoyaltyPlant.User;
using Moq;
using Xunit;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class PaymentTest : DigitalOrderingTest
    {
        private List<MenuItemData> _menuItems = new List<MenuItemData>();
        private Mock<IPaymentFrontend> _paymentFrontend;

        public PaymentTest()
        {
            SetCurrentOrder(12, 12);
            Engine.Instance.AddModule(new PaymentModule());
            Engine.Instance.AddModule(new SurveysModule());
            Engine.Instance.AddModule(new UserModule());
            PaymentModule.Instance.InitPaymentIntent(new DoControllerPaymentObject(Controller, DoModule.CurrentOrder));
            _paymentFrontend = new Mock<IPaymentFrontend>();
            PaymentModule.Instance.SetController(new PaymentController(_paymentFrontend.Object));
        }

        [Fact]
        public async void NGenius_GetEnrollSettings_AssociatedOutlet()
        {
            SetCurrentOrderForDelivery(DeliveryProcessId, 122);
            var paymentIntent = PaymentModule.Instance.InitPaymentIntent(new DoControllerPaymentObject(Controller, DoModule.CurrentOrder));
            paymentIntent.Gate = new NGenius();

            string salesOutletId = null;
            MockWebClient.OnResponse += request =>
            {
                if (request is GetEnrollSettingsRequest getEnrollSettingsRequest)
                {
                    salesOutletId = getEnrollSettingsRequest.JObject["salesOutletId"].ToObject<string>();
                }
            };

            await PaymentModule.Instance.GetEnrollSettings();

            // associated outlet for district 122
            Assert.Equal("11", salesOutletId);
        }

        [Fact]
        public async void MobilePayment_PaymentType_ApplePay()
        {
            MakeCartWithItems();
            SetupSuccessOrders();
            SetupCheckout();
            SetupPaymentTypes(12);

            var paymentIntent = PaymentModule.Instance.InitPaymentIntent(new DoControllerPaymentObject(Controller, DoModule.CurrentOrder));
            paymentIntent.Gate = new Stripe(GateType.StripeConnect);

            string paymentTypeRequestCheckout = null;
            MockWebClient.OnResponse += request =>
            {
                if (request is CheckoutRequest checkoutRequest)
                {
                    paymentTypeRequestCheckout = checkoutRequest.JObject["payment"]["type"].ToObject<string>();
                }
            };

            var mobilePayment = PaymentModule.Instance.GetAllPaymentMethods().FirstOrDefault(p => p.PaymentMethodType == PaymentMethodType.ApplePay);
            PaymentModule.Instance.Controller.OnSelectPaymentMethod(mobilePayment);

            Assert.True(await DigitalOrderingModule.Instance.Controller.PayAsync());
            Assert.Equal("APPLE_PAY", paymentTypeRequestCheckout);
        }

        [Fact]
        public async void ByBankCard_PaymentType_BankCardApp()
        {
            MakeCartWithItems();
            SetupSuccessOrders();
            SetupCheckout();
            SetupPaymentTypes(12);

            var paymentIntent = PaymentModule.Instance.InitPaymentIntent(new DoControllerPaymentObject(Controller, DoModule.CurrentOrder));
            paymentIntent.Gate = new Stripe(GateType.StripeConnect);

            PaymentModule.Instance.SetModel(new PaymentModel
            {
                BankCards =
                {
                    new BankCard(GateType.Stripe)
                    {
                        BankCardId = 123,
                        CardMask = "5555 **** 5555",
                        Token = "SOME TOKEN",
                        IsSaveable = true,
                        CardType = BankCardType.Maestro
                    }
                }
            });

            string paymentTypeRequestCheckout = null;
            MockWebClient.OnResponse += request =>
            {
                if (request is CheckoutRequest checkoutRequest)
                {
                    paymentTypeRequestCheckout = checkoutRequest.JObject["payment"]["type"].ToObject<string>();
                }
            };

            var paymentMethods = PaymentModule.Instance.GetAllPaymentMethods();
            Assert.Equal(4, paymentMethods.Count);

            var bankCardPaymentMethod = paymentMethods.FirstOrDefault(p => p.PaymentMethodType == PaymentMethodType.BankCardApp);
            PaymentModule.Instance.Controller.OnSelectPaymentMethod(bankCardPaymentMethod);

            Assert.True(await DigitalOrderingModule.Instance.Controller.PayAsync());
            Assert.Equal("BANK_CARD_APP", paymentTypeRequestCheckout);
        }

        [Fact]
        public async void PreCheck_FailedTwoTimes_MessageBox()
        {
            MockLpLogWriter.DisableExceptionByErrorLogs = true;

            var mockMessageHandler = TestUtils.SetupMessageHandler();

            MockWebClient.AddResponse("precheck", new PrecheckResponse()
            {
                StatusCode = HttpStatusCode.InternalServerError,
                Accepted = true,
            });

            await DigitalOrderingModule.Instance.Controller.PerformCheckoutAsync();

            Assert.True(MockWebClient.IsCallCount<PrecheckRequest>(2));
            Assert.False(string.IsNullOrWhiteSpace(I18N.DO_PRECHECK_TIMEOUT));
            mockMessageHandler.Verify(mh => mh.CreateTwoButtonsMessage(I18N.LP_SERVER_FAILED_WHILE_ORDERING_WITH_LOGS_BUTTON, I18N.OK, I18N.SEND_ERROR_LOGS, null, It.IsAny<Action>()), Times.Once);
        }

        [Theory]
        [InlineData(0, true)]
        [InlineData(1, true)]
        [InlineData(12, false)] // current order on outlet id - 12
        public async void PerformCheckout_FeatureDeleteBankCards_DeleteBankCards(int lastAddBankCardOutletId,
            bool deletedBankCards)
        {
            var bankCards = new List<BankCard>
            {
                new BankCard(GateType.Vantiv)
                {
                    BankCardId = 1,
                },
                new BankCard(GateType.Vantiv)
                {
                    BankCardId = 2,
                }
            };

            DoModel.OutletIdOfLastOrderWithBankCard = lastAddBankCardOutletId;

            MockWebClient.AddResponse("get-bank-cards", new GetCardsResponse()
            {
                StatusCode = HttpStatusCode.OK,
                Cards =
                {
                    bankCards[0],
                    bankCards[1],
                }
            });

            SetupAcceptingPreCheck();

            MockWebClient.AddResponse("delete-bank-card", new DeleteBankCardResponse()
            {
                Result = true,
                StatusCode = HttpStatusCode.OK,
            });

            PaymentModule.Instance.SetModel(new PaymentModel
            {
                LastPaymentMethod = new PaymentMethod(PaymentMethodType.BankCardApp, 1),
                BankCards = bankCards,
            });

            PartnerModel.SpecificSettings.Add(new SpecificSetting
            {
                Name = "DELETE_BANK_CARDS"
            }
            );

            SetupPaymentTypes(12);

            MakeCartWithItems();

            await DigitalOrderingModule.Instance.Controller.PerformCheckoutAsync();

            if (deletedBankCards)
            {
                Assert.Equal(PaymentMethodType.NotSelected, PaymentModule.Instance.LastPaymentMethod.PaymentMethodType);
                Assert.True(MockWebClient.IsCallCount<DeleteBankCardRequest>(2));
                Assert.Empty(PaymentModule.Instance.BankCards);
                return;
            }

            Assert.Equal(1, PaymentModule.Instance.LastPaymentMethod.CardId);
            Assert.Equal(2, PaymentModule.Instance.BankCards.Count);
            Assert.True(MockWebClient.IsCallCount<DeleteBankCardRequest>(0));
        }

        [Fact]
        public void Checkout_CartWithItems_IsPayable()
        {
            MakeCartWithItems();
            Assert.True(PaymentModule.Instance.NeedToShowSelectPaymentMethodOnCheckoutScreen());
        }

        [Fact]
        public void Checkout_CartWithGifts_IsPayable()
        {
            MakeCartWithGiftsOnly();
            Assert.False(PaymentModule.Instance.NeedToShowSelectPaymentMethodOnCheckoutScreen());
        }

        [Fact]
        public async void MakeOrder_NotSelectedPaymentMethod_ShowSelectPaymentMethod()
        {
            MakeCartWithItems();
            SetupCheckout();

            LoyaltyModule.Instance.SetPointsAmount(100);
            PaymentModule.Instance.SetPaymentMethod(new PaymentMethod(PaymentMethodType.NotSelected));

            Assert.False(await DigitalOrderingModule.Instance.Controller.PayAsync());

            _paymentFrontend.Verify(pf => pf.ShowPaymentMethodsScreen(It.IsAny<PaymentMethod>()), Times.Once);
        }

        [Fact]
        public async void MakeOrder_CartWithItems_PayCashOffline()
        {
            // APP-4032
            await Task.Delay(6000);

            MakeCartWithItems();
            MakeCartWithGiftsOnly();
            SetupSuccessOrders();
            SetupCheckout();

            LoyaltyModule.Instance.SetPointsAmount(100);
            PaymentModule.Instance.SetPaymentMethod(new PaymentMethod(PaymentMethodType.CashOffline));

            string paymentTypeRequestCheckout = null;
            MockWebClient.OnResponse += request =>
            {
                if (request is CheckoutRequest checkoutRequest)
                {
                    paymentTypeRequestCheckout = checkoutRequest.JObject["payment"]["type"].ToObject<string>();
                }
            };
            
            PaymentModule.Instance.Controller.PaymentIntent.Gate = new Best2Pay();

            Assert.True(await DigitalOrderingModule.Instance.Controller.PayAsync());
            Assert.Equal("CASH_OFFLINE", paymentTypeRequestCheckout);
        }

        [Fact]
        public async void MakeOrder_CartWithGifts_PayWithoutMoney()
        {
            MakeCartWithGiftsOnly();
            SetupSuccessOrders();
            SetupCheckout();

            // APP-4032
            await Task.Delay(4000);

            LoyaltyModule.Instance.SetPointsAmount(100);

            var paymentType = "";
            MockWebClient.OnResponse += request =>
            {
                if (request is CheckoutRequest checkoutRequest)
                {
                    paymentType = checkoutRequest.JObject["payment"]["type"].ToString();
                }
            };

            PaymentModule.Instance.Controller.PaymentIntent.Gate = new Best2Pay();

            Assert.True(await DigitalOrderingModule.Instance.Controller.PayAsync());

            Assert.True(MockWebClient.IsCallCount<CheckoutRequest>(1));
            Assert.Equal(PaymentMethodExtension.DefaultRequestPaymentType, paymentType);
        }

        private void SetupCheckout()
        {
            MockWebClient.AddResponse("checkout", new CheckoutResponse
            {
                StatusCode = HttpStatusCode.OK,
                Accepted = true,
            });
        }

        public void SetupSuccessOrders()
        {
            MockWebClient.OnResponse += request =>
            {
                if (request is OrdersRequest)
                {
                    foreach (var order in MockOrdersManager.Orders)
                    {
                        order.PosId = "some posID";
                    }
                }
            };
        }

        private void MakeCartWithItems()
        {
            var menuItem1 = new MenuPositionData()
            {
                Id = "menuItem1",
                Prices = { Price = 5.0m }
            };

            var menuItem2 = new MenuPositionData()
            {
                Id = "menuItem2",
                Prices = { Price = 5.0m }
            };

            var menuItems = new List<MenuItemData>
            {
                menuItem1,
                menuItem2
            };
            _menuItems.AddRange(menuItems);
            DoMenu.SetMenuItems(_menuItems);

            // add all exists items in cart
            foreach (var menuItem in DoMenu.MenuItems)
            {
                DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(menuItem));
            }
        }

        private void MakeCartWithGiftsOnly()
        {
            const string menuItemId = "abracadabra";

            var presents = new List<Present>();
            var presentId = 1;
            var present = new Present(presentId, 1000, "preview tex " + presentId, 10, presentId, "",
                "short description " + presentId)
            {
                DigitalOrderingLinkedItems = new List<DigitalOrderingLinkedItem>
                {
                    new DigitalOrderingLinkedItem(menuItemId, Present.OrderingTypes.OnlyDigitalOrdering),
                },
                MoveToHiddenAfterUse = true,
            };
            present.SetPriceInPoints(10);
            presents.Add(present);

            presents.PutToLoyaltyModel();

            var menuItem = new MenuPositionData
            {
                Id = menuItemId,
                Prices = { Price = 7.77m },
            };

            var giftPositionData = GiftPositionData.CreateByPresent(present, menuItem, "giftCategoryId");

            _menuItems.Add(menuItem);
            _menuItems.Add(giftPositionData);
            DoMenu.SetMenuItems(_menuItems);

            var giftPosition = DoMenu.GetMenuItem(giftPositionData.Id);

            Assert.NotNull(giftPosition);

            DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(giftPosition));
        }

        private void SetupPaymentTypes(int zoneId)
        {
            var zone = DoModule.Processes.SelectMany(p => p.Outlets).First(o => o.Id == zoneId);
            zone.PaymentTypes = new List<PaymentMethodType>
            {
                PaymentMethodType.BankCardApp,
                PaymentMethodType.ApplePay,
                PaymentMethodType.AndroidPay,
                PaymentMethodType.BankCardOffline,
                PaymentMethodType.CashOffline
            };
        }
    }
}