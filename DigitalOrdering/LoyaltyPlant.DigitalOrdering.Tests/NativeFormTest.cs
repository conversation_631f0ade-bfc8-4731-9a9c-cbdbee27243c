using System.Linq;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.DigitalOrdering.Model.ExternalVendors.Postmates;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.Model;
using Xunit;
using Moq;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class NativeFormTest : DigitalOrderingTest
    {
        private const int FormId = 132;

        public NativeFormTest()
        {
            SetCurrentOrder(12, 12);
            Engine.Instance.AddModule(new SurveysModule());
            PartnerModel.PhonePrefix = "+1";
        }

        [Theory(DisplayName = "https://youtrack.loyaltyplant.com/issue/MD-17598")]
        [InlineData("****** 5145353", false)] // wrong prefix and code
        [InlineData("283 5145353", false)] // without phone prefix
        [InlineData("****** 5145353", true)] // good code with spaces
        [InlineData("+16805145353", true)]
        [InlineData("+11415145353", false)] // not usa code
        public void PostmatesIntegration_PhoneNumber_Validate(string phone, bool correct)
        {
            SetupPostmates();
            SetupSurveyForm();

            var phoneField = DoModule.CurrentOrder.Form.Fields.FirstOrDefault(f => f.Meta == Meta.Phone) as PhoneField;
            Assert.NotNull(phoneField);
            phoneField.EnteredAnswer = phone;

            var result = DoModule.CurrentOrder.ValidateForm(out _);
            Assert.Equal(correct, result);
        }

        private void SetupPostmates()
        {
            SetCurrentOrderForDelivery(17, 123);
            DigitalOrderingModule.Instance.CurrentOrder.Process.FormId = FormId;

            var currentDistrict = DigitalOrderingModule.Instance.CurrentOrder.CurrentZone as DODistrict;
            var postmatesVendor = new DoPostmatesVendor();
            currentDistrict.ExternalVendor = postmatesVendor;
        }
        
        private void SetupSurveyForm()
        {
            SurveysModule.Instance.SetModel(new SurveysModel
            {
                Surveys =
                {
                    {
                        FormId,
                        new Survey
                        {
                            Id = FormId,
                            Fields =
                            {
                                new EmailField(1, 1, false, "email", Surveys.Model.Meta.Email),
                                new PhoneField(3, 3, true, "phone", Surveys.Model.Meta.Phone),
                                new TextBoxField(TextBoxType.OneLine, 1, 1, false, "", Meta.Name),
                            }
                        }
                    }
                }
            });

            var surveyFrontend = new Mock<ISurveysFrontend>();
            SurveysModule.Instance.SetController(new SurveysController(surveyFrontend.Object));
        }
    }
}