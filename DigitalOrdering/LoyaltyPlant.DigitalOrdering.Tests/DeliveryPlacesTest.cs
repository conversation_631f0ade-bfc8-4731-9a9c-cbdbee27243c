// Naming convension
// UnitOfWork_StateUnderTest_ExpectedBehavior

using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Tests.Utils;
using LoyaltyPlant.Languages;
using LoyaltyPlant.Partner;
using Xunit;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class DeliveryPlacesTest
    {
        private readonly MockWebController _wc;

        public DeliveryPlacesTest()
        {
            _wc = new MockWebController();

            Engine.Initialize(_wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());

            Engine.Instance.AddModule(new PartnerModule());
            Engine.Instance.AddModule(new DigitalOrderingModule(new MockOrdersManager()));
            Engine.Instance.AddModule(new LanguagesModule());
        }

        [Fact(DisplayName = "APP-2263 Исправить формать американского адреса в CRM")]
        public void Place_GetFormattedAddress()
        {
            var place = new Place()
            {
                MainAddress = "54 Rising Hill Rd",
                Zip = "91766",
                City = "Pomona",
                Country = "United States"
            };

            Assert.Equal("54 Rising Hill Rd, Pomona, United States, 91766", place.GetFormattedAddress());
        }

        [Fact]
        public void Places_ListOfSameItems_Sort()
        {
            var places = new List<Place>();

            for (int i = 0; i < 20; i++)
            {
                places.Add(new Place(i));
                places.Add(new Place(i)
                {
                    IsTemporary = true,
                });
            }

            places.Sort();

            var firstTemporaries = places.Take(20);

            Assert.True(firstTemporaries.All(p => p.IsTemporary));
        }

        [Theory]
        [InlineData(52.250137, 21.062359, 614)]
        [InlineData(52.2642213, 21.0333122, 615)]
        [InlineData(52.1531335390185, 21.0284760966897, 616)]
        [InlineData(52.2313034157704, 20.9613541513681, 617)]
        [InlineData(52.3014333369465, 20.9210606664419, 618)]
        [InlineData(52.2810755383419, 21.0499612614512, 619)]
        [InlineData(52.261753311637, 20.9072000160813, 620)]
        [InlineData(52.31587613645, 21.0028998926282, 621)]
        [InlineData(52.2356736, 21.0126144, 622)]
        [InlineData(52.2115993628673, 20.9833194315434, 623)]
        [InlineData(52.174474509199115, 21.187720187008381, 624)]
        [InlineData(52.197734350037706, 20.883678384125233, 625)]
        [InlineData(52.2731636349863, 20.9953152760863, 626)]
        [InlineData(52.181071874681308, 20.960277579724792, 627)]
        [InlineData(52.1537446984374, 21.1383712291718, 628)]
        [InlineData(52.2499358922175, 21.202603764832, 629)]
        [InlineData(52.2672250359663, 21.1192960292101, 630)]
        [InlineData(52.198449399999987, 21.044240200000001, 631)]
        [InlineData(52.2008402226023, 21.0477953776717, 631)]
        [InlineData(52.206517321566, 21.0468425229192, 631)]
        [InlineData(52.206517321566, 21.0448342189193, 631)]
        [InlineData(52.2002362856868, 21.0835140198469, 631)]
        [InlineData(52.194053834071674, 20.840673521161079, null)]
        [InlineData(52.1603190171216, 20.8858700841665, null)]
        [InlineData(52.3030814924494, 20.8453506231308, null)]
        [InlineData(52.3566210396637, 21.1429085209966, null)]
        [InlineData(56.0962840163086, 35.0549905747175, null)]
        [InlineData(37.2199270263301, -85.5082996934652, null)]
        [InlineData(-26.5537782947056, -57.3263889178634, null)]
        [InlineData(-25.0585991910067, 129.748846627772, null)]
        public async void WarsawDistricts_AvailabilityOrderInPlace(double lat, double lon, int? exceptedDistrict)
        {
            //APP-4088 we've added some caching, so now we need the app to feel like the processes must be updated.
            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            _wc.AddResponse("StubContent/processes_delivery_in_warsaw.json");

            PartnerModule.Instance.SetModel(new PartnerModel
            {
                Outlets =
                {
                    new Outlet
                    {
                        Id = 2410,
                    }
                }
            });

            // load Warsaw districts
            _ = await DigitalOrderingModule.Instance.UpdateProcesses(false);

            DigitalOrderingModule.Instance.CreateOrder(DoProcessType.Delivery, new Place
            {
                Latitude = lat,
                Longitude = lon
            }, out _);

            var order = DigitalOrderingModule.Instance.CurrentOrder;

            Assert.Equal(exceptedDistrict, order?.SelectedRegionId);
        }

        [Theory]
        [InlineData(51.533307971177379, -0.0761250288650011, null)]
        [InlineData(51.581316364371453, 0.096255429089069366, null)]
        [InlineData(51.524112099999989, 0.05494219999999999, null)]
        [InlineData(51.546649884915588, 0.83461359143257141, 222)]
        [InlineData(51.542749088659185, 0.64625747501850139, 756)]
        [InlineData(51.56571326885134, 0.51940944045782089, 197)]
        [InlineData(51.576295885644747, 0.14868993312120438, 196)]
        [InlineData(51.577188302933124, 0.0035556033253669739, 554)]
        [InlineData(51.368348548711573, -0.078147836029529572, 720)]
        public async void LondonFatPizza_AvailabilityOrderInPlace(double lat, double lon, int? exceptedDistrict)
        {
            //APP-4088 we've added some caching, so now we need the app to feel like the processes must be updated.
            DigitalOrderingModule.UpdatedProcessesLastTime = DateTime.MinValue;

            _wc.AddResponse("StubContent/processes_delivery_in_london.json");

            PartnerModule.Instance.SetModel(new PartnerModel
            {
                Outlets =
                {
                    new Outlet
                    {
                        Id = 5470,
                    },
                    new Outlet
                    {
                        Id = 5467,
                    },
                    new Outlet
                    {
                        Id = 5469,
                    },
                    new Outlet
                    {
                        Id = 6525,
                    },
                    new Outlet
                    {
                        Id = 6733,
                    },
                    new Outlet
                    {
                        Id = 5468,
                    },
                }
            });

            // load London districts
            _ = await DigitalOrderingModule.Instance.UpdateProcesses(false);

            DigitalOrderingModule.Instance.CreateOrder(DoProcessType.Delivery, new Place
            {
                Latitude = lat,
                Longitude = lon
            }, out _);

            var order = DigitalOrderingModule.Instance.CurrentOrder;

            Assert.Equal(exceptedDistrict, order?.SelectedRegionId);
        }

        [Fact]
        public async void SavedDeliveryAddresses_EmptyQuery_GetOnlyOldAddresses()
        {
            var savedAddresses = SetSavedAddresses();

            List<Place> searchResult = null;
            await DigitalOrderingModule.Instance.DeliveryPlaces.GetLocationSearchResults("", (s, places) =>
                {
                    searchResult = places;
                });

            var addresses = searchResult.Select(p => p.MainAddress).ToList();
            Assert.Equal(4, addresses.Count());
            Assert.Equal(savedAddresses, addresses);
        }

        [Fact]
        public async void Autocomplete_WithSavedAddresses_GetAddresses()
        {
            _wc.AddResponse("StubContent/places_autocomplete_response_ad.json");

            SetSavedAddresses();

            List<Place> searchResult = null;
            await DigitalOrderingModule.Instance.DeliveryPlaces.GetLocationSearchResults("Ade", (s, places) =>
            {
                searchResult = places;
            });

            Assert.Equal(5, searchResult.Count());
            Assert.Equal("Adelaide", searchResult[0].MainAddress);
            Assert.True(searchResult[0].IsTemporary);
        }

        [Fact]
        public async void GooglePlaces_Details_ParseMarinaPark()
        {
            _wc.AddResponse("StubContent/places_details_response_dubai_marina_park.json");

            var place = await DigitalOrderingModule.Instance.DeliveryPlaces.SetPlaceCoordinatesByGoogleId(new Place()
            {
                MainAddress = "Marina Park",
                SecondAddress = "Dubai - United Arab Emirates",
            });

            Assert.Contains("Marina Park", place.Address);
            Assert.Contains("Dubai", place.Address);
            Assert.Contains("United Arab Emirates", place.Address);
            Assert.Equal(25.068737200000001, place.Latitude);
            Assert.Equal(55.133487900000013, place.Longitude);
            Assert.Equal("United Arab Emirates", place.Country);
            Assert.Equal("Dubai", place.City);
            Assert.True(place.IsEstablishment);
        }

        [Fact(DisplayName = "https://jira.loyaltyplant.com/browse/APP-1945")]
        public async void GooglePlaces_Details_ParseBolshoyProspect()
        {
            _wc.AddResponse("StubContent/places_details_response_spb_bolshoy_prospect.json");

            var place = await DigitalOrderingModule.Instance.DeliveryPlaces.SetPlaceCoordinatesByGoogleId(new Place()
            {
                MainAddress = "Bolshoy Prospekt, 65",
                SecondAddress = "Sankt-Peterburg, Russia",
            });

            Assert.Contains("Bolshoy Prospekt, 65", place.Address);
            Assert.Contains("Sankt-Peterburg", place.Address);
            Assert.Contains("Russia", place.Address);
            Assert.Equal(59.9637952, place.Latitude);
            Assert.Equal(30.309238400000002, place.Longitude);
            Assert.Equal("Russia", place.Country);
            Assert.Equal("Sankt-Peterburg", place.City);
            Assert.False(place.IsEstablishment);
        }

        [Fact]
        public async void GooglePlaces_Autocomplete_ParseAddresses()
        {
            _wc.AddResponse("StubContent/places_autocomplete_response_ad.json");

            List<Place> searchResult = null;
            await DigitalOrderingModule.Instance.DeliveryPlaces.GetLocationSearchResults("Ad", (s, places) =>
            {
                searchResult = places;
            });

            Assert.Equal(5, searchResult.Count());

            var admiralteyskaya = searchResult[4];
            Assert.Equal("Admiralteyskaya", admiralteyskaya.MainAddress);
            Assert.Equal("Kirpichnyy Pereulok, Saint Petersburg, Leningrad Oblast, Russia", admiralteyskaya.SecondAddress);
            Assert.Equal("ChIJvStLyRoxlkYRzsiq1PI1AD4", admiralteyskaya.GoogleSearchId);
        }

        private static string[] SetSavedAddresses()
        {
            var mainAddresses = new[] { "Adelaide", "Address 0", "Address 1", "Address 2" };
            var secondAddresses = new[] { "SA, Australia", "Saint-Petersburg", "Saint-Petersburg", "Saint-Petersburg" };
            var i = 0;
            foreach (var savedAddress in mainAddresses)
            {
                DigitalOrderingModule.Instance.DeliveryPlaces.AddLast(new Place(i)
                {
                    MainAddress = savedAddress,
                    SecondAddress = secondAddresses[i],
                    IsTemporary = true,
                });
                i++;
            }

            return mainAddresses;
        }
    }
}