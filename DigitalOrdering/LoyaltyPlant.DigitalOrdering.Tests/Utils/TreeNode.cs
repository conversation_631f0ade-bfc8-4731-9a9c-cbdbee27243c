using System.Collections;
using System.Collections.Generic;

namespace LoyaltyPlant.DigitalOrdering.Tests.Utils
{
    // based on https://stackoverflow.com/questions/9860207/build-a-simple-high-performance-tree-data-structure-in-c-sharp
    public class TreeNode : IEnumerable<TreeNode>
    {
        private readonly Dictionary<string, TreeNode> _children =
            new Dictionary<string, TreeNode>();

        public readonly string Id;
        public TreeNode Parent { get; private set; }

        public TreeNode(string id)
        {
            Id = id;
        }

        public TreeNode GetChild(string id)
        {
            return _children[id];
        }

        public void Add(TreeNode item)
        {
            item.Parent?._children.Remove(item.Id);

            item.Parent = this;
            _children.Add(item.Id, item);
        }
        
        public IEnumerable<TreeNode> GetAllChildren()
        {
            foreach (var child in _children.Values)
            {
                yield return child;
                foreach (var subChild in child.GetAllChildren())
                {
                    yield return subChild;
                }
            }
        }

        public IEnumerator<TreeNode> GetEnumerator()
        {
            return _children.Values.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        public int Count => _children.Count;
    }
}