using System.Linq;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using Xunit;

namespace LoyaltyPlant.DigitalOrdering.Tests
{
    public class ChangeAddressMenuTest : DigitalOrderingTest
    {
        public ChangeAddressMenuTest()
        {
            SetCurrentOrder(12, 12);
        }

        [Theory]
        [InlineData("good", MenuCompareResult.Good)]
        [InlineData("missing", MenuCompareResult.PositionMissing)]
        [InlineData("empty", MenuCompareResult.CartEmpty)]
        public void Test_Menu_CompareMenuResult(string situation, MenuCompareResult menuResult)
        {
            SetupMockResponse("StubContent/DigitalOrdering_init_menu_changeAddress.json");
            var result = DigitalOrderingModule.Instance.GetMenuAsync().Result;

            // mongo tacos - в новом меню не отличается от того, что есть в старом меню
            var constItem = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0243bc89-a56f-4e02-b2d7-a6a99d06a24f");
            // Peppermint Mocha - этого элемента не будет в новом меню
            var notExistFirstItem = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0442a49f-2c62-4452-8877-9af957449771");
            // Purple Taro - этого элемента тоже не будет в новом меню
            var notExistSecondItem = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0595a4a3-ed04-4929-9519-82b9202c0e4d");

            switch (situation)
            {
                // положить айтем, который active из active категории
                case "good":
                    DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(constItem));
                    break;

                // добавляем 2 айтема из текущего меню. Один из них будет отсуствовать в новом меню
                case "missing":
                    DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(constItem));
                    DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(notExistFirstItem));
                    break;

                case "empty":
                    DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(notExistFirstItem));
                    DigitalOrderingModule.Instance.CurrentOrder.Cart.AddCartEntity(new CartMenuItem(notExistSecondItem));
                    break;
            }

            // берем новое меню
            SetupMockResponse("StubContent/DigitalOrdering_second_menu_changeAddress.json");
            _ = DigitalOrderingModule.Instance.GetMenuAsync().Result;

            var compareResult = DoModule.CompareMenu(DoMenu, out _);

            Assert.Equal(menuResult, compareResult);
        }

        [Theory]
        [InlineData(MenuCompareResult.Good, 1)] // - списки модификаторов совпадают полностью
        [InlineData(MenuCompareResult.Good, 2)] // - списки модификаторов у CurrentOrder и в новом меню отличаются, но все, кто selected в CurrentOrder, присутствуют в списке модификаторов у айтема из нового меню
        [InlineData(MenuCompareResult.CartEmpty, 3)] // - CartEmpty списки отличаются, один из модификаторов, который у айтема из CurrentOrder отмечен как Selected, отсутствует у айтема из нового меню
        [InlineData(MenuCompareResult.Good, 4)] // - группа модификаторов становится неактивной, ниче оттуда выбрано не было
        [InlineData(MenuCompareResult.CartEmpty, 5)] // - группа модификаторов становится inactive, оттуда было че-то выбрано
        [InlineData(MenuCompareResult.CartEmpty, 6)] // - группа модификаторов становится hidden, оттуда было че-то выбрано

        // TODO реализовать то, что ниже :)
        // 4. Good ? список модификаторов у айтема из CurrentOrder пустой, а в меню он есть, где все модификаторы необязательные
        //[InlineData(MenuCompareResult.Good, 4)]
        // 5. Good ? список модификаторов у айтема из CurrentOrder состоит из необязательных не выбранных модификаторов, а в меню у айтема нет модификаторов
        //[InlineData(MenuCompareResult.Good, 5)]
        // 6. CartEmpty ? в CurrentOrder модификатор необязательный и не выбран, а в меню он обязательный
        //[InlineData(MenuCompareResult.CartEmpty, 6)]
        // 7. Good цена Selected модификатора отличается от того, что в новом меню
        //[InlineData(MenuCompareResult.Good, 7)]

        public void Test_CompareMenuMethod_Modifiers(MenuCompareResult menuResult, int situationNumber)
        {
            SetupMockResponse("StubContent/DigitalOrdering_init_menu_changeAddress.json");
            var result = DigitalOrderingModule.Instance.GetMenuAsync().Result;

            var currOrder = DigitalOrderingModule.Instance.CurrentOrder;

            switch (situationNumber)
            {
                // положить айтем, у которого есть какие-то модификаторы и они совпадают с тем, что в новом меню у такого же айтема
                case 1:
                    // mongo tacos - в новом меню не отличается от того, что есть в старом меню
                    var constItemWithConstModifiers = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0243bc89-a56f-4e02-b2d7-a6a99d06a24f");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(constItemWithConstModifiers));
                    break;

                case 2:
                    // закинуть позицию BangBang Nachos
                    var itemWithChangedModifiers2 = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "06f19742-383b-4be0-b10f-038508578cbf");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(itemWithChangedModifiers2));

                    // отметить как select модификатор
                    // Snack Modifiers
                    var itemGroupMod2 = itemWithChangedModifiers2.GroupModifiers.FirstOrDefault(gm => gm.Id == "e6fae00c-c035-4122-ad05-15a16ce4512e");
                    var itemMod2 = itemGroupMod2.Modifiers.FirstOrDefault(m => m.Id == "74e687e8-777e-4580-a7ff-be4aa802c1d9");

                    var cartItem2 = currOrder.Cart.Entities.FirstOrDefault();
                    DigitalOrderingModule.Instance.Controller.SelectModifier(cartItem2, itemGroupMod2, itemMod2.Id);

                    break;
                case 3:
                    // закинуть позицию BangBang Nachos
                    var itemWithChangedModifiers3 = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "06f19742-383b-4be0-b10f-038508578cbf");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(itemWithChangedModifiers3));

                    // отметить как select модификатор
                    // Snack Modifiers
                    var itemGroupMod3 = itemWithChangedModifiers3.GroupModifiers.FirstOrDefault(gm => gm.Id == "e6fae00c-c035-4122-ad05-15a16ce4512e");
                    // этого модификатора нет в новом меню
                    var itemMod3 = itemGroupMod3.Modifiers.FirstOrDefault(m => m.Id == "352a4f08-38d2-4df6-97ba-2b1ed8a84ee8");

                    var cartItem3 = currOrder.Cart.Entities.FirstOrDefault();
                    DigitalOrderingModule.Instance.Controller.SelectModifier(cartItem3, itemGroupMod3, itemMod3.Id);

                    break;
                case 4:
                    // Teriyaki Bowl
                    var teriyakiBowl4 = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0b633beb-16bc-4338-a18b-92750b1024f1");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(teriyakiBowl4));

                    break;

                case 5:
                    // Teriyaki Bowl
                    var teriyakiBowl5 = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0b633beb-16bc-4338-a18b-92750b1024f1");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(teriyakiBowl5));

                    // отметить как select модификатор
                    // Starch, inactive в новом меню
                    var itemGroupMod5 = teriyakiBowl5.GroupModifiers.FirstOrDefault(gm => gm.Id == "dd91389e-55b8-4017-9457-98f147a14a1f");
                    var itemMod5 = itemGroupMod5.Modifiers.FirstOrDefault(m => m.Id == "9763e6e1-ff9d-4468-be8f-691eabc25454");

                    var cartItem5 = currOrder.Cart.Entities.FirstOrDefault();
                    DigitalOrderingModule.Instance.Controller.SelectModifier(cartItem5, itemGroupMod5, itemMod5.Id);

                    break;
                case 6:
                    // Teriyaki Bowl
                    var teriyakiBowl6 = DoMenu.MenuItems.FirstOrDefault(m => m.Id == "0b633beb-16bc-4338-a18b-92750b1024f1");
                    currOrder.Cart.AddCartEntity(new CartMenuItem(teriyakiBowl6));

                    // отметить как select модификатор
                    // Vegetables, hidden в новом меню
                    var itemGroupMod6 = teriyakiBowl6.GroupModifiers.FirstOrDefault(gm => gm.Id == "e342e458-7517-499e-a1c5-89bde407d818");
                    var itemMod6 = itemGroupMod6.Modifiers.FirstOrDefault(m => m.Id == "e0f8a98c-e271-4cc9-aaae-d70fbcdf1c91");

                    var cartItem6 = currOrder.Cart.Entities.FirstOrDefault();
                    DigitalOrderingModule.Instance.Controller.SelectModifier(cartItem6, itemGroupMod6, itemMod6.Id);

                    break;
            }

            // берем новое меню
            SetupMockResponse("StubContent/DigitalOrdering_second_menu_changeAddress.json");
            _ = DigitalOrderingModule.Instance.GetMenuAsync().Result;

            // проверяем :)
            var compareResult = DoModule.CompareMenu(DoMenu, out _);

            Assert.Equal(menuResult, compareResult);

            switch (situationNumber)
            {
                // проверить что модификатор selected после того, как меню изменилось
                case 2:
                    var modTxt2 = currOrder.Cart.Entities.FirstOrDefault().ModifiersText;
                    Assert.Equal("No Chickpeas", modTxt2);

                    break;

                case 1:
                case 3:
                case 4:
                case 5:
                case 6:
                    // nothing

                    break;
            }
        }
    }
}
