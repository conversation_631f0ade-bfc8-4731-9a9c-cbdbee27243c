using System;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.Content.Model
{
    public enum PeriodType
    {
        Interval,
        DaysOfWeek
    }

    [Serializable]
    public abstract class ActualPeriod
    {
        public PeriodType PeriodType { get; set; }

        protected ActualPeriod(PeriodType periodType)
        {
            PeriodType = periodType;
        }
    }

    [Serializable]
    public class PeriodInterval : ActualPeriod
    {
        public long BeginTimestamp { get; set; }
        public long EndTimestamp { get; set; }

        public PeriodInterval(long beginTimestamp, long endTimestamp) : base(PeriodType.Interval)
        {
            BeginTimestamp = beginTimestamp;
            EndTimestamp = endTimestamp;
        }

        // APP-2492 время актуальности новости для акции/подарка, пока не трогаем
        public bool IsIntervalAvailableOffer()
        {
            var now = LpDateTime.PartnerNow.DateTimeTotalMillis();
            return now <= BeginTimestamp;
        }

        // APP-2492 время актуальности новости для акции/подарка, пока не трогаем
        public bool IsIntervalAvailablePresent()
        {
            var now = LpDateTime.PartnerNow.DateTimeTotalMillis();
            return now <= EndTimestamp;
        }
    }

    [Serializable]
    public class DaysOfWeekInterval : ActualPeriod
    {
        public long BeginTimestamp { get; set; }
        public long EndTimestamp { get; set; }

        public DaysOfWeekInterval(long beginTimestamp, long endTimestamp)
            : base(PeriodType.DaysOfWeek)
        {
            BeginTimestamp = beginTimestamp;
            EndTimestamp = endTimestamp;
        }
    }

    [Serializable]
    public class PeriodDay
    {
        public DayOfWeek Day;
        public TimeSpan TimeFrom;
        public TimeSpan TimeTo;

        public PeriodDay(string day, string timeFrom, string timeTo)
        {
            switch (day)
            {
                case "mon":
                    Day = DayOfWeek.Monday;
                    break;
                case "tue":
                    Day = DayOfWeek.Tuesday;
                    break;
                case "wed":
                    Day = DayOfWeek.Wednesday;
                    break;
                case "thu":
                    Day = DayOfWeek.Thursday;
                    break;
                case "fri":
                    Day = DayOfWeek.Friday;
                    break;
                case "sat":
                    Day = DayOfWeek.Saturday;
                    break;
                case "sun":
                    Day = DayOfWeek.Sunday;
                    break;
            }
            
            //android safe method no datetimee
            var hourFrom = Convert.ToInt32(timeFrom.Split(':')[0]);
            var hourTo = Convert.ToInt32(timeTo.Split(':')[0]);
            var minuteFrom = Convert.ToInt32(timeFrom.Split(':')[1]);
            var minuteTo = Convert.ToInt32(timeTo.Split(':')[1]);
            TimeFrom = new TimeSpan(hourFrom, minuteFrom, 0);
            TimeTo = new TimeSpan(hourTo, minuteTo, 0);
        }
    }
}
