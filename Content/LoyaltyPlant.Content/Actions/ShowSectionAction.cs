using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.Content.Actions
{
    public class ShowSectionAction : CommonAction
    {
        public ShowSectionAction()
        {
            
        }

        public ShowSectionAction(BundleAction bundleAction) : base(bundleAction)
        {
            
        }
        
        private Section _section;

        public Section Section
        {
            set
            {
                _section = value;
                Id = value.Id;
            }
            get => _section ?? (_section = ContentModule.Instance.GetSection(Id));
        }
    }
}