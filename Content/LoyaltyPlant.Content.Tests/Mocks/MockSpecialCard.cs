using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.Content.Tests.Mocks
{
    public class MockSpecialCard : Card
    {
        public MockSpecialCard(int id) : base(id, LpDateTime.Now.Millisecond, "previewText", 0, 0, "", "")
        {
        }

        public override string ActionButtonText => "ActionButtonText";

        public override bool IsSpecial => true;

        public override string GetCardTypeString()
        {
            return "";
        }

        public override void OnClicked()
        {
        }
    }
}