using System;
using Foundation;
using LoyaltyPlant.App.iOS;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.InviteFriend;
using LoyaltyPlant.InviteFriend.Model;
using LoyaltyPlant.PromoCode;
using LoyaltyPlant.PromoCode.Actions;
using UIKit;

namespace LoyaltyPlant.InviteFriend.iOS
{
    public class InviteFriendFrontend : AppFrontend, IInviteFriendFrontend, IPromoCodeFrontend
    {
        public InviteFriendFrontend(UIWindow window, UINavigationController navigationController) : base(window, navigationController)
        {
        }

        public void SetTextFieldError(string alertMessage)
        {
            InviteFriendModule.Instance.Controller.ShowPopup(alertMessage);
        }

        public void ShowEnterReferralPromocodeScreen(PromoCodeAction screenAction) =>
            ShowEnterPromocodeScreen(screenAction);

        public void ShowEnterPromocodeScreen(PromoCodeAction screenAction)
        {
            new NSObject().BeginInvokeOnMainThread(() =>
                ShowViewController(new PromocodeViewController(screenAction)));
        }

        public void ShowInviteFriendScreen(InviteFriendCard inviteFriendCard)
        {
            new NSObject().BeginInvokeOnMainThread(() => 
                ShowViewController(new InviteFriendViewController(inviteFriendCard)));
        }
    }
}
