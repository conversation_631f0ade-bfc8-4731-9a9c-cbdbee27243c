using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Content.Utils;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Surveys.Tasks;
using LoyaltyPlant.Surveys.Utils;

namespace LoyaltyPlant.Surveys
{
    public class SurveysModule : ControllableModule<SurveysModule, SurveysModel, SurveysController, ISurveysFrontend>, ICardHandler
    {
        public void SaveSurvey(Survey survey)
        {
            if (survey != null)
            {
                SyncAnswersLinkedQuestions(survey.Fields);
                Save();
            }
        }

        // needToSendLog added because of lots of log lines being sent on checkout screen
        public async Task<Survey> GetSurvey(int id, long version, bool needToSendLog = false)
        {
            SendLogIfNeeded("SurveysModule.GetSurvey(): trying to get survey from model by id and version.", needToSendLog);
            
            var survey = GetSurveyFromModel(id, version);
            if (survey != null)
            {
                SendLogIfNeeded("SurveysModule.GetSurvey(): successfully got survey from model.", needToSendLog);
                if (SyncAnswersLinkedQuestions(survey.Fields))
                {
                    Save();
                }
                return survey;
            }
            
            SendLogIfNeeded("SurveysModule.GetSurvey(): couldn't get survey from model, sending \"get-form\" request.", needToSendLog);
            var response = await new LpTask<GetSurveyResponse>().ExecuteAsync(new GetSurveyRequest(id));
            if (response.Ok)
            {
                SendLogIfNeeded("SurveysModule.GetSurvey(): got correct response. Saving survey to model.", needToSendLog);
                var form = response.Form;
                Model.Surveys[form.Id] = form;
                SyncAnswersLinkedQuestions(response.Form.Fields);
                Save();
            }
            else
                SendLogIfNeeded("SurveysModule.GetSurvey(): got incorrect response. There's no form to save to model.", needToSendLog);
            
            SendLogIfNeeded("SurveysModule.GetSurvey(): Trying to get survey from model only by id, ignoring version.", needToSendLog);
            return GetSurveyFromModel(id);
        }

        public Survey GetSurveyFromModel(int id, long version)
        {
            var form = GetSurveyFromModel(id);
            if (form != null && form.Version == version)
            {
                return form;
            }
            return null;
        }        
        
        public Survey GetSurveyFromModel(int id)
        {
            Model.Surveys.TryGetValue(id, out var form);
            return form;
        }

        public async Task<bool> SubmitSurvey(Survey survey)
        {
            var response = await new LpTask<SubmitSurveyResponse>().ExecuteAsync(new SubmitSurveyRequest(survey));

            if (response.Success)
            {
                SyncAnswersLinkedQuestions(survey.Fields);
                survey.ResultText = response.ResultText;
                survey.CurrentFilledFormId = response.CurrentFilledFormId;
                return true;
            }

            return false;
        }

        public bool CheckAllAnswersEntered(List<IField> fields)
        {
            var allAnswersCorrect = true;

            foreach (var field in fields)
            {
                if (!field.HasAnswer() && field.Required)
                {
                    LpLogger.LOG_W($"answer for {field} not entered");
                    allAnswersCorrect = false;
                    break;
                }
            }

            return allAnswersCorrect;
        }

        private static bool SyncAnswersLinkedQuestions(IList<IField> formFields)
        {
            var moduleWithQuestions = Engine.Instance.GetModule<ILinkableQuestions>();
            if (moduleWithQuestions == null)
            {
                return false;
            }

            var changedFormFields = false;

            foreach (var field in formFields)
            {
                if (field.LinkedQuestionId != -1)
                {
                    var question = moduleWithQuestions.GetFieldById(field.LinkedQuestionId);
                    if (question != null)
                    {
                        if (question.HasAnswer() && !field.HasAnswer())
                        {
                            field.SetEnteredAnswerForOtherField(question);
                            changedFormFields = true;
                            continue;
                        }
                        
                        if (field.HasAnswer() && !question.HasAnswer())
                        {
                            question.SetEnteredAnswerForOtherField(field);
                            moduleWithQuestions.OnFieldChanged(question);
                        }
                    }
                }
            }

            return changedFormFields;
        }

        public Card UpdateCard(XElement element, string type)
        {
            Card newCard = null;
            if (type == "open-url" && element.ToString().Contains("lpform://"))
            {
                var card = SurveyCard.Create(element);
                Model.SurveyCards.SwapOrAddCard(card);
                newCard = card;
            }
            return newCard;
        }

        public void RemoveCard(int id)
        {
            Model.SurveyCards.RemoveAll(c => c.Id == id);
        }

        public IList<Card> GetCards()
        {
            var cards = new List<Card>();
            cards.AddRange(Model.SurveyCards);
            return cards;
        }

        // APP-4354. Требуется сбросить кеш после смены языка
        public void UpdateSurveysIfNeeded(bool wasUpdated)
        {
            if (wasUpdated)
            {
                Model.Surveys.Clear();
            }
        }

        private static void SendLogIfNeeded(string log, bool needToSendLog)
        {
            if (needToSendLog)
                LpLogger.LOG_I(log);
        }
    }
}
