using System;
using System.Text;
using CoreGraphics;
using Foundation;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.iOS.Fields;
using LoyaltyPlant.Surveys.Model;
using UIKit;

namespace LoyaltyPlant.Surveys.iOS
{
    public class StringFieldView : BaseFieldView, IFieldView
    {
        private UIDatePicker picker;
        private string initialFieldText = string.Empty;

        public StringFieldView(StringField field, nfloat width) : base(field, width)
        {
            Field = field;
            this.Tag = field.Id;
            this.SetWidth(width);

            SetTitleLabel();

            SetPlaceholder(field is PhoneField placeholderPhoneField ? placeholderPhoneField.GetPlaceholderTextForView() : field.Placeholder);

            SetTextField();

            if (string.IsNullOrWhiteSpace(TextField.Text))
                Placeholder.Hidden = false;
            else
                Placeholder.Hidden = true;

            SetLine();

            if (field.Required)
            {
                SetRequiredDot(TextField);
            }

            SetErrorSign(TextField);

            SetErrorLabel();

            TextField.Started += (sender, e) =>
            {
                Placeholder.Hidden = true;
                Line.BackgroundColor = Colors.DefaultTextColor;

                if (field is DateField && String.IsNullOrWhiteSpace(TextField.Text))
                {
                    var date = (field.Meta == Meta.Birthday) ? new DateTime(1980, 1, 1) : LpDateTime.Now;
                    TextField.Text = LpDateTime.DateToStringCurrentFormat(date);
                    field.SetAnswer(LpDateTime.DateToStringServerFormat(date));
                    picker.Date = date.DateTimeToNSDate();
                    if (field.HasAnswer())
                        Placeholder.Hidden = true;
                }

                HideError();
            };

            if (Field is PhoneField phoneField)
            {
                TextField.AddTarget((se, args) =>
                {
                    initialFieldText = TextField.Text;
                }, UIControlEvent.EditingDidBegin);

                TextField.AddTarget((se, args) =>
                {
                    if (string.IsNullOrEmpty(TextField.Text))
                    {
                        initialFieldText = string.Empty;
                        return;
                    }
                    var preprocessedResultingText = PreprocessPhoneInput(TextField.Text);

                    Action<string> setTextAndSelection = (string value) =>
                    {
                        TextField.Text = value;
                        TextField.SelectedTextRange = TextField.GetTextRange(TextField.EndOfDocument, TextField.EndOfDocument);
                    };

                    if (RegularExpressions.InputPhoneNumber(preprocessedResultingText))
                    {
                        if (!Engine.Instance.IsFeatureActive(PhoneField.IGNORE_PHONE_REGULAR_EXPRESSION))
                            preprocessedResultingText = phoneField.ProcessPlusesForInput(preprocessedResultingText, out bool isChanged);

                        setTextAndSelection(preprocessedResultingText);
                    }
                    else
                    {
                        setTextAndSelection(initialFieldText);
                    }
                }, UIControlEvent.EditingChanged);
            }

            TextField.Ended += (sender, e) =>
            {
                Line.BackgroundColor = Colors.DividerTextColor;

                if (field is DateField)
                {
                    if (LpDateTime.ParseFromCurrentDateFormat(TextField.Text, out var dateTime))
                    {
                        field.SetAnswer(LpDateTime.DateToStringServerFormat(dateTime));
                    }
                }
                else if (field is TimeField)
                {
                    if (LpDateTime.ParseFromCurrentTimeFormat(TextField.Text, out var dateTime))
                    {
                        field.SetAnswer(LpDateTime.TimeToStringServerFormat(dateTime));
                    }
                }
                else
                {
                    field.SetAnswer(TextField.Text);
                }

                Placeholder.Hidden = !string.IsNullOrWhiteSpace(TextField.Text);

                if (initialFieldText != TextField.Text
                    && field is PhoneField checkoutPhoneField
                    && TextField.Superview?.Superview is FieldsView textFieldSuperview
                    && textFieldSuperview.IsCheckoutScreen
                    && DigitalOrderingModule.Instance.CurrentOrder.IsPhoneChangedMessageAvailableForCurrentOrder()
                    && checkoutPhoneField.HasAnswer())
                {
                    textFieldSuperview.ShowMessageWhenPhoneNumberChangedOnCheckout();
                }
            };

            if (field is TextBoxField && (field as TextBoxField).TextBoxFieldType == TextBoxType.Multiline)
            {
                TextField.EditingChanged += (sender, e) =>
                {
                    var delta = ResizeViewWithText();
                    this.SetHeight(this.Frame.Height + delta);
                    (this.Superview as FieldsView)?.ResizeViewWithText(field, delta);
                };

                var d = ResizeViewWithText();
                this.SetHeight(this.Frame.Height + d);
            }

            TextField.AutocorrectionType = UITextAutocorrectionType.No;

            if (field is EmailField)
            {
                TextField.KeyboardType = UIKeyboardType.EmailAddress;
                TextField.AutocapitalizationType = UITextAutocapitalizationType.None;
            }
            else if (field is PhoneField)
            {
                TextField.TextContentType = UITextContentType.TelephoneNumber;
                TextField.KeyboardType = UIKeyboardType.PhonePad;
            }
            else if (field is DateField)
            {
                CreateDatePicker();
            }
            else if (field is TimeField)
            {
                CreateTimePicker();
            }
            else if (field is TextBoxField textBoxField)
            {
                switch (textBoxField.GetKeyboardType())
                {
                    case KeyboardTypes.Email:
                        TextField.KeyboardType = UIKeyboardType.EmailAddress;
                        TextField.AutocapitalizationType = UITextAutocapitalizationType.None;
                        break;
                    case KeyboardTypes.Phone:
                        TextField.TextContentType = UITextContentType.TelephoneNumber;
                        TextField.KeyboardType = UIKeyboardType.PhonePad;
                        break;
                    case KeyboardTypes.Digits:
                        TextField.KeyboardType = UIKeyboardType.NumberPad;
                        break;
                    case KeyboardTypes.CamelCase:
                        TextField.KeyboardType = UIKeyboardType.Default;
                        TextField.AutocapitalizationType = UITextAutocapitalizationType.Words;
                        break;
                    case KeyboardTypes.Password:
                        TextField.SecureTextEntry = true;
                        TextField.AutocapitalizationType = UITextAutocapitalizationType.None;
                        break;
                    case KeyboardTypes.Default:
                        TextField.KeyboardType = UIKeyboardType.Default;
                        TextField.AutocapitalizationType = UITextAutocapitalizationType.None;
                        break;

                }
            }
        }

        public void SetMaxLength(int length)
        {
            TextField.ShouldChangeCharacters = (textField, range, replacementString) =>
            {
                var newLength = textField.Text.Length + replacementString.Length - range.Length;
                return newLength <= length;
            };
        }

        private string PreprocessPhoneInput(string phoneInputString)
        {
            var resultString = new StringBuilder();

            foreach (char c in phoneInputString)
            {
                if (c != '-' && c != '(' && c != ')' && c != ' ')
                    resultString.Append(c);
            }
            return resultString.ToString().Replace("++", "+");
        }

        private nfloat ResizeViewWithText()
        {
            if (TextField.Frame.Height == Placeholder.Frame.Height + 2 * Dimensions.PaddingSmall
                && string.IsNullOrWhiteSpace(TextField.Text))
            {
                return 0;
            }

            var minHeight = Placeholder.Frame.Height + 2 * Dimensions.PaddingSmall;
            var prevHeight = TextField.Frame.Height;

            var lbl = new UILabel(TextField.Bounds);
            lbl.Font = TextField.Font;
            lbl.Lines = 0;
            lbl.Text = TextField.Text;
            lbl.SizeHeightToFit();

            TextField.SetHeight(lbl.Frame.Height + 2 * Dimensions.PaddingSmall);

            if (TextField.Frame.Height < minHeight)
                TextField.SetHeight(minHeight);

            Line.SetLocation(0, TextField.Frame.Bottom);
            ErrorSign.SetCenterY(TitleLabel.Frame.Bottom + TextField.Frame.Height / 2);
            ErrorLabel.SetTop(Line.Frame.Bottom + 4f * Dimensions.Multiplier);

            var delta = TextField.Frame.Height - prevHeight;
            return delta;
        }

        public UIDatePicker CreateDatePicker()
        {
            picker = new UIDatePicker(CGRect.Empty);

            picker.Locale = NSLocale.FromLocaleIdentifier(Engine.Instance.Platform.CurrentCulture.ToString());
            picker.Mode = UIDatePickerMode.Date;
            picker.TimeZone = NSTimeZone.LocalTimeZone;
            if (UIDevice.CurrentDevice.CheckIOSVersion(14))
                picker.PreferredDatePickerStyle = UIDatePickerStyle.Wheels;
            picker.UserInteractionEnabled = true;
            picker.SetBottom(Dimensions.Height);
            // 2198705417 - количество секунд с 30.04.1900 (это минимально допустимая дата в грегорианском календаре) до 01.01.1970
            // 3404246399 - количество секунд с 01.01.1970 до 11.16.2077 - максимально допустимая дата грегорианского календаря
            picker.MinimumDate = NSDate.FromTimeIntervalSince1970(-2198705417);
            picker.MaximumDate = NSDate.FromTimeIntervalSince1970(3404246399);

            var calendar = new NSCalendar(NSCalendarType.Gregorian);
            picker.Calendar = calendar;

            try
            {
                if (!string.IsNullOrWhiteSpace(TextField.Text) && LpDateTime.ParseFromCurrentDateFormat(TextField.Text, out var dateTime))
                {
                    picker.Date = dateTime.DateTimeToNSDate();
                }
            }
            catch
            {
                this.TextField.Text = "";
                Placeholder.Hidden = false;
            }
            TextField.InputView = picker;

            {
                picker.RemoveFromSuperview();
                picker.BackgroundColor = UIColor.White;
            }

            picker.ValueChanged += (s, e) =>
                TextField.Text = (s as UIDatePicker).Date.LpStringFormat();

            return picker;
        }

        public UIDatePicker CreateTimePicker()
        {
            var picker = new UIDatePicker(CGRect.Empty);
            picker.Mode = UIDatePickerMode.Time;
            picker.TimeZone = NSTimeZone.SystemTimeZone;
            if (UIDevice.CurrentDevice.CheckIOSVersion(14))
                picker.PreferredDatePickerStyle = UIDatePickerStyle.Wheels;
            picker.UserInteractionEnabled = true;
            picker.SetBottom(Dimensions.Height);

            if (LpDateTime.ParseFromServerTimeFormat((Field as TimeField).MinValue, out var minTimeFromText))
            {
                picker.MinimumDate = DateTime.Now.Date.Add(minTimeFromText).DateTimeToNSDate().ToLocalTime();
            }

            if (LpDateTime.ParseFromServerTimeFormat((Field as TimeField).MaxValue, out var maxTimeFromText))
            {
                picker.MaximumDate = DateTime.Now.Date.Add(maxTimeFromText).DateTimeToNSDate().ToLocalTime();
            }

            if (!string.IsNullOrWhiteSpace(TextField.Text))
            {
                var time = TextField.Text.ToNsTimeLpStringFormat();
                if (time != null)
                    picker.Date = time;
            }
            TextField.InputView = picker;

            {
                picker.RemoveFromSuperview();
                picker.BackgroundColor = UIColor.White;
            }

            picker.ValueChanged += (s, e) =>
                TextField.Text = (s as UIDatePicker).Date.LpStringFormatTime();

            return picker;
        }

        public override bool ResignFirstResponder()
        {
            return TextField.ResignFirstResponder();
        }

        public override bool BecomeFirstResponder()
        {
            return TextField.BecomeFirstResponder();
        }

        public string GetAnswer()
        {
            return TextField.Text;
        }

        public override bool IsResponsible()
        {
            return true;
        }

        public override void DoKeyboardNextAction()
        {
            this.TextField.ResignFirstResponder();

            if (this.Field is DateField df && df.EnteredAnswer != null)
            {
                TextField.Text = df.GetAnswerValueFormattedString();
            }

            if (this.Field is TimeField tf && tf.EnteredAnswer != null)
            {
                TextField.Text = tf.GetAnswerValueFormattedString();
            }

            if (string.IsNullOrWhiteSpace(TextField.Text))
                Placeholder.Hidden = false;
            else
                Placeholder.Hidden = true;
        }

        public void RemoveTitleLabel()
        {
            if (TitleLabel == null)
                return;

            var titleLabelHeight = TextField.Frame.Top;
            TitleLabel.RemoveFromSuperview();
            this.SetHeight(Frame.Height - titleLabelHeight);
            Bounds = new CGRect(Bounds.X, Bounds.Y + titleLabelHeight, Bounds.Width, Bounds.Height);
        }

        public void HideRequiredMark()
        {
            HideRequiredDot();
        }
    }
}
