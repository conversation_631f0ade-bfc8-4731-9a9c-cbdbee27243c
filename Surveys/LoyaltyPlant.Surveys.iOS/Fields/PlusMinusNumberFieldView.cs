using System;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.iOS.Fields;
using UIKit;

namespace LoyaltyPlant.Surveys.iOS
{
    public class PlusMinusNumberFieldView : BaseFieldView, IFieldView
    {
        private AddRemoveControl _addRemoveControl { get; }

        public PlusMinusNumberFieldView(PlusMinusField field, nfloat width) : base(field, width)
        {
            Field = field;
            this.Tag = field.Id;
            this.SetWidth(width);

            SetTitleLabel();

            this.SetHeight(TitleLabel.Frame.Bottom + Dimensions.Padding);

            _addRemoveControl = new AddRemoveControl(field.EnteredAnswer, field.MinValue, field.MaxValue, Dimensions.BigFont, () => 
            {
                field.SetAnswer(_addRemoveControl.GetCount());
            }, () =>
            {
                field.SetAnswer(_addRemoveControl.GetCount());
            });
            _addRemoveControl.SetRight(this.Frame.Width);
            _addRemoveControl.SetInCenterVertical(this);
            this.AddSubview(_addRemoveControl);

            TitleLabel.SetWidth(TitleLabel.Frame.Width - _addRemoveControl.Frame.Width - Dimensions.PaddingSmall);

            if (field.EnteredAnswer > 0)
            {
                _addRemoveControl.SetCount(field.EnteredAnswer);
            }
        }

        public int GetAnswer()
        {
            return _addRemoveControl.GetCount();
        }

        public override bool IsResponsible()
        {
            return false;
        }

        public override void DoKeyboardNextAction()
        {

        }

        public override void ShowError(string error)
        {
        }
    }
}
