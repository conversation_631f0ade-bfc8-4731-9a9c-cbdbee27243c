<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A9A215AF-C3EC-47B4-8F80-A4DAEC3BE6FF}</ProjectGuid>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>LoyaltyPlant.Surveys.iOS</RootNamespace>
    <AssemblyName>LoyaltyPlant.Surveys.iOS</AssemblyName>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <MtouchNoSymbolStrip>true</MtouchNoSymbolStrip>
    <MtouchFastDev>true</MtouchFastDev>
    <IOSDebuggerPort>15631</IOSDebuggerPort>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <DeviceSpecificBuild>false</DeviceSpecificBuild>
    <MtouchVerbosity>
    </MtouchVerbosity>
    <MtouchLink>
    </MtouchLink>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchNoSymbolStrip>true</MtouchNoSymbolStrip>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <MtouchVerbosity>
    </MtouchVerbosity>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.iOS" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SurveysFrontend.cs" />
    <Compile Include="SurveyViewController.cs" />
    <Compile Include="Fields\CheckboxesSelectFieldView.cs" />
    <Compile Include="Fields\FieldsView.cs" />
    <Compile Include="Fields\IFieldView.cs" />
    <Compile Include="Fields\PlusMinusNumberFieldView.cs" />
    <Compile Include="Fields\SpinnerFieldView.cs" />
    <Compile Include="Fields\StringFieldView.cs" />
    <Compile Include="Fields\TextBlockFieldView.cs" />
    <Compile Include="Fields\TogglesSelectFieldView.cs" />
    <Compile Include="Fields\MultilineStringFieldView.cs" />
    <Compile Include="Fields\BaseFieldView.cs" />
    <Compile Include="Fields\ViewModels\ComboboxPickerModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Languages\LoyaltyPlant.Languages\LoyaltyPlant.Languages.csproj">
      <Project>{a684cd2f-2eb6-4497-adff-4fec041f4427}</Project>
      <Name>LoyaltyPlant.Languages</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Partner\LoyaltyPlant.Partner\LoyaltyPlant.Partner.csproj">
      <Project>{707FFD41-EFFE-4215-92C5-21CE81F4BF0C}</Project>
      <Name>LoyaltyPlant.Partner</Name>
    </ProjectReference>
    <ProjectReference Include="..\LoyaltyPlant.Surveys\LoyaltyPlant.Surveys.csproj">
      <Project>{48A5794D-EDEB-4539-BB94-D9829CB4754F}</Project>
      <Name>LoyaltyPlant.Surveys</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Texts\LoyaltyPlant.Texts.csproj">
      <Project>{2E749BDD-98E9-4B1E-BC7F-CBF006C8A453}</Project>
      <Name>LoyaltyPlant.Texts</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.View\LoyaltyPlant.View.csproj">
      <Project>{05E6F7F8-997D-47A7-8C14-D3583384B80B}</Project>
      <Name>LoyaltyPlant.View</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Core\LoyaltyPlant.Core.csproj">
      <Project>{8227D169-A02F-4C18-8563-CAC13B7B33F5}</Project>
      <Name>LoyaltyPlant.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Backend\LoyaltyPlant.App.iOS\LoyaltyPlant.App.iOS.csproj">
      <Name>LoyaltyPlant.App.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\DigitalOrdering\LoyaltyPlant.DigitalOrdering\LoyaltyPlant.DigitalOrdering.csproj">
      <Name>LoyaltyPlant.DigitalOrdering</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
</Project>