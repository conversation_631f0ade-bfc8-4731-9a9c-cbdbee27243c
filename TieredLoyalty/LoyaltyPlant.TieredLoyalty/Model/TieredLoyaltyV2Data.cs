using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using static LoyaltyPlant.Core.Utils.NetExtentions;

namespace LoyaltyPlant.TieredLoyalty.Model
{
    [Serializable]
    [Preserve(AllMembers = true)]
    public class TieredLoyaltyV2Data
    {
        [JsonProperty("accepted")]
        public bool Accepted { get; set; }

        [JsonProperty("tierLossWarning")]
        public bool TierLossWarning { get; set; }

        [JsonProperty("motivatingText")]
        public string MotivatingText { get; set; }

        [JsonProperty("lineLoadPercentage")]
        public int LoadPercentage { get; set; }

        [JsonProperty("chargesType")]
        public string ChargesType { get; set; }

        [JsonProperty("tiers")]
        public List<Tier> Tiers { get; set; } = new List<Tier>();

        [JsonProperty("errors")]
        public List<string> Errors { get; set; } = new List<string>();

        [Serializable]
        [Preserve(AllMembers = true)]
        public class Tier
        {
            [JsonProperty("grade")]
            public int Grade { get; set; }
            
            [JsonProperty("title")]
            public string Title { get; set; }
            
            [JsonProperty("titleColor")]
            public string TitleColor { get; set; }
            
            [JsonProperty("subtitle")]
            public string Subtitle { get; set; }
            
            [JsonProperty("subtitleColor")]
            public string SubtitleColor { get; set; }
            
            [JsonProperty("motivatingTextColor")]
            public string MotivatingTextColor { get; set; }
            
            [JsonProperty("progressBarColor")]
            public string ProgressBarColor { get; set; }
            
            [JsonProperty("progressBarAmountMissingForRenewalColor")]
            public string ProgressBarAmountMissingForRenewalColor { get; set; }
            
            [JsonProperty("progressBarBackgroundColor")]
            public string ProgressBarBackgroundColor { get; set; }
            
            [JsonProperty("couponId")]
            public int CouponId { get; set; }
            
            [JsonProperty("cardBackgroundColor")]
            public string CardBackgoundColor { get; set; }
            
            [JsonProperty("coinColor")]
            public string CoinColor { get; set; }
            
            [JsonProperty("pointsAnimationBackgroundColor")]
            public string PointsAnimationBackgroundColor { get; set; }
            
            [JsonProperty("pointsAnimationTextColor")]
            public string PointsAnimationTextColor { get; set; }
            
            [JsonProperty("callToActionColor")]
            public string CallToActionColor { get; set; }
            
            [JsonProperty("callToActionTextColor")]
            public string CallToActionTextColor { get; set; }

            [JsonProperty("currentTierBadgePicture")]
            public string CurrentTierBadgePicture { get; set; }

            [JsonProperty("nextTierBadgePicture")]
            public string NextTierBadgePicture { get; set; }
            
            [JsonProperty("lottieAnimationToPlayOnUpgradeLocalFile")]
            public string LottieAnimation { get; set; }

            [JsonProperty("tierDistanceOnLine")]
            public int TierDistanceOnLine { get; set; }
            
            [JsonProperty("linePercentageForRenewal")]
            public int LinePercentageForRenewal { get; set; }
            
            [JsonProperty("currentLevel")]
            public bool IsCurrentLevel { get; set; }

            [JsonProperty("warningAnimationParameters")]
            public TieredLoyaltyAnimationValues AnimationParameters { get; set; }

            [JsonProperty("hintValues")]
            public TieredLoyaltyHintValues HintValues { get; set; }

            [JsonProperty("hintValuesRenewals")]
            public TieredLoyaltyHintValues HintValuesRenewals { get; set; }

            public bool ContainsAllHintValues()
            {
                bool isNotNull(string stringValue)
                {
                    return !string.IsNullOrEmpty(stringValue) && stringValue != "null";
                };
                return isNotNull(HintValues.HintTextColor)
                       && isNotNull(HintValues.HintBorderColor)
                       && isNotNull(HintValues.HintBackgroundColor)
                       && isNotNull(HintValues.HintFirstLineText)
                       && isNotNull(HintValues.HintSecondLineText);
            }
        }

        [Serializable]
        [Preserve(AllMembers = true)]
        public class TieredLoyaltyHintValues
        {
            [JsonProperty("hintBorderColor")]
            public string HintBorderColor { get; set; }
            
            [JsonProperty("hintBackgroundColor")]
            public string HintBackgroundColor { get; set; }
            
            [JsonProperty("hintFirstLineText")]
            public string HintFirstLineText { get; set; }
            
            [JsonProperty("hintSecondLineText")]
            public string HintSecondLineText { get; set; }
            
            [JsonProperty("hintTextColor")]
            public string HintTextColor { get; set; }

            public bool ContainsAllHintValues()
            {
                bool isNotNull(string stringValue)
                {
                    return !string.IsNullOrEmpty(stringValue) && stringValue != "null";
                };
                return isNotNull(HintTextColor)
                       && isNotNull(HintBorderColor)
                       && isNotNull(HintBackgroundColor)
                       && isNotNull(HintFirstLineText)
                       && isNotNull(HintSecondLineText);
            }
        }

        [Serializable]
        [Preserve(AllMembers = true)]
        public class TieredLoyaltyAnimationValues
        {
            [JsonProperty("transparencyMax")]
            public int TransparencyMax { get; set; }

            [JsonProperty("transparencyMin")]
            public int TransparencyMin { get; set; }

            [JsonProperty("animationColor")]
            public string AnimationColor { get; set; }
        }
    }
}
