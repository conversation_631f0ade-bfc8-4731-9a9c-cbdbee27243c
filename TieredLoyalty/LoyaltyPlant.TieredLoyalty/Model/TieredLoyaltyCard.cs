using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Texts;


namespace LoyaltyPlant.TieredLoyalty.Model
{
    [Serializable]
    public class TieredLoyaltyCard : Card
    {
        public List<Level> Levels { get; } = new List<Level>();
        public bool IsTieredLoyaltyV2Card;
        
        public TieredLoyaltyCard(int id, long version, string previewText, int sectionId, long priority, bool isTieredLoyaltyV2Card) : 
            base(id, version, previewText, sectionId, priority, null)
        {
            IsTieredLoyaltyV2Card = isTieredLoyaltyV2Card;
        }
        
        public static TieredLoyaltyCard Create(XElement element, bool isTieredLoyaltyV2Card)
        {
            var card = new TieredLoyaltyCard(Convert.ToInt32(element.GetAttributeValue("card-id")),
                long.Parse(element.GetAttributeValue("version")),
                element.GetCData("previewtext"),
                Convert.ToInt32(element.GetAttributeValue("section-id")),
                Convert.ToInt32(element.GetAttributeValueTag("priority")),
                isTieredLoyaltyV2Card);

            if (isTieredLoyaltyV2Card)
            {
                var tlModel = TieredLoyaltyModule.Instance.Model;
                if (Engine.Instance.Platform.PlatformNumber == PlatformType.Android &&
                    !tlModel.IsTieredLoyaltyV2Enabled)
                    return null;
                else
                    return card;
            }
            
            ParseLevels(card, element);

            return card;
        }

        private static void ParseLevels(TieredLoyaltyCard card, XElement rootElement)
        {
            card.Levels.Clear();
            foreach (var element in rootElement.GetElementsList("levels", "level"))
            {
                var level = new Level();
                level.Id = Convert.ToInt32(element.GetAttributeValue("level-id"));
                level.RequiredValue = Convert.ToInt32(element.GetAttributeValue("required-value"));
                level.Order = Convert.ToInt32(element.GetAttributeValue("order"));
                level.Name = element.GetCData("name");
                level.Reward = element.GetCData("reward");
                card.Levels.Add(level);
            }
            card.Levels.Sort();
        }
        
        public override string GetCardTypeString()
        {
            return IsTieredLoyaltyV2Card ? "tiered-loyalty-v2-card" : "tiered-loyalty-card";
        }

        public override void OnClicked()
        {
            if (IsTieredLoyaltyV2Card)
            {
                var currentTier = TieredLoyaltyModule.Instance.GetCurrentTier();
                
                if (currentTier == null)
                    return;
                
                var cards = ContentModule.Instance.GetAllCards();
                var tierCard = cards.FirstOrDefault(card => card.Id == currentTier.CouponId);
                if (tierCard == default)
                    return;
                
                tierCard.OnClicked();
            }
            else
            {
                var tieredLoyaltyFlagDescription = I18N.TIERED_LOYALTY_DESCRIPTION;
                if (!string.IsNullOrWhiteSpace(tieredLoyaltyFlagDescription))
                {
                    var card = ContentModule.Instance.GetAllCards().FirstOrDefault(c => c.PreviewText == tieredLoyaltyFlagDescription);
                    card?.OnClicked();
                } 
            }
        }
    }
}