using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Geofencing.Model;

namespace LoyaltyPlant.Geofencing
{
    [Serializable]

    // !!! Обязательно прочти перед тем, как сохранить изменения в этом классе
    // Если тут что-то поменял в этом классе, то не забудь
    // УвЕлИчИтЬ на 1 CacheVersion !!!

    public class GeofencingModel : IModuleModel
    {
        public int CacheVersion => 7;
        
        public long DisableTimestamp { get; set; }
        public List<GeofenceZone> GeofenceZones { get; set; } = new List<GeofenceZone>();
        public long Version { get; set; }
        public bool Enabled { get; set; }
    }
}
