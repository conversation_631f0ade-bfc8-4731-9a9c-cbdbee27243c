using System;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.MakeCall.Model;
using UIKit;

namespace LoyaltyPlant.MakeCall.iOS
{
    public class MakeCallViewController : BaseViewController
	{
		readonly MakeCallCard card;

		public override UIColor NavigationBarTitleColor => Colors.DefaultTextColor;
		public override UIColor NavigationBarActionButtonsColor => Colors.DefaultTextColor;
		
		public MakeCallViewController(MakeCallCard card)
		{
			this.card = card;
		}

		public override void ViewDidLoad()
		{
            base.ViewDidLoad();
            SetGradientForBackground(View, Colors.SecondaryBackgroundGradientUpColor, Colors.SecondaryBackgroundGradientDownColor,
                                     Colors.SecondaryBackgroundColor);
			
			var scrollView = new UIScrollView(View.Bounds);
			scrollView.SetHeight(scrollView.Frame.Height - Dimensions.TopBarHeight);
			scrollView.ShowsVerticalScrollIndicator = false;
			View.AddSubview(scrollView);

            var header = CreateHeader(card.PreviewText, Width - 2 * Dimensions.PaddingBig, Colors.DefaultTextColor);
            header.SetLocation(Dimensions.Padding, 0);
            header.AccessibilityIdentifier = "headerText";
            scrollView.AddSubview(header);

            var top = header.Frame.Bottom + Dimensions.PaddingSmall;
            
            foreach (var button in card.Buttons)
			{
				var cell = CreateCell(button.Phone, "J", button.PreviewText, () => 
				{
                    MakeCallModule.Instance.Controller.MakeCall(button.Phone);
				});

                cell.SetTop(top);
                scrollView.AddSubview(cell);
                top = cell.Frame.Bottom;
            }

            scrollView.ContentSize = new CGSize(scrollView.Frame.Width, top);
        }


		protected override ApplicationScreenType GetApplicationScreenType()
        {
            return ApplicationScreenType.MakeCallCardScreen;
        }

		private UIView CreateCell(string buttonPhonetext, string iconText, string text, Action action)
		{
			var view = new UIView(new CGRect(0, 0, Width, 0f));
			view.AccessibilityIdentifier = iconText;

            var header = new UILabel(new CGRect(Dimensions.PaddingBig, Dimensions.Padding,
                                                view.Frame.Width - 2* Dimensions.PaddingBig, 0));
            header.SetFont(UniversalUtils.FontStyle.Default);
            header.TextColor = Colors.DefaultTextColor;
            header.Text = text;
            header.Lines = 0;
            header.SetLineHeight(22f);
            header.SizeToFit();
            view.AddSubview(header);

			var icon = new UILabel();
            icon.SetTop(header.Frame.Bottom + Dimensions.PaddingSmall);
			icon.TextColor = Colors.AccentColor;
			icon.Text = iconText;
            icon.Font = UIFont.FromName("icon_font", Dimensions.NewDefaultFont);
			icon.SizeToFit();
			icon.SetLeft(Dimensions.PaddingBig);
			view.AddSubview(icon);

            var actionText = new UILabel();
            actionText.SetTop(icon.Frame.Top);
            actionText.TextColor = Colors.AccentColor;
            actionText.Text = buttonPhonetext;
            actionText.SetFont(UniversalUtils.FontStyle.Default);
            actionText.SetLeft(icon.Frame.Right + Dimensions.Padding);
            actionText.SetWidth(Width - Dimensions.PaddingBig - actionText.Frame.Left);
            actionText.LineBreakMode = UILineBreakMode.TailTruncation;
            actionText.SizeHeightToFit();
            view.AddSubview(actionText);

            view.SetHeight(actionText.Frame.Bottom + Dimensions.Padding);
			view.SetMaterialSelection(action);

            var line = new UILine(view.Frame.Width - 2*Dimensions.PaddingBig);
            line.SetLeft(Dimensions.PaddingBig);
            line.SetBottom(view.Frame.Height);
			view.AddSubview(line);

            return view;
		}
	}
}
