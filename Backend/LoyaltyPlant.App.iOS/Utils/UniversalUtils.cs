using System;
using CoreAnimation;
using CoreGraphics;
using Foundation;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Utils;
using UIKit;
using System.Linq;
using System.Text.RegularExpressions;
using LoyaltyPlant.View;
using Xamarin.Essentials;

namespace LoyaltyPlant.App.iOS.Utils
{
    public static class UniversalUtils
    {
        public static void SetGradientColor(this UINavigationBar view, UIColor first, UIColor second, UIColor defaultColor)
        {
            nfloat r1, g1, b1, a1, r2, g2, b2, a2, r3, g3, b3, a3;

            defaultColor.GetRGBA(out r1, out g1, out b1, out a1);
            first.GetRGBA(out r2, out g2, out b2, out a2);
            second.GetRGBA(out r3, out g3, out b3, out a3);

            if (r1 == r2 && r1 == r3 && g1 == g2 && g1 == g3 && b1 == b2 && b1 == b3 && a1 == a2 && a1 == a3)
                return;

            CAGradientLayer gradient = new CAGradientLayer();
            gradient.Frame = new CGRect(0, 0, view.Frame.Width, view.Frame.Height + 20f);
            gradient.Colors = new CGColor[] { first.CGColor, second.CGColor };

            UIGraphics.BeginImageContext(new CGSize(view.Frame.Width, view.Frame.Height + 20f));
            gradient.RenderInContext(UIGraphics.GetCurrentContext());
            UIImage outputImage = UIGraphics.GetImageFromCurrentImageContext();
            UIGraphics.EndImageContext();
            view.SetBackgroundImage(outputImage, UIBarMetrics.Default);
        }

        public static void SetGradientColor(this UIView view, UIColor first, UIColor second, UIColor defaultColor)
        {
            nfloat r1, g1, b1, a1, r2, g2, b2, a2, r3, g3, b3, a3;

            defaultColor.GetRGBA(out r1, out g1, out b1, out a1);
            first.GetRGBA(out r2, out g2, out b2, out a2);
            second.GetRGBA(out r3, out g3, out b3, out a3);

            if (r1 == r2 && r1 == r3 && g1 == g2 && g1 == g3 && b1 == b2 && b1 == b3 && a1 == a2 && a1 == a3)
                return;

            CAGradientLayer gradient = new CAGradientLayer();
            gradient.Frame = view.Bounds;
            gradient.Colors = new CGColor[] { first.CGColor, second.CGColor };
            view.Layer.AddSublayer(gradient);
        }

        public static void SetGradientColorForBackground(this UIView view, UIColor first, UIColor second, UIColor defaultColor)
        {
            nfloat r1, g1, b1, a1, r2, g2, b2, a2, r3, g3, b3, a3;

            defaultColor.GetRGBA(out r1, out g1, out b1, out a1);
            first.GetRGBA(out r2, out g2, out b2, out a2);
            second.GetRGBA(out r3, out g3, out b3, out a3);

            if (r1 == r2 && r1 == r3 && g1 == g2 && g1 == g3 && b1 == b2 && b1 == b3 && a1 == a2 && a1 == a3)
            {
                view.BackgroundColor = first;
                return;
            }

            CAGradientLayer gradient = new CAGradientLayer();
            gradient.Frame = view.Bounds;
            gradient.Colors = new CGColor[] { first.CGColor, second.CGColor, second.CGColor };
            view.Layer.InsertSublayer(gradient, 0);
        }

        public static void MakeCardViewShadow(this UIView view)
        {
            view.Layer.CornerRadius = 0f;
            var shadowPath = UIBezierPath.FromRoundedRect(new CGRect(1f, 0, view.Bounds.Width - 2f, view.Bounds.Height), 0);

            view.Layer.MasksToBounds = false;
            view.Layer.ShadowColor = UIColor.Black.CGColor;
            view.Layer.ShadowOffset = new CGSize(0f, 0.3f);
            view.Layer.ShadowOpacity = 0.25f;
            view.Layer.ShadowPath = shadowPath.CGPath;
        }

        public static void MakeCoinLabel(this UILabel label, float size, UIColor defaultColor)
        {
            label.Font = UIFont.FromName("icomoon", size);
            label.Text = IconFontSymbols.Coin;
            label.TextColor = Colors.CoinColor.CGColor.Alpha == 0x00 ? defaultColor : Colors.CoinColor;
            label.IsAccessibilityElement = false;
            label.SizeToFit();
        }

        public static void MakeMoneyLabel(this UILabel label, UIView leftView, UIView mainView, float fontSize)
        {
            label.TextColor = Colors.PriceLabelTextColor;
            label.Font = UniversalUtils.SystemFont(fontSize);
            label.SizeToFit();
            label.SetLeft(leftView.Frame.Right + 4f);
            label.SetTop(mainView.Frame.Height / 2 - label.Frame.Height / 2);
        }

        public static void MakePresentPriceLabel(this UILabel label, UIColor defaultColor, FontType fontType = UniversalUtils.FontType.Normal)
        {
            label.TextColor = defaultColor;
            label.SetFont(UniversalUtils.FontStyle.Default, fontType);
            label.SizeToFit();
        }

        public enum FontType
        {
            Light,
            Normal,
            Medium,
            Semibold,
            Bold
        }

        public enum FontStyle
        {
            Small,
            Default,
            BigDefault,
            Subheader,
            Bigheader,
        }

        public static void SetFont(this UILabel label, FontStyle style, FontType type = FontType.Normal)
        {
            switch (style)
            {
                case FontStyle.Small:
                    label.Font = SystemFont(Dimensions.SmallFont, type);
                    break;
                case FontStyle.Default:
                    label.Font = SystemFont(Dimensions.NewDefaultFont, type);
                    break;
                case FontStyle.BigDefault:
                    label.Font = SystemFont(Dimensions.NewDefaultBigFont, type);
                    break;
                case FontStyle.Subheader:
                    label.Font = SystemFont(Dimensions.NewSubheaderFont, type);
                    break;
                case FontStyle.Bigheader:
                    label.Font = SystemFont(Dimensions.NewBigheaderFont, FontType.Bold);
                    break;
            }
        }

        public static void SetFont(this UILabel label, float size, FontType type = FontType.Normal)
        {
            label.Font = SystemFont(size, type);
        }

        public static void SetFont(this UILabel label, nfloat size, FontType type = FontType.Normal)
        {
            label.Font = SystemFont((float)size, type);
        }

        public static UIFont SystemFont(float size, FontType type = FontType.Normal)
        {
            UIFont font = null;

            UIFontTextStyle fontTextStyle = UIFontTextStyle.Body;
            switch (size)
            {
                case float n when (n <= 11f):
                    fontTextStyle = UIFontTextStyle.Caption2;
                    break;
                case 12f:
                    fontTextStyle = UIFontTextStyle.Caption1;
                    break;
                case float n when (n > 12f && n <= 15f):
                    fontTextStyle = UIFontTextStyle.Footnote;
                    break;
                case 16f:
                    fontTextStyle = UIFontTextStyle.Callout;
                    break;
                case float n when (n > 16f && n <= 20f):
                    fontTextStyle = UIFontTextStyle.Body;
                    break;
                case 21f:
                    fontTextStyle = UIFontTextStyle.Title3;
                    break;
                case float n when (n > 21f && n <= 28f):
                    fontTextStyle = UIFontTextStyle.Title1;
                    break;
                case float n when (n > 28f && n <= 36f):
                default:
                    fontTextStyle = UIFontTextStyle.LargeTitle;
                    break;
            }

            var fontWeight = UIFontWeight.Regular;
            switch (type)
            {
                case FontType.Light:
                    fontWeight = UIFontWeight.Light;
                    break;
                case FontType.Normal:
                    fontWeight = UIFontWeight.Regular;
                    break;
                case FontType.Medium:
                    fontWeight = UIFontWeight.Medium;
                    break;
                case FontType.Semibold:
                    fontWeight = UIFontWeight.Semibold;
                    break;
                case FontType.Bold:
                    fontWeight = UIFontWeight.Bold;
                    break;
            }

            font = UIFont.GetPreferredFontForTextStyle(fontTextStyle);
            return UIFont.SystemFontOfSize(size: font.PointSize, weight: fontWeight);
        }

        public static void SetSimleRenderingOption(this UILabel label, UIColor color)
        {
            label.Layer.ShouldRasterize = true;
        }

        public static void SetLongTapAction(this UIView view, Action action)
        {
            long lastTapTime = 0;

            var longTap = new UILongPressGestureRecognizer();
            longTap = new UILongPressGestureRecognizer(() =>
            {
                var now = LpDateTime.Now.DateTimeTotalMillis();
                if (now - lastTapTime > 1000)
                {
                    action();
                }
                lastTapTime = LpDateTime.Now.DateTimeTotalMillis();
            });
            longTap.NumberOfTapsRequired = 0;
            longTap.MinimumPressDuration = 0.6f;

            if (view.GestureRecognizers != null)
            {
                foreach (var gr in view.GestureRecognizers)
                    if (gr is UILongPressGestureRecognizer)
                        view.RemoveGestureRecognizer(gr);
            }

            view.AddGestureRecognizer(longTap);
        }

        public static void SetClickAction(this UIView view, Action action)
        {
            var singleFingerTap = new UITapGestureRecognizer(new Action(delegate
            {
                action.Invoke();
            }));
            view.AddGestureRecognizer(singleFingerTap);
        }

        // unusing
        public static void ApplyToggleStyle(this UISwitch toggle)
        {
            toggle.OnTintColor = Colors.AccentColor;
            toggle.ThumbTintColor = UIColor.LightGray;
        }

        public static UIView CreateWebViewCell(string webText, nfloat w, UIColor color, UIFont font, bool centerAlign = false, UIColor accentColor = null, float lineHeight = -1f)
        {
            if (string.IsNullOrWhiteSpace(webText))
                return new UIView(CGRect.Empty);

            try
            {
                if (webText.Contains("<") || webText.Contains(">"))
                {
                    //html 
                    // duration is about 1 second
                    NSError error = null;
                    webText = webText?.Replace("\n", "<br>");
                    webText = webText.Replace("</ol>", "</ol><br/>").Replace("</ul>", "</ul><br/>");

                    if (UIDevice.CurrentDevice.CheckIOSVersion(13))
                    {
                        font = ChangeFontValueForiOS13(font.Name, font.PointSize);
                    }

                    webText = PreprocessLists(webText, !Settings.IsLtrSelected);

                    var style = "<meta charset=\"UTF-8\"><style> body { font-family: '-apple-system', '" + font.Name + "'; font-size: " + font.PointSize + "px; } " +
                        "b {font-weight: 'bold'; } </style>";

                    var tmp1 = new NSAttributedStringDocumentAttributes
                    {
                        DocumentType = NSDocumentType.HTML,
                        StringEncoding = NSStringEncoding.UTF8,
                        BackgroundColor = UIColor.Clear
                    };

                    var attributedString = new NSAttributedString(style + webText, tmp1, ref error);
                    var final = new NSMutableAttributedString(attributedString);
                    final.AddAttribute(UIStringAttributeKey.ForegroundColor, color, new NSRange(0, attributedString.Length));

                    if (lineHeight > 0)
                    {
                        var style1 = new NSMutableParagraphStyle();
                        style1.MaximumLineHeight = lineHeight;
                        style1.MinimumLineHeight = lineHeight;
                        style1.ParagraphSpacingBefore = 10;
                        final.AddAttribute(UIStringAttributeKey.ParagraphStyle, style1, new NSRange(0, attributedString.Length));
                    }

                    var uiView = new UIView(new CGRect(0, 0, w, 0));
                    uiView.BackgroundColor = UIColor.Clear;

                    var tmp = new UITextView(uiView.Frame)
                    {
                        Editable = false,
                        BackgroundColor = UIColor.Clear,
                        TextContainerInset = UIEdgeInsets.Zero,
                        TextColor = color,
                        TintColor = color,
                        Font = font,
                        AttributedText = final,
                        ScrollEnabled = false
                    };
                    tmp.TextContainer.LineFragmentPadding = 0f;
                    tmp.SetWidth(uiView.Frame.Width);

                    //Caclulating bounding size coz SizeToFit() not doing a great job for some reason
                    var size = new CGSize(tmp.Frame.Width, nfloat.MaxValue);
                    var options = NSStringDrawingOptions.UsesLineFragmentOrigin | NSStringDrawingOptions.UsesFontLeading;
                    var rectangle = tmp.AttributedText.GetBoundingRect(size, options, null);
                    tmp.SetHeight(rectangle.Height);

                    if (centerAlign)
                        tmp.TextAlignment = UITextAlignment.Center;
                    else if (!Settings.IsLtrSelected)
                        tmp.TextAlignment = UITextAlignment.Right;

                    uiView.SetHeight(tmp.Frame.Height);
                    uiView.AddSubview(tmp);
                    uiView.SetWidth(tmp.Frame.Width);
                    return uiView;
                }
                else
                {
                    //simple text
                    // duration is about 2 milliseconds
                    var uiView = new UIView(new CGRect(0, 0, w, 0));
                    uiView.BackgroundColor = UIColor.Clear;

                    var tmp = new UILabel(uiView.Frame);
                    tmp.BackgroundColor = UIColor.Clear;
                    tmp.Text = webText;
                    if (lineHeight > -1)
                        tmp.SetLineHeight(lineHeight);
                    tmp.TextColor = color;
                    tmp.Lines = 0;
                    tmp.TintColor = color;
                    tmp.SetWidth(uiView.Frame.Width);
                    tmp.Font = font;
                    tmp.SizeHeightToFit();
                    if (centerAlign)
                        tmp.TextAlignment = UITextAlignment.Center;
                    else if (!Settings.IsLtrSelected)
                        tmp.TextAlignment = UITextAlignment.Right;

                    uiView.AddSubview(tmp);
                    uiView.SetHeight(tmp.Frame.Height);
                    uiView.SetWidth(tmp.Frame.Width);
                    return uiView;
                }
            }
            catch
            {
                var uiView = new UIView(new CGRect(0, 0, w, 0));
                uiView.BackgroundColor = UIColor.Clear;

                var tmp = new UILabel(uiView.Frame);
                tmp.BackgroundColor = UIColor.Clear;
                tmp.Text = webText;
                if (lineHeight > -1)
                    tmp.SetLineHeight(lineHeight);
                tmp.TextColor = color;
                tmp.Lines = 0;
                tmp.TintColor = color;
                tmp.SetWidth(uiView.Frame.Width);
                tmp.Font = font;
                tmp.SizeHeightToFit();
                if (centerAlign)
                    tmp.TextAlignment = UITextAlignment.Center;
                else if (!Settings.IsLtrSelected)
                    tmp.TextAlignment = UITextAlignment.Right;

                uiView.AddSubview(tmp);
                uiView.SetHeight(tmp.Frame.Height);
                uiView.SetWidth(tmp.Frame.Width);
                return uiView;
            }
        }
        
        // APP-5285
        private static string PreprocessLists(string content, bool isRtl)
        {
            var smallPadding = "\u00A0\u00A0\u00A0";
            var largePadding = "\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0";
            
            // Ordered list
            content = Regex.Replace(content, @"<ol>(.*?)</ol>", match =>
            {
                var index = 1;
                var listItems = Regex.Matches(match.Groups[1].Value, @"<li>(.*?)</li>")
                    .Select(m => isRtl
                        ? $"{m.Groups[1].Value}{smallPadding}.{index++}{largePadding}"
                        : $"{largePadding}{index++}.{smallPadding}{m.Groups[1].Value}")
                    .ToList();

                return string.Join("<br>", listItems);
            }, RegexOptions.Singleline);

            // Unordered list
            content = Regex.Replace(content, @"<ul>(.*?)</ul>", match =>
            {
                var listItems = Regex.Matches(match.Groups[1].Value, @"<li>(.*?)</li>")
                    .Select(m => isRtl
                        ? $"{m.Groups[1].Value}{smallPadding}•{largePadding}"
                        : $"{largePadding}•{smallPadding}{m.Groups[1].Value}")
                    .ToList();

                return string.Join("<br>", listItems);
            }, RegexOptions.Singleline);

            return content;
        }
        
        public static string GetStringFromHtmlCode(string inputString)
        {
            if (string.IsNullOrEmpty(inputString) || (!inputString.Contains("<") && !inputString.Contains(">")))
                return inputString;

            var output =
                //get rid of HTML tags
                Regex.Replace(inputString, "<[^>]*>", string.Empty);

            //get rid of multiple blank lines
            output = Regex.Replace(output, @"^\s*$\n", string.Empty, RegexOptions.Multiline)
                //and replace &nbsp symbols with regular spaces
                .Replace("&nbsp", " ");

            return output;
        }

        public static void AddedUnderlineAttributeToLabel(this UILabel label)
        {
            var underlineAttriString = new NSAttributedString(label.Text, underlineStyle: NSUnderlineStyle.Single);
            label.AttributedText = underlineAttriString;
        }

        public static void AddStrikeAttributeToLabel(this UILabel label)
        {
            var strikedAttriString = new NSAttributedString(label.Text, strikethroughStyle: NSUnderlineStyle.Single);
            label.AttributedText = strikedAttriString;
        }

        // https://jira.loyaltyplant.com/browse/APP-1968
        private static UIFont ChangeFontValueForiOS13(string family, nfloat size)
        {
            UIFont result = UIFont.SystemFontOfSize(size, UIFontWeight.Regular);

            if (family.StartsWith(".SFUI", StringComparison.InvariantCultureIgnoreCase))
            {
                var fontWeight = family.Split('-').LastOrDefault();

                if (!string.IsNullOrWhiteSpace(fontWeight) && Enum.TryParse<UIFontWeight>(fontWeight, true, out var uIFontWeight))
                {
                    result = UIFont.SystemFontOfSize(size, uIFontWeight);
                    return result;
                }
            }

            return result;
        }

        public static int Occurences(this string str, string val)
        {
            int occurrences = 0;
            int startingIndex = 0;

            while ((startingIndex = str.IndexOf(val, startingIndex)) >= 0)
            {
                ++occurrences;
                ++startingIndex;
            }

            return occurrences;
        }

        static DateTime reference = new DateTime(2001, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);

        public static DateTime NSDateToLocalDateTime(this NSDate date)
        {
            var utcDateTime = reference.AddSeconds(date.SecondsSinceReferenceDate);
            var dateTime = utcDateTime.ToLocalTime();
            return dateTime;
        }

        public static DateTime NSDateToDateTime(this NSDate date)
        {
            return reference.AddSeconds(date.SecondsSinceReferenceDate);
        }

        public static NSDate ToLocalTime(this NSDate date)
        {
            var utcDateTime = reference.AddSeconds(date.SecondsSinceReferenceDate);
            if (utcDateTime.Kind != DateTimeKind.Local)
                utcDateTime = DateTime.SpecifyKind(utcDateTime, DateTimeKind.Local);

            var newNSDate = TimeZoneInfo.ConvertTimeToUtc(utcDateTime);
            return newNSDate.DateTimeToNSDate();
        }

        public static NSDate DateTimeToNSDate(this DateTime datetime)
        {
            return NSDate.FromTimeIntervalSinceReferenceDate((datetime - reference).TotalSeconds);
        }

        public static NSDate ToNsDateLpStringFormat(this string date)
        {
            if (string.IsNullOrWhiteSpace(date))
                return NSDate.Now;

            try
            {
                var dateFormatter = new NSDateFormatter();
                dateFormatter.Locale = NSLocale.CurrentLocale;
                dateFormatter.DateStyle = NSDateFormatterStyle.Short;
                dateFormatter.TimeStyle = NSDateFormatterStyle.None;
                return dateFormatter.Parse(date);
            }
            catch
            {
                var dateFormatter = new NSDateFormatter();
                dateFormatter.DateFormat = LpDateTime.ServerDateFormat;
                return dateFormatter.Parse(date);
            }
        }

        public static NSDate ToNsTimeLpStringFormat(this string time)
        {
            if (string.IsNullOrWhiteSpace(time))
                return NSDate.Now;

            var dt = LpDateTime.TimeFromStringCurrentFormat(time);
            return dt.DateTimeToNSDate();
        }

        public static string LpStringFormat(this NSDate date)
        {
            if (date == null)
                return "";

            return LpDateTime.DateToStringCurrentFormat(date.NSDateToLocalDateTime());
        }

        public static string LpServerStringFormat(this NSDate date)
        {
            if (date == null)
                return "";

            var dateFormatter = new NSDateFormatter();
            dateFormatter.DateFormat = LpDateTime.ServerDateFormat;
            return dateFormatter.ToString(date);
        }

        public static string LpStringFormatTime(this NSDate date)
        {
            if (date == null)
                return "";

            var dateFormatter = new NSDateFormatter();
            if (Engine.Instance.Platform.TimeFormat == TimeFormat.AmPm)
                dateFormatter.DateFormat = "h:mm a";
            else
                dateFormatter.DateFormat = "HH:mm";
            return dateFormatter.ToString(date);
        }

        public static bool IsEquals(this UIColor color, UIColor scolor)
        {
            nfloat r1, g1, b1, a1, r2, g2, b2, a2;

            color.GetRGBA(out r1, out g1, out b1, out a1);
            scolor.GetRGBA(out r2, out g2, out b2, out a2);

            if (r1 == r2 && g1 == g2 && b1 == b2 && a1 == a2)
                return true;

            return false;
        }

        public static UIColor Convert(this LpColor color)
        {
            return UIColor.FromRGB(color.R, color.G, color.B);
        }

        public static void PerformHapticFeedback(HapticFeedbackType hapticFeedbackType = HapticFeedbackType.Click)
        {
            try
            {
                HapticFeedback.Perform(hapticFeedbackType);
            }
            catch (FeatureNotSupportedException ex)
            {
                // Feature not supported on device
            }
            catch (Exception ex)
            {
                // Other error has occurred.
            }
        }
    }
}

