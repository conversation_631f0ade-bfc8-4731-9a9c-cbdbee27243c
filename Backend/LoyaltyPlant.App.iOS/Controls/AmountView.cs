using System;
using LoyaltyPlant.App.iOS.Utils;
using UIKit;

namespace LoyaltyPlant.App.iOS.Controls
{
    public class AmountView : UIView
    {
        public UILabel CoinLabel { get; set; }
        public UILabel AmountLabel { get; set; }
        public nfloat Padding { get; set; }
        public nfloat TextPadding { get; set; }
        private nfloat _fontSize { get; set; }
        private bool _needToShowCoinLabel { get; set; }

        public AmountView(string text, UIColor backgroundColor, UIColor textColor, float fontSize, bool needPadding, bool needToShowCoinLabel = true)
        {
            this.BackgroundColor = backgroundColor;
            _fontSize = fontSize;
            _needToShowCoinLabel = needToShowCoinLabel;

            var amountText = new UILabel();
            amountText.Text = text;
            amountText.TextColor = textColor;
            amountText.Font = UniversalUtils.SystemFont(fontSize);
            amountText.SizeToFit();
            amountText.BackgroundColor = UIColor.Clear;

            AmountLabel = amountText;

            var coinText = new UILabel();
            coinText.MakeCoinLabel(fontSize, textColor);
            coinText.SetWidth(coinText.Frame.Width);
            coinText.BackgroundColor = UIColor.Clear;
            coinText.IsAccessibilityElement = false;

            CoinLabel = coinText;

            var textPadding = 3f;
            var padding = Dimensions.PaddingSmall;

            if (fontSize >= Dimensions.HeaderFont)
            {
                padding = Dimensions.Padding;
                textPadding = 5f;
            }

            if (!needPadding)
                padding = 0;

            Padding = padding;
            TextPadding = textPadding;

            this.SetSize(padding * 2 + (needToShowCoinLabel ? 2 * textPadding : textPadding) + amountText.Frame.Width + (needToShowCoinLabel ? coinText.Frame.Width : 0), amountText.Frame.Height + textPadding);

            amountText.SetHeight(this.Frame.Height);
            coinText.SetHeight(this.Frame.Height);

            AdjustLabelsPositionForRtl(false);

            AddSubview(amountText);

            if (needToShowCoinLabel)
                AddSubview(coinText);
        }

        public void SetPoints(int points) =>
            SetPoints(points.ToString());

        public void SetPoints(string points)
        {
            AmountLabel.Text = points;
            AmountLabel.SizeToFit();

            AmountLabel.IsAccessibilityElement = true;
            AmountLabel.AccessibilityLabel = points + " " + LocalizationExtensions.GetLocalizedText("PointsAccessibilityLabel");
            AmountLabel.AccessibilityTraits = UIAccessibilityTrait.StaticText;

            this.SetSize(Padding * 2 + TextPadding + AmountLabel.Frame.Width + CoinLabel.Frame.Width, AmountLabel.Frame.Height + TextPadding);

            AmountLabel.SetHeight(this.Frame.Height);
            CoinLabel.SetHeight(this.Frame.Height);
            AdjustLabelsPositionForRtl(false);
        }

        public void SetAmountHeightEqualToCoin()
        {
            CoinLabel.SizeToFitFromCenter();
            AmountLabel.Font = UIFont.SystemFontOfSize(_fontSize);
            AmountLabel.SizeWidthToFit();
            AdjustLabelsPositionForRtl(true);
            SizeToFit();
        }

        private void AdjustLabelsPositionForRtl(bool shouldBeCenteredHorizontallyInParentView)
        {
            var leftPadding = shouldBeCenteredHorizontallyInParentView
                ? Center.X - (AmountLabel.Frame.Size.Width + CoinLabel.Frame.Size.Width + TextPadding) / 2
                : TextPadding;
                
            if (Settings.IsLtrSelected || !_needToShowCoinLabel)
            {
                AmountLabel.SetLeft(leftPadding);
                CoinLabel.SetLeft(AmountLabel.Frame.Right + TextPadding);
            }
            else
            {
                CoinLabel.SetLeft(leftPadding);
                AmountLabel.SetLeft(CoinLabel.Frame.Right + TextPadding);
            }
        }
    }
}
