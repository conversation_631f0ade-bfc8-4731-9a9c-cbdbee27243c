using System.Collections.Generic;
using LoyaltyPlant.Cities;
using LoyaltyPlant.Core;
using LoyaltyPlant.Geofencing;
using LoyaltyPlant.Languages;
using LoyaltyPlant.Legal;
using LoyaltyPlant.Legal.Model;
using LoyaltyPlant.OpenUrl;
using LoyaltyPlant.PushNotifications;
using LoyaltyPlant.Texts;
using LoyaltyPlant.User;
using LoyaltyPlant.View;
using Xamarin.Essentials;

namespace LoyaltyPlant.Backend.UI.Factories
{
    public static class SettingsViewFactory
    {
        public static IList<LpView> GenerateMainSettings()
        {
            var fields = new List<LpView>();

            // временный костыль чтобы 4 партнера прошли ревью
            // https://loyaltyplant.slack.com/archives/CG834895Z/p1657896299775679
            if (!(DeviceInfo.Platform == DevicePlatform.iOS) &&
                (Engine.Instance.Configuration.PartnerId == 3077 ||
                Engine.Instance.Configuration.PartnerId == 2605 ||
                Engine.Instance.Configuration.PartnerId == 3164 ||
                Engine.Instance.Configuration.PartnerId == 3162))
            {
                var profileButton = new LpButton(I18N.NAVIGATION_USER_INFO,
                () => UserModule.Instance.Controller.ShowProfileScreen(),
                IconFontSymbols.UserProfile);

                fields.Add(profileButton);
            }

            if (GenerateNotificationSettings().Count > 0)
            {
                var notificationSettings = new LpButton(I18N.NOTIFICATIONS,
                                                        () => BackendModule.Instance.Controller.OnShowNotificationSettings(),
                    IconFontSymbols.Bell);

                fields.Add(notificationSettings);
            }

            var citiesModule = CitiesModule.Instance;
            if (citiesModule.Enabled)
            {
                var changeCityButton = new LpButton(I18N.CITY,
                    async () => await citiesModule.Controller.OnChangeCity(new Cities.Model.SelectCityAction() {CanGoBack = true}),
                    IconFontSymbols.City,
                    citiesModule.GetSelectedCity()?.Name);

                fields.Add(changeCityButton);
            }

            var languageModule = LanguagesModule.Instance;
            if (languageModule.Enabled)
            {
                var changeLanguageButton = new LpButton(I18N.LANGUAGE,
                    async () => await languageModule.Controller.OnChangeLanguage(new Languages.Model.SelectLanguageAction() {CanGoBack = true}),
                    IconFontSymbols.Internet,
                    languageModule.GetSelectedLanguage()?.Name);

                fields.Add(changeLanguageButton);
            }

            if (!string.IsNullOrEmpty(LegalModule.Instance.PrivacyPolicyUrl))
            {
                var legalInformation = new LpButton(I18N.PRIVACY_POLICY,
                () =>
                {
                    if (LegalModule.Instance.RequiredToAcceptEula  && !LegalModule.Instance.IsRusEula())
                    {
                        LegalModule.Instance.Controller.OnClickedToReadEula(DocumentType.PrivacyPolicy);
                    }
                    else
                    {
                        OpenUrlModule.Instance.Controller.OpenUrl(LegalModule.Instance.PrivacyPolicyUrl, true);
                    }
                }, IconFontSymbols.Blank);
                fields.Add(legalInformation);
            }

            if (!string.IsNullOrEmpty(LegalModule.Instance.TermsOfUseUrl))
            {
                var legalInformation = new LpButton(I18N.TERMS_OF_USE,
                () =>
                {
                    if (LegalModule.Instance.RequiredToAcceptEula)
                    {
                        LegalModule.Instance.Controller.OnClickedToReadEula(DocumentType.TermsOfUse);
                    }
                    else
                    {
                        OpenUrlModule.Instance.Controller.OpenUrl(LegalModule.Instance.TermsOfUseUrl, true);
                    }
                }, IconFontSymbols.Blank);
                fields.Add(legalInformation);
            }

            return fields;
        }

        public static IList<LpView> GenerateNotificationSettings()
        {
            var fields = new List<LpView>();

            if (PushNotificationsModule.Instance != null)
            {
                if (PushNotificationsModule.Instance.IsUnsubscriptionEnabled())
                {
                    var unsubscribe = new LpSwitcher(I18N.WISH_TO_SUBSCRIBE_TO_PUSH_DESCRIPTION,
                        enable => PushNotificationsModule.Instance.SetEnabled(enable),
                        PushNotificationsModule.Instance.Enabled);

                    fields.Add(unsubscribe);
                }
            }

            if (GeofencingModule.Instance != null)
            {
                if (GeofencingModule.Instance.AreGeofencesEnabled())
                {
                    var geofencingSwitcher = new LpSwitcher(I18N.GEOFENCING_SETTING,
                        enable => GeofencingModule.Instance.SetEnable(enable),
                        GeofencingModule.Instance.Enabled);

                    fields.Add(geofencingSwitcher);
                }
            }

            return fields;
        }
    }
}
