using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Backend.UI.LpViews;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Loyalty.Model.Cards;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Texts;
using LoyaltyPlant.User;
using LoyaltyPlant.View;

namespace LoyaltyPlant.DigitalOrdering
{
    public static class DigitalOrderingExtensions
    {	    
        public static List<IMainScreenElement> GetOrderElements(this DigitalOrderingModule doModule)
        {
            var list = new List<IMainScreenElement>();
            if (doModule != null)
            {
                var orders = doModule.GetIncompletedOrders();
                foreach (var order in orders)
                {
                    list.Add(new DoOrderElement(order));
                }
            }
            return list;
        }

        public static SidebarItem GetSidebarItem(this DigitalOrderingModule doModule)
        {
            if (doModule.Card != null && doModule.Controller != null)
            {
                return new SidebarItem(I18N.MY_RECENT_ORDERS, IconFontSymbols.TemporaryOrders,
                    SidebarMenuItemType.OrdersHistory, () =>
                    {
                        doModule.Controller.ShowOrdersHistory();
                    });
            }
            return null;
        }

        public static List<IMainScreenElement> GetCategoriesViews(this DoMenu doMenu, string categoryId)
        {
            try
            {
                var categories = new List<IMainScreenElement>();

                if (string.IsNullOrEmpty(categoryId))
                {
                    categories.Add(new BigHeaderElement(doMenu.Label));
                }
                else
                {
                    var category = doMenu.GetCategory(categoryId);
                    categories.Add(new BigHeaderElement(category.Label));
                }
                foreach (var category in doMenu.GetVisibleCategories(categoryId))
                {
                    if (category.Id == DoMenu.RootGiftCategoryId && LoyaltyModule.Instance.SubscriptionCards.Any())
                    {
                        var hasAnySubCategory = false;
                        var currentCategoryPosition = categories.Count;

                        //проверим подписки в подкатегориях
                        foreach (var subCategory in category.GetSubCategories())
                        {
                            if (!subCategory.IsGiftCategory)
                            {
                                hasAnySubCategory = true;
                                continue;
                            }

                            var subscriptionItems = GetSubscriptionMenuElemets(subCategory);
                            if (!subscriptionItems.IsNullOrEmpty())
                                categories.AddRange(subscriptionItems);
                            else
                                hasAnySubCategory = true;
                        }

                        //отобразим раздел Rewards в начале списка
                        if (hasAnySubCategory)
                            categories.Insert(currentCategoryPosition, new DoCategoryElement(category));
                    }
                    else if (category.IsGiftCategory && !category.IsParentCategory() && LoyaltyModule.Instance.SubscriptionCards.Any())
                    {
                        var subscriptionItems = GetSubscriptionMenuElemets(category);
                        // добавим только категорию без подписок
                        // Смотреть сюда, когда будешь искать, почему категория не отображается!
                        if (subscriptionItems.IsNullOrEmpty())
                            categories.Add(new DoCategoryElement(category));
                    }
                    else
                    {
                        if (categories.Exists(c => (c as DoCategoryElement)?.Category.Id == category.Id))
                            continue;

                        if (category.ViewType == Model.Menu.Data.CategoryData.CategoryViewType.Popup)
                            categories.Add(new DoCategoryAsItemElement(category));
                        else
                            categories.Add(new DoCategoryElement(category));
                    }
                }
                return categories;
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E("DigitalOrderingExtension.GetCategoriesViews(). Exception happend ", ex);

                DigitalOrderingModule.Instance.Controller.CreateOkMessage(I18N.DIGITAL_ORDERING_CATEGORIES_CLICK_ERROR);
                BackendModule.Instance.ShowMainScreen();

                return new List<IMainScreenElement>();
            }
        }

        public static List<IMainScreenElement> GetMenuItemsViews(this DoMenu doMenu, List<MenuItem> menuItems = null)
        {
            var viewElements = new List<IMainScreenElement>();
            var localMenuItems = (menuItems != null) ? menuItems : doMenu.MenuItems.ToList();

            foreach (var menuItem in localMenuItems)
            {
                var lp = (menuItem as GiftPosition)?.LinkedPresent;

                if (lp == null || !lp.IsSubscriptionItem)
                    viewElements.Add(new DoMenuItemElement(menuItem));
            }

            return viewElements;
        }

        public static List<IMainScreenElement> GetMenuItemsViewsByCategory(this Category category)
        {
            var viewElements = new List<IMainScreenElement>();
            var menuItems = new List<MenuItem>(category.GetMenuItemsWithCategories());

            viewElements.Add(new BigHeaderElement(category.Label));

            if (!string.IsNullOrWhiteSpace(category.Description))
                viewElements.Add(new DoCategoryDescriptionElement(category.Description));

            foreach (var menuItem in menuItems)
            {
                var lp = (menuItem as GiftPosition)?.LinkedPresent;

                if (lp == null || !lp.IsSubscriptionItem)
                    viewElements.Add(new DoMenuItemElement(menuItem));
            }

            return viewElements;
        }

        private static IList<DoMenuSubscriptionItemElement> GetSubscriptionMenuElemets(Category category)
        {
            var subscriptionElements = new List<DoMenuSubscriptionItemElement>();
            var menuItems = new List<MenuItem>(category?.GetMenuItemsWithCategories());

            if (menuItems.IsNullOrEmpty())
                return subscriptionElements;

            foreach (var item in LoyaltyModule.Instance.SubscriptionCards)
            {
                var ids = LoyaltyModule.Instance.GetPresentsIdsFromCurrentSubscriptionCard(item);

                var menuItem = menuItems.
                    FirstOrDefault(m => m.GetCategoryId() == category.Id &&
                                        ids.Exists(p => p == (m as GiftPosition).LinkedPresentId));

                if (menuItem is GiftPosition giftPosition)
                {
                    var menuItemLinkedPresentId = giftPosition.LinkedPresentId;
                    var isLinkedPresentIsNotEmpty = item.IsLinkedPresentIsNotEmpty(menuItemLinkedPresentId);

                    if (isLinkedPresentIsNotEmpty)
                        subscriptionElements.Add(new DoMenuSubscriptionItemElement(item));
                }
            }
            return subscriptionElements;
        }

        public static string GetOrderCardNumberText(this DOPaidOrder paidOrder)
        {
            if (!string.IsNullOrWhiteSpace(paidOrder.CustomTextToShowInsteadOfPosId))
            {
                var name = UserModule.Instance.Name;
                if (!string.IsNullOrWhiteSpace(name))
                    return "";
            }

            if (string.IsNullOrWhiteSpace(paidOrder.PosId))
                return "";

            if (paidOrder.IsPendingQueueNumber)
                return "";

            if (!paidOrder.IsApprovable())
            {
                return paidOrder.GetFormattedNumber();
            }
            else
            {
                if (paidOrder.WasApproved)
                    return paidOrder.GetFormattedNumber();
            }

            return "";
        }
        
        public static string GetOrderCardHintText(this DOPaidOrder paidOrder)
        {
            if (paidOrder.OrderStatus == OrderStatus.Processed)
            {
                return paidOrder.Address;
            }

            if (!string.IsNullOrWhiteSpace(paidOrder.CustomTextToShowInsteadOfPosId))
            {
                var name = UserModule.Instance.Name;
                if (!string.IsNullOrWhiteSpace(name))
                    return I18N.DO_STATUS_SAY_YOUR_NAME_TO_CASHIER + " " + name;
            }

            if (paidOrder.IsPendingQueueNumber)
                return I18N.ORDER_WILL_RECEIVE_QUEUE_NUMBER;

            if (string.IsNullOrWhiteSpace(paidOrder.PosId))
                return I18N.ORDER_NUMBER_WILL_BE_ADDED_IN_A_MINUTE;

            if (!paidOrder.IsApprovable())
            {
                if (paidOrder.OrderStatus == OrderStatus.Processed)
                {
                    return "";
                }
                else
                { 
                    var formattedTime = paidOrder.GetFormattedTimeValue();
                    switch (paidOrder.Type)
                    {
                        case DoProcessType.EatIn when !paidOrder.TableNumber.IsNullOrEmpty():
                            return DigitalOrderingTexts.TableTimeHeader(paidOrder.TableNumber) + " " + formattedTime;
                        case DoProcessType.EatIn:
                        case DoProcessType.PickUp:
                            return I18N.PICKUP_TIME_HEADER + " " + formattedTime;
                        case DoProcessType.Delivery:
                        {
                            if (PartnerModule.Instance.IsHideDeliveryTime())
                                return string.Empty;

                            return I18N.DELIVERY_TIME_HEADER + " " + formattedTime;
                        }
                        case DoProcessType.Catering:
                            return I18N.CATERING_TIME_HEADER + " " + formattedTime;
                    }
                }
                return "";
            }
            else
            {
                if (!paidOrder.WasApproved)
                    return I18N.YOUR_NOT_APPROVED_ORDER_HINT;
                else 
                    return I18N.YOUR_APPROVED_ORDER_HINT;
            }
        }
	    
        public static string GetCardCustomText(this DOPaidOrder order)
        {
            if (order.NeedToShowNameInsteadOfOrderNumber())
                return order.CustomTextToShowInsteadOfPosId + " " + order.GetTextToShowInsteadOrderNumber();

            return "";
        }
    }
}