using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.DigitalOrdering.Model;

namespace LoyaltyPlant.DigitalOrdering
{
    public class DoOrderElement : IMainScreenElement
    {
        public DOPaidOrder Order { get; }

        public DoOrderElement(DOPaidOrder order)
        {
            Order = order;
        }
        
        public MainScreenElementType ScreenCardType => MainScreenElementType.DoOrderStatusCard;
    }
}