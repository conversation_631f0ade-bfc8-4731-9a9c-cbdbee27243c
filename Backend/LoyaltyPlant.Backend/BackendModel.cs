using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Partner.Model;

namespace LoyaltyPlant.Backend
{
    [Serializable]

    // !!! Обязательно прочти перед тем, как сохранить изменения в этом классе
    // Если тут что-то поменял в этом классе, то не забудь
    // УвЕлИчИтЬ на 1 CacheVersion !!!

    public class BackendModel : IModuleModel
    {
        public int CacheVersion => 9;

        // IsFakeAuthorizationEnabled - Is activated feature Псевдоавторизация?

        public LpAuthorizationType SelectedAuthorizationType;

        public BackendModel()
        {
        }
    }
}
