using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using LoyaltyPlant.Backend.Extensions;
using LoyaltyPlant.Backend.PersistentCache;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Backend.Utils;
using LoyaltyPlant.Cities;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Actions;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Content.Model.Customization;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Analytics;
using LoyaltyPlant.Core.Auth;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Tasks;
using LoyaltyPlant.DigitalOrdering.Utils;
using LoyaltyPlant.Feedback;
using LoyaltyPlant.Hints;
using LoyaltyPlant.Images;
using LoyaltyPlant.Languages;
using LoyaltyPlant.Legal;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.Utils;
using LoyaltyPlant.PromoCode;
using LoyaltyPlant.PushNotifications;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlant.User;

namespace LoyaltyPlant.Backend
{
    [Analytics]
    public partial class BackendController : BaseModuleController<BackendModule, BackendModel, IBackendFrontend>
    {
        //Actually we should be having a sync each time a user opens the app again,
        //otherwise we'll not get some nice good UX. 
        private const int SyncCoolDownInSeconds = 10;

        private const int ANALYSIS_TIME_FROM_ICON_PUSH_TO_FIRST_SCREEN_LIMIT = 5;
        private const int ANALYSIS_ENGINE_LOADING_TIME_TOTAL_LIMIT = 3;

        private bool _isDownloadedFirstImages;
        private bool _isFirstLaunchEventSentToAnalytics;

        public bool IsMainScreenWasAlreadyShown = false;
        public bool IsTransparentToolbar { get; private set; }
        public bool IsFirstLaunchFailedMessageWasShown;
        public InitialActionModel MainScreenInitialActionModel { get; set; }
        
        public ApplicationScreenType ApplicationScreenType => Frontend.ApplicationScreenType;
        //TODO: Пре-сохранить лист со скринами. Текущее ветвление мне не нравится, но и связи с обновлениями фичей нет
        private List<ApplicationScreenType> ScreensSequence
        {
            get
            {
                var screensList = new List<ApplicationScreenType>()
                {
                    ApplicationScreenType.SelectLanguageScreen,
                    ApplicationScreenType.SelectCityScreen,
                    ApplicationScreenType.SlideshowScreen,
                    ApplicationScreenType.EulaScreen,
                    ApplicationScreenType.EnterReferralPromocodeScreen,
                    ApplicationScreenType.SocialLoginScreen
                };

                if (Engine.Instance.IsFeatureActive(UserFeature.ENTER_PERSONAL_INFO_ONLY_WITH_SIGN_IN))
                    screensList.Remove(ApplicationScreenType.SocialLoginScreen);

                return screensList;
            }
        }

        public BackendController(IBackendFrontend frontend) : base(frontend)
        {
        }

        //TODO: Fix naming. Actually, this is not "StartApp", it's more like "StartApp or proceed with initial screens."
        [SkipAnalytics]
        public async Task StartApp(SyncType syncType = SyncType.Default)
        {
            LpLogger.LOG_I("BackendController.StartApp() is called.");
            var startAppStartTime = DateTime.Now;
            try
            {
                if (!Module.IsStartedApp)
                {
                    LpLogger.LOG_I($"BackendController.StartApp(): App started. Version: {Engine.Instance.Platform.AppVersion}");
                    ShowSplashScreen();
                }
                else
                    // this means we've probably come here from ProceedApp from some of the initial screens
                    ShowLoadingIndicator(I18N.PLEASE_WAIT);

                await LoadEngineFromFilefNeeded();

                if (!Module.IsStartedApp)
                {
                    var isGstaticReachable = await Engine.Instance.Platform.IsGstaticReachable();

                    if (!isGstaticReachable)
                        CreateNoInternetMessage();

                    LpAnalytics.AppOpenAction();
                }

                Module.IsStartedApp = true;

                LpLogger.LOG_I("BackendController.StartApp(): DoTheRegistrationIfNeeded() is called (await).");
                var doTheRegistrationStartTime = DateTime.Now;

                bool registrationIsOk = await DoTheRegistrationIfNeeded();

                var doTheRegistrationExecutionTime = DateTime.Now - doTheRegistrationStartTime;
                LpLogger.LOG_I($"BackendController.StartApp(): DoTheRegistrationIfNeeded() finished. Took {(int)doTheRegistrationExecutionTime.TotalMilliseconds} msec to complete.");

                if (!registrationIsOk) return;

                //It seems that these methods will NOT block us and will be executed only when we have a fresh Model
                LpLogger.LOG_I("BackendController.StartApp(): TextsModule.UpdateTextsIfNeeded() is called (await).");
                var updateTextsStartTime = DateTime.Now;

                await TextsModule.Instance.UpdateTextsIfNeeded();

                var updateTextsExecutionTime = DateTime.Now - updateTextsStartTime;
                LpLogger.LOG_I($"BackendController.StartApp(): TextsModule.UpdateTextsIfNeeded() finished. Took {(int)updateTextsExecutionTime.TotalMilliseconds} msec to complete.");

                LpLogger.LOG_I("BackendController.StartApp(): PartnerModule.UpdatePartnerIfNeeded() is called (await).");
                var updatePartnerStartTime = DateTime.Now;

                await UpdatePartnerIfNeeded();

                var updatePartnerExecutionTime = DateTime.Now - updatePartnerStartTime;
                LpLogger.LOG_I($"BackendController.StartApp(): PartnerModule.UpdatePartnerIfNeeded() finished. Took {(int)updatePartnerExecutionTime.TotalMilliseconds} msec to complete.");

                LpLogger.LOG_I("BackendController.StartApp(): LegalModule.UpdateLegalIfNeeded() is called (await).");
                var updateLegalStartTime = DateTime.Now;

                await LegalModule.Instance.UpdateLegalIfNeeded();

                var updateLegalExecutionTime = DateTime.Now - updateLegalStartTime;
                LpLogger.LOG_I($"BackendController.StartApp(): LegalModule.UpdateLegalIfNeeded() finished. Took {(int)updateLegalExecutionTime.TotalMilliseconds} msec to complete.");

                //TODO: need to figure out why ConfigureAwait is used sometimes and sometimes not...
                //https://medium.com/@vijaynathv/should-i-use-configureawait-true-or-configureawait-false-47252e937795
                _ = PushNotificationsModule.Instance.SendAllPushReceives().ConfigureAwait(false);

                if (syncType != SyncType.None)
                {
                    LpLogger.LOG_I("BackendController.StartApp(): PerformFistSyncIfNeeded() is called (await).");
                    var performFistSyncStartTime = DateTime.Now;

                    await PerformFistSyncIfNeeded();

                    var performFistSyncExecutionTime = DateTime.Now - performFistSyncStartTime;
                    LpLogger.LOG_I($"BackendController.StartApp(): PerformFistSyncIfNeeded() finished. Took {(int)performFistSyncExecutionTime.TotalMilliseconds} msec to complete.");
                }

                SaveClientIdToPersistenceFieldsIfNeeded();

                var appLoadingTimeFromIconPushToFirstScreen = DateTime.Now - Engine.Instance.LastAppLaunchStartDateTime;
                LpLogger.LOG_I($"BackendController.StartApp(): All initial loading finished! Ready to show initial screens and then main screen! ANALYSIS_TIME_FROM_ICON_PUSH_TO_MAIN_SCREEN = {appLoadingTimeFromIconPushToFirstScreen.ToString(@"ss\:ff")} seconds");

                var isFirstLaunch = Module.PersistenceFields.LaunchCount <= 1;

                if (isFirstLaunch && appLoadingTimeFromIconPushToFirstScreen.Seconds > ANALYSIS_TIME_FROM_ICON_PUSH_TO_FIRST_SCREEN_LIMIT)
                    LpLogger.LOG_W($"BackendController.StartApp(): It was a first launch and it was too long... ANALYSIS_TIME_FROM_ICON_PUSH_TO_MAIN_SCREEN_TOO_LONG = {appLoadingTimeFromIconPushToFirstScreen.ToString(@"ss\:ff")} seconds");

                if (isFirstLaunch && !_isFirstLaunchEventSentToAnalytics)
                {
                    LpAnalytics.AppFirstLaunch();
                    _isFirstLaunchEventSentToAnalytics = true;
                }

                UpdateAuthorizationWasDoneStatus();

                var isScreenShown = false;
                foreach (var screenType in ScreensSequence)
                {
                    switch (screenType)
                    {
                        case ApplicationScreenType.SelectLanguageScreen:
                            isScreenShown = await CheckIfWeNeedToReselectLanguage();
                            Frontend.SetRtlForAppIfNeeded();
                            break;
                        case ApplicationScreenType.SelectCityScreen:
                            isScreenShown = await CheckIfWeNeedToReselectCity();
                            break;
                        case ApplicationScreenType.EnterReferralPromocodeScreen:
                            isScreenShown = CheckIfWeNeedToAskPromocode();
                            break;
                        case ApplicationScreenType.EulaScreen:
                            isScreenShown = CheckIfWeNeedToShowUglyEULA();
                            break;
                        case ApplicationScreenType.SlideshowScreen:
                            isScreenShown = ShowSlideshowScreenIfNeeded();
                            break;
                        case ApplicationScreenType.SocialLoginScreen:
                            isScreenShown = ShowSurveyOrAuthorizationScreensIfNeeded();
                            break;
                        default:
                            isScreenShown = false;
                            break;
                    }
                    if (isScreenShown)
                        return;
                }

                if (!PartnerModule.Instance.IsProcessesPreRequestShouldBeIgnored())
                {
                    //APP-4088
                    LpLogger.LOG_I("BackendController.StartApp(): DigitalOrderingModule.UpdateProcesses() is called in background.");

                    _ = DigitalOrderingModule.Instance.UpdateProcesses(false);
                }

                //TODO Эти же методы вызываются выше в PerformFistSyncIfNeeded(), возможно их стоит оттуда убрать.
                _ = DownloadFirstMainScreenImagesAsync();
                SchedulePostponedDownloadingAllImagesInBackgroundAsync();

                if (syncType != SyncType.None)
                {
                    _ = SchedulePostponedSyncRequestAndProcessErrorsAsync(syncType);
                }

                LpLogger.LOG_I("BackendController.StartApp(): All starting screens were shown! Redirecting to the Main Screen now...");

                IsMainScreenWasAlreadyShown = true;
                ShowMainScreen();
            }
            catch (Exception exception)
            {
                LpLogger.LOG_E($"BackendController.StartApp(): Exception! ", exception);
                Module.IsStartedApp = false;
            }
            finally
            {
                var startAppExecutionTime = DateTime.Now - startAppStartTime;
                LpLogger.LOG_I($"BackendController.StartApp() finished. Took {startAppExecutionTime.Seconds} sec {startAppExecutionTime.Milliseconds} msec to complete.");
                HideLoadingIndicator();
            }
        }

        private void UpdateAuthorizationWasDoneStatus()
        {
            if (Module.PersistenceFields.AuthorizationWasDone)
            {
                return;
            }

            if (PartnerModule.Instance.IsFakeAuthorizationEnabled &&
                !Module.PersistenceFields.AuthorizationWasDone)
            {
                if (!UserModule.Instance.ShouldAnswerToBioQuestions())
                {
                    Module.PersistenceFields.AuthorizationWasDone = true;
                    _ = Module.SavePersistenceFields().ConfigureAwait(false);
                }
            }
        }

        private async Task PerformFistSyncIfNeeded()
        {
            if (!Engine.Instance.IsAppHasSuccessfullyDownloadNecessaryContent || UserModule.Instance.IsCacheRecreated)
            {
                LpLogger.LOG_I($"StartApp() First sync will be executed synchronosly! (IsAppHasSuccessfullyDownloadNecessaryContent = {Engine.Instance.IsAppHasSuccessfullyDownloadNecessaryContent} OR IsCacheRecreated = {UserModule.Instance.IsCacheRecreated}");
                //For the first launch, this should not be postponed, actually... 
                await SendInitialSyncRequestAndProcessErrorsAsync();

                // https://jira.loyaltyplant.com/browse/APP-2806
                // We should prioritize the download of the first main screen images.
                LpLogger.LOG_I("StartApp() Scheduling the download of the first main screen images... DownloadFirstMainScreenImagesAsync()");
                _ = DownloadFirstMainScreenImagesAsync();

                LpLogger.LOG_I("StartApp() After first sync execution... Scheduling the download of all of the rest images... StartDownloadAllImages()");
                SchedulePostponedDownloadingAllImagesInBackgroundAsync();
            }
        }

        private async Task<bool> DoTheRegistrationIfNeeded()
        {
            if (Engine.Instance.IsRegistered())
                return true;
            
            var keychainClientId = TryGetClientIdFromKeychain();
            if (!keychainClientId.IsNullOrEmpty())
            {
                Engine.Instance.Credentials.ClientId = keychainClientId;
                SaveClientIdToPersistenceFieldsIfNeeded();
                Engine.Instance.Save();
                LpLogger.LOG_I("BackendController.DoTheRegistrationIfNeeded(): got clientId from keychain. Proceed to send register response with \"last-client-id-ok-da\" parameter.");
            }
            
            LpLogger.LOG_I("StartApp(): User was not registered yet, let us make registration first");

            LpLogger.LOG_I("BackendController.PreparePlatformIdentifiers() is called (await).");
            await Engine.Instance.Platform.PreparePlatformIdentifiers();
            LpLogger.LOG_I("BackendController.PreparePlatformIdentifiers() finished.");

            LpLogger.LOG_I("Engine.Instance.RegisterAsync() is called (await).");
            var registrationResponse = await Engine.Instance.RegisterAsync(BackendModule.Instance.PersistenceFields.ClientId);
            LpLogger.LOG_I("Engine.Instance.RegisterAsync() finished.");

            //we got an incorrect Register response... (No internet / Force update / RequestTimeout / etc...) 
            if (!registrationResponse.Ok)
            {
                if (!IsMainScreenWasAlreadyShown
                    && !registrationResponse.IsNoInternetExceptionThrown()
                    && !registrationResponse.IsWipeOrForeceUpdateException())
                {
                    LpLogger.LOG_I("Register response during app launch is incorrect. Sending logs and presenting messagebox.");
                    registrationResponse.SendAppLaunchErrorLogsIfNeeded();
                    MessagesModule.Instance.CreateAppLoadingFailedMesage();
                }
                else
                {
                    LpLogger.LOG_I("Register response is incorrect. ResolveIncorrectResponse() is called.");
                    registrationResponse.ResolveIncorrectResponse();
                    LpLogger.LOG_I("ResolveIncorrectResponse() finished.");
                }

                return false;
            }

            //we got a correct Register response... But the server rejected the registration! 
            if (!registrationResponse.Accepted)
            {
                LpLogger.LOG_E($"StartApp(): Register was not accepted: {registrationResponse.RejectedReason}");
                MessagesModule.Instance.CreateRegistrationRejectedMessage(registrationResponse.RejectedReason);
                return false;
            }

            //we got a correct Register response... And everything went well!
            if (registrationResponse.Accepted)
            {
                LpLogger.LOG_I("StartApp(): Registration went well!");
                SaveClientIdToKeychain(Engine.Instance.Credentials.ClientId);
                return true;
            }

            LpLogger.LOG_E("StartApp(): Something unexpected happened during registration!");
            return false;
        }
        
        private string TryGetClientIdFromKeychain() => Frontend.TryGetClientIdFromKeychain();
        private void SaveClientIdToKeychain(string clientId) => Frontend.SaveClientIdToKeychain(clientId);
        public void DeleteClientIdFromKeychain() => Frontend.DeleteClientIdFromKeychain();

        private async Task UpdatePartnerIfNeeded()
        {
            var gpiResponse = await PartnerModule.Instance.UpdatePartnerIfNeeded();
            if (gpiResponse != null && !gpiResponse.Ok && !IsMainScreenWasAlreadyShown
                && !gpiResponse.IsNoInternetExceptionThrown()
                && !gpiResponse.IsWipeOrForeceUpdateException())
            {
                LpLogger.LOG_I("GetPartnerInfo response during app launch is incorrect. Sending logs and presenting messagebox.");
                gpiResponse.SendAppLaunchErrorLogsIfNeeded();

                if (!gpiResponse.IsNoInternetExceptionThrown() && gpiResponse.IsLpServerNotReachable())
                    MessagesModule.Instance.CreateLpServerFailedToConnectMessage();
                else
                    MessagesModule.Instance.CreateAppLoadingFailedMesage();
            }
        }

        private static void SaveClientIdToPersistenceFieldsIfNeeded()
        {
            //TODO: for what? we just got this from the Engine? no? 
            if (!string.IsNullOrWhiteSpace(Engine.Instance.Credentials.ClientId))
            {
                BackendModule.Instance.PersistenceFields.ClientId = Engine.Instance.Credentials.ClientId;
                _ = BackendModule.Instance.SavePersistenceFields();
            }
        }

        private async Task LoadEngineFromFilefNeeded()
        {
            LpLogger.LOG_I("BackendController.LoadEngineFromFilefNeeded() is called (await).");
            var loadEngineFromFileStartTime = DateTime.Now;
            try
            {
                if (Engine.Instance.IsLoaded)
                    return;

                LpLogger.LOG_I("BackendController.LoadEngineFromFilefNeeded(): Engine is not loaded. Calling Engine.LoadAllAsync() (await)");
                var loadAllAsyncStartTime = DateTime.Now;

                await Engine.Instance.LoadAllAsync();
                var loadAllAsyncExecutionTime = DateTime.Now - loadAllAsyncStartTime;

                if (loadAllAsyncExecutionTime.TotalSeconds > ANALYSIS_ENGINE_LOADING_TIME_TOTAL_LIMIT)
                    LpLogger.LOG_W($"Engine.LoadAllAsync() completed! ANALYSIS_ENGINE_LOADING_TIME_TOTAL_TOO_LONG = {(int)loadAllAsyncExecutionTime.TotalMilliseconds} msec");
                else
                    LpLogger.LOG_I($"Engine.LoadAllAsync() completed! ANALYSIS_ENGINE_LOADING_TIME_TOTAL = {(int)loadAllAsyncExecutionTime.TotalMilliseconds} msec");

                LpAnalytics.SetUserInfo(Engine.Instance.Credentials.PublicId);

                //nothing happens in ProceedLaunchApp... ???
                Frontend.ProceedLaunchApp();
            }
            finally
            {
                var loadEngineFromFileExecutionTime = DateTime.Now - loadEngineFromFileStartTime;
                LpLogger.LOG_I($"BackendController.LoadEngineFromFilefNeeded() finished. Took {(int)loadEngineFromFileExecutionTime.TotalMilliseconds} msec to complete.");
            }
        }

        public void ShowSplashScreen()
        {
            Frontend.ShowSplashScreen();
        }

        private static bool CheckIfWeNeedToShowUglyEULA()
        {
            if (LegalModule.Instance.Controller.IsNeedToOpenEula())
            {
                LpLogger.LOG_I("StartApp(): PartnerModule.Instance.IsFakeAuthorizationEnabled = false AND EnsureOpenEula() = true. EULA will be shown!");
                return true;
            }

            return false;
        }

        private bool CheckIfWeNeedToAskPromocode()
        {
            if (MainScreenInitialActionModel != null &&
                MainScreenInitialActionModel.ActionType == InitialActionType.Promocode)
                return false;
            
            var promoCodeModule = PromoCodeModule.Instance;
            if (promoCodeModule != null && promoCodeModule.NeedToShowEnterPromocode())
            {
                LpLogger.LOG_I("StartApp(): Promocode screen will be shown!");
                promoCodeModule.Controller.ShowEnterReferralPromoCodeScreen();
                return true;
            }

            return false;
        }

        private static async Task<bool> CheckIfWeNeedToReselectCity()
        {
            if (await CitiesModule.Instance.UpdateCitiesIfNeeded())
            {
                LpLogger.LOG_I("StartApp(): Cities update was needed and was completed!");

                if (CitiesModule.Instance.IsChangeableCity())
                {
                    CitiesModule.Instance.Controller.ShowSelectCityScreen(new Cities.Model.SelectCityAction()
                    { CanGoBack = false });
                    return true;
                }

                await CitiesModule.Instance.ChangeCity(CitiesModule.Instance.LastSelectedCityId);
                await PartnerModule.Instance.UpdatePartnerAsync();
            }

            return false;
        }

        private static async Task<bool> CheckIfWeNeedToReselectLanguage()
        {
            if (await LanguagesModule.Instance.UpdateLanguagesIfNeeded())
            {
                LpLogger.LOG_I("StartApp(): Language update was needed and was completed!");

                var languages = LanguagesModule.Instance.Languages;

                if (languages.Count == 1)
                {
                    LpLogger.LOG_I("StartApp(): Got only one language, no need to show language selection screen");
                    await LanguagesModule.Instance.ChangeLanguage(languages[0].Id);
                }
                else if (languages.Count > 1)
                {
                    LanguagesModule.Instance.Controller.ShowSelectLanguageScreen(
                        new Languages.Model.SelectLanguageAction() { CanGoBack = false });
                    LpLogger.LOG_I("StartApp(): Language selection screen will be shown!");
                    return true;
                }
            }

            return false;
        }

        private bool ShowSlideshowScreenIfNeeded()
        {
            if (!Engine.Instance.IsFirstInstallFromServer)
                return false;

            if (Engine.Instance.IsFeatureActive(UserModule.SLIDE_SHOW_FEATURE))
            {
                if (!Module.PersistenceFields.WasSlideshowShown)
                {
                    LpLogger.LOG_I("StartApp(): ShowSlideshowScreenIfNeeded... Showing slide show screen");
                    OnShowSlideshowScreen();
                    return true;
                }
                else
                {
                    LpLogger.LOG_I("StartApp(): ShowSlideshowScreenIfNeeded... Slide Show was already shown");
                }
            }
            return false;
        }

        private bool ShowSurveyOrAuthorizationScreensIfNeeded()
        {
            if (UserModule.Instance.AllFieldsAreFilled())
                return false;

            LpLogger.LOG_I("StartApp(): UserModule.AllFieldsAreFilled != true. Checking if we need to show surveys or authorization screen");

            // https://confluence.loyaltyplant.com/pages/viewpage.action?pageId=17531286
            var whenShowActualState = GetWhenShowActualState();
            var hasValidateBonusCard = LoyaltyModule.Instance.HasValidatedBonusCard;

            bool isNeedToShowLoginScreen = UserModule.Instance.NeedToShowLoginScreen(hasValidateBonusCard);

            if (isNeedToShowLoginScreen && PartnerModule.Instance.IsFakeAuthorizationEnabled &&
                !Module.PersistenceFields.AuthorizationWasDone)
            {
                UserModule.Instance.SetIsNeedToLogin(true);

                LpLogger.LOG_I("StartApp(): ShouldAnswerToBioQuestions... Showing authorization screen");
                ShowAuthorizationScreen();
                return true;
            }

            if (UserModule.Instance.NeedToShowStartBioScreen(whenShowActualState) ||
                UserModule.Instance.NeedToShowStartBioScreen(hasValidateBonusCard))
            {
                if (UserModule.Instance.IsOnlyEmailRequired())
                {
                    if (UserModule.Instance.IsAllRequiredQuestionsAnswered())
                    {
                        LpLogger.LOG_I("StartApp(): NeedToShowStartBioScreen = true, only required question is email, but it was answered. Skipping");
                        return false;
                    }
                    else
                    {
                        LpLogger.LOG_I("StartApp(): NeedToShowStartBioScreen = true, only required question is email, and required question is not answered. Showing Email login screen");
                        UserModule.Instance.Controller.ShowEmailAuthScreen(false);
                    }
                }
                else
                {
                    LpLogger.LOG_I("StartApp(): NeedToShowStartBioScreen = true... showing it");
                    UserModule.Instance.Controller.ShowStartBioScreen(true);
                }
                return true;
            }

            return false;
        }

        private async Task DownloadFirstMainScreenImagesAsync()
        {
            LpLogger.LOG_I("StartApp(): initiating a NON-BLOCKING download of the first main screen images (HIGH priority)");

            // https://jira.loyaltyplant.com/browse/APP-2806
            // changed from 5000 to 1000. We should not be waiting for so long anyway
            const int timeOutWaitMainScreenImages = 1000;

            if (ImagesModule.Instance != null && !_isDownloadedFirstImages)
            {
                // take 4 images for download on splash screen
                var firstMainScreenImages = Module.GetMainScreenImages().Take(4);
                await ImagesModule.Instance.DownloadAsync(firstMainScreenImages, timeOutWaitMainScreenImages);
                _isDownloadedFirstImages = true;
            }
        }

        private void SchedulePostponedDownloadingAllImagesInBackgroundAsync()
        {
            LpLogger.LOG_I("StartApp(): initiating a NON-BLOCKING download of the first main screen images (LOW priority)");
            LpLogger.LOG_I("SchedulePostponedDownloadingAllImagesInBackgroundAsync() postponing images download for 2 sec ");

            if (ImagesModule.Instance != null)
            {
                // download all images on background
                var cardsImages = ContentModule.Instance.GetImages();
                ImagesModule.Instance.Download(cardsImages);
            }
        }

        public async Task SchedulePostponedSyncRequestAndProcessErrorsAsync(SyncType syncType = SyncType.Default)
        {
            if (syncType == SyncType.Promocode)
            {
                LpLogger.LOG_I("Promocode has been entered, waiting 3 seconds before sending Sync");
                await Task.Delay(3000);
            }

            LpLogger.LOG_I("StartApp or OnApplicationResumed: Initiating a Sync in background");

            //APP-3681
            var response = await Engine.Instance.SyncAsync(SyncType.Default | SyncType.NeedPersonalInfo);
            response.ResolveIncorrectResponse(true);
            MessagesModule.Instance?.ShowNextMessage();
        }

        private async Task SendInitialSyncRequestAndProcessErrorsAsync()
        {
            var response = await Engine.Instance.SyncAsync(SyncType.Initial);
            if (!response.Ok && !IsMainScreenWasAlreadyShown
                && !response.IsNoInternetExceptionThrown()
                && !response.IsWipeOrForeceUpdateException())
            {
                LpLogger.LOG_I("Intital Sync response during app launch is incorrect. Sending logs and presenting messagebox.");
                response.SendAppLaunchErrorLogsIfNeeded();
                MessagesModule.Instance.CreateAppLoadingFailedMesage();
            }
            else
                response.ResolveIncorrectResponse();
        }

        public void ApplySlideshow()
        {
            BackendModule.Instance.PersistenceFields.WasSlideshowShown = true;
            Engine.Instance.ProceedApp();
        }

        public void ApplyDotDigitalResults(bool? appPushes, bool? emailNotification, bool? smsNotification)
        {
            var dotDigitalModel = PartnerModule.Instance.GetDotDigitalModel();
            if (dotDigitalModel == null)
                return;

            int SmsQuestionId = dotDigitalModel.SmsOption?.QuestionId ?? 0;
            int EmailQuestionId = dotDigitalModel.EmailOption?.QuestionId ?? 0;

            if (appPushes != null && PushNotificationsModule.Instance.Model.Enabled != appPushes)
                PushNotificationsModule.Instance.SetEnabled(appPushes.Value);

            void fillQuestionAnswer(int questionId, bool questionAnswer)
            {
                var question = UserModule.Instance.Model.Fields.FirstOrDefault(field => field.Id == questionId);
                var yesNoAnswer = questionAnswer ? Answer.TrueFalse.Yes : Answer.TrueFalse.No;
                var selectedAnswer = question.Answers.Find(answer => answer.YesNoValue == yesNoAnswer);
                (question as SpinnerTrueFalseField).SetAnswer(selectedAnswer);
            };

            if (smsNotification != null)
                fillQuestionAnswer(SmsQuestionId, smsNotification.Value);

            if (emailNotification != null)
                fillQuestionAnswer(EmailQuestionId, emailNotification.Value);

            if (emailNotification != null || smsNotification != null)
                UserModule.Instance.Save();
        }

        [SkipAnalytics]
        public List<IMainScreenElement> GetMainScreenElements()
        {
            var sections = Module.GetMainScreenSections();
            var finalList = new List<IMainScreenElement>();

            //TODO-APP-3460 Added this try catch, just in case... There was an arreyIndexOutOfBouds Exception with finalList.InsertRange(indexForOrdersCardsSection, orders); when something were wrong with orders from server
            try
            {
                var feedbackIndexInsert = 1;

                var bottomSection = ContentModule.Instance.GetLinkedBottomSection();
                if (bottomSection != null)
                {
                    //we have bottom section and we will remove it from main list
                    sections.Remove(bottomSection);
                }

                var indexForOrdersCardsSection = 0;

                if (sections.Count > 0)
                {
                    var first = sections[0];
                    if (first.ParentSectionCustomization.IsOnlyImageSection() ||
                        first.ParentSectionCustomization.IsOnlyBigSlideshowSection())
                    {
                        IsTransparentToolbar = true;
                    }
                }

                foreach (var parentSection in sections)
                {
                    var parentSectionElement = new ParentSectionElement(parentSection);
                    finalList.Add(parentSectionElement);
                }

                var doModule = DigitalOrderingModule.Instance;
                if (!IsTransparentToolbar)
                {
                    finalList.Insert(0, new MainScreenImageElement());
                    //orders will be under default logo
                    indexForOrdersCardsSection = 1;
                }
                else
                {
                    if (doModule != null)
                    {
                        indexForOrdersCardsSection = doModule.Enabled
                            ? sections.FindIndex((obj) => obj.Id == doModule.Card.SectionId) + 1
                            : 1;
                    }
                }

                var orders = doModule.GetOrderElements();
                if (orders != null && orders.Any())
                {
                    finalList.InsertRange(indexForOrdersCardsSection, orders);
                    feedbackIndexInsert = indexForOrdersCardsSection + orders.Count;
                }

                //feedback message if needed            
                var currentFeedbackObject = FeedbackModule.Instance.FeedbackRemainder;
                if (FeedbackModule.Instance.ShouldShowFeedbackReminder && currentFeedbackObject != null)
                {
                    finalList.Insert(feedbackIndexInsert, new FeedbackMessageElement(
                        currentFeedbackObject.Rating,
                        currentFeedbackObject.Text));
                }
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E("GetMainScreenElements() something went wrong: Exception:", ex);
            }

            return finalList;
        }

        public void OnShowSlideshowScreen()
        {
            Frontend.ShowSlideshowScreen();
        }

        public void OnShowSettingsScreen()
        {
            Frontend.ShowSettingsScreen();
        }

        public void ShowMainScreen(Action completion = null)
        {
            Frontend.ShowMainScreen(completion);
        }

        public void ShowAuthorizationScreen()
        {
            Frontend.ShowAuthorizationScreen();
        }

        public void ShowAuthorizationScreenFromSideBar()
        {
            var logStringBuilder = new StringBuilder("ShowAuthScreenFromSideBar:").AppendLine();

            if (!UserModule.Instance.HasBioRequiredNoAnsweredQuestions())
            {
                logStringBuilder.Append("No Required not-answered questions. Opening Profile");
                UserModule.Instance.Controller.ShowProfileScreen();
            }
            else
            {
                logStringBuilder.Append("Some required questions are not answered").AppendLine();
                if (PartnerModule.Instance.IsFakeAuthorizationEnabled)
                {
                    logStringBuilder.Append("Fake AUTH available. Opening AuthScreen");
                    Frontend.ShowAuthorizationScreenFromSideBar();
                }
                else if (UserModule.Instance.IsOnlyEmailRequired())
                {
                    logStringBuilder.Append("Fake AUTH is not available. Only EmailQuestion required. Opening EmailAuthScreen");
                    UserModule.Instance.Controller.ShowEmailAuthScreen(true);
                }
                else
                {
                    logStringBuilder.Append("Not only EmailQuestion required. Fake AUTH is not available. Opening SurveyScreen");
                    UserModule.Instance.Controller.ShowStartBioScreen(false);
                }
            }
            LpLogger.LOG_I(logStringBuilder.ToString());
        }

        public void OnShowNotificationSettings()
        {
            Frontend.ShowNotificationsSettingsScreen();
        }

        public async Task OnShownScreen(ApplicationScreenType applicationScreenType)
        {
            LpLogger.LOG_I($"BackendController.OnShownScreen({applicationScreenType})");

            switch (applicationScreenType)
            {
                case ApplicationScreenType.MainScreen:
                    HideLoadingIndicator();

                    if (DigitalOrderingModule.Instance.Enabled)
                    {
                        LpLogger.LOG_I("MainScreen is shown. Sending orders request now and then updating the main screen");
                        await DigitalOrderingModule.Instance.UpdateOrders(false);
                        DigitalOrderingModule.Instance.Controller?.ClearCtaAvailableProcessesIds();
                        UpdateMainScreen();
                    }

                    /* todo android specific dialogs
                     Engine.Instance.Core.CheckDeviceAutoStartSettings();
                    Engine.Instance.Core.CheckGeofencePermission();
                    */

                    HintsModule.Instance.ShowBonusCardAndFirstPresentHints();

                    if (LegalModule.Instance.Controller.ShowIfUpdatedDocument())
                    {
                        MessagesModule.Instance.BlockMessages = true;
                    }
                    else
                    {
                        MessagesModule.Instance.BlockMessages = false;
                    }

                    MainScreenInitialActionModel?.InitialAction?.Invoke();
                    MainScreenInitialActionModel = null;

                    break;
                case ApplicationScreenType.DoCategoryScreen:
                case ApplicationScreenType.DoMenuScreen:
                    DigitalOrderingModule.Instance.Controller.OnMenuScreenOpenned();
                    break;
                case ApplicationScreenType.QrCodeOnPresentScreen:
                    HintsModule.Instance.ShowExitPresentValidationHint();
                    break;
                case ApplicationScreenType.BonusCardValidationScreen:
                    HintsModule.Instance.ShowValidateBonusCardHint();
                    break;
                case ApplicationScreenType.SocialLoginScreen:
                    OnAuthScreenOpenned();
                    break;
            }
            MessagesModule.Instance.ShowNextMessage();
        }

        private bool _onAuthScreenOpennedFlag = false;

        [SkipAnalytics]
        public void OnAuthScreenOpenned()
        {
            if (!_onAuthScreenOpennedFlag)
                ShowPopup(I18N.SOCIAL_LOGIN_HEADER_EXISTING_ACCOUNT);

            _onAuthScreenOpennedFlag = true;
        }

        public bool CheckConnectionAndShowMessageIfNeeded()
        {
            if (!Engine.Instance.Platform.NoInternetConnection)
                return true;

            CreateOkMessage(I18N.INET_REQUIRED_TO_CONTINUE);
            return false;
        }

        public void OnOpenSection(int sectionId)
        {
            var sectionAction = new ShowSectionAction
            {
                Id = sectionId,
            };

            var section = sectionAction.Section;
            if (section == null)
            {
                LpLogger.LOG_E("On clicked MoreButton on section " + sectionId + " but cant find section in model");
                return;
            }

            if (section is ParentSection parentSection && parentSection.ParentSectionCustomization != null &&
                parentSection.ParentSectionCustomization.SectionStyle == ParentSectionCustomization.SectionStyles.BigSlideshow)
            {
                var card = section.GetCards().FirstOrDefault();
                if (card != null)
                {
                    card.OnClicked();
                    return;
                }
            }

            Frontend.ShowSectionScreen(sectionAction);
        }

        public void OnClickedToLogout()
        {
            CreateOkCancelMessage(I18N.ARE_YOU_SURE_YOU_WANT_TO_LOGOUT, () => Logout());
        }

        public void Logout()
        {
            LpLogger.LOG_I("BackendController.Logout()");

            var isLogoutSuccess = Frontend.Logout(BackendModule.Instance.SelectedAuthorizationType);
            if (isLogoutSuccess)
            {
                // почистить инфу о юзере и послать на сервер пустую инфу о юзере
                UserModule.Instance.ClearBioInfoWhenLogout();

                UserModule.Instance.SetIsNeedToLogin(true);

                Module.PersistenceFields.AuthorizationWasDone = false;
                _ = Module.SavePersistenceFields();

                // почистить кэш
                Frontend.ClearCache();

                // выкинуть на правильный экран
                if (PartnerModule.Instance.IsFakeAuthorizationEnabled)
                    Frontend.ShowAuthorizationScreen();
                else if (UserModule.Instance.IsOnlyEmailRequired())
                    UserModule.Instance.Controller.ShowEmailAuthScreen(false);
                else
                    UserModule.Instance.Controller.ShowStartBioScreen(true);
            }
        }

        public void OnClickedToLoginWithEmail()
        {
            if (UserModule.Instance.IsOnlyEmailRequired())
                UserModule.Instance.Controller.ShowEmailAuthScreen(true);
            else
                UserModule.Instance.Controller.ShowStartBioScreen(false);
        }

        public void OnClickedToViewProfile()
        {
            if (!UserModule.Instance.IsAllRequiredQuestionsAnswered())
            {
                LpLogger.LOG_I("UserModule. Show BioScreen, because not all required questions answered");
                Module.PersistenceFields.AuthorizationWasDone = false;
                ShowAuthorizationScreenFromSideBar();
            }
            else if (Engine.Instance.IsFeatureActive(UserFeature.SHOW_PIS_ON_INFO_SCREEN))
                UserModule.Instance.Controller.ShowProfileScreen();
        }

        public async Task ClearCache()
        {
            LpLogger.LOG_I("BackendController.ClearCache()");

            ShowLoadingIndicator(I18N.PLEASE_WAIT);

            await Engine.Instance.DeleteAllAsync();

            Frontend.ClearCache();

            await ClearPersistenceFieldsWithoutClientId();

            HideLoadingIndicator();

            MessagesModule.Instance.CreatePleaseRestartAppMessage();

            DigitalOrderingModule.Instance.UpdateProcessesLastTime();
        }

        private async Task ClearPersistenceFieldsWithoutClientId()
        {
            LpLogger.LOG_I("BackendController.ClearPersistenceFieldsWithoutClientId()");
            var _persistenceController = new PersistenceController(Engine.Instance.CacheController);
            var persistenceFields = await _persistenceController.Load();

            persistenceFields.AuthorizationWasDone = false;
            persistenceFields.AppBuild = 0;
            persistenceFields.LaunchCount = 0;

            await _persistenceController.Save(persistenceFields);
        }

        public void OnApplicationResumed()
        {
            LpLogger.LOG_I("BackendController.OnApplicationResumed() - start");

            // useful for xiaomi and android >= 10 
            //TODO: I have an ANdroid which is >10, but this is not usually executed
            if (ApplicationScreenType == ApplicationScreenType.LoadingScreen)
            {
                LpLogger.LOG_W("Proceed start because splash screen idle (ApplicationScreenType == ApplicationScreenType.LoadingScreen)");
                _ = StartApp();

                return;
            }

            //TODO: When the app is doing a "cold load" from the filesystem, Engine.Instance.IsRegistered() sometimes returns FALSE because data is not yet loaded from file
            if (Engine.Instance.IsRegistered())
            {
                var elapsedSecondsFromLastSync = (LpDateTime.Now - Engine.Instance.LastSyncTryDateTime).TotalSeconds;
                if (elapsedSecondsFromLastSync < SyncCoolDownInSeconds)
                {
                    LpLogger.LOG_W($"Skip sync because work cooldown: elapsed - {elapsedSecondsFromLastSync} out of {SyncCoolDownInSeconds}");
                    return;
                }

                //TODO: for now, lets remove a sync here, otherwise, sync is often called simultaneously by BackendController.StartApp() and BackendController.OnApplicationResumed(). Need to fix it later.
                LpLogger.LOG_I($"OnApplicationResumed(): Engine.IsRegistered = true, initiating sync, elapsed - {elapsedSecondsFromLastSync} out of {SyncCoolDownInSeconds}");
                SchedulePostponedSyncRequestAndProcessErrorsAsync().ConfigureAwait(false);
                _ = DigitalOrderingModule.Instance.UpdateProcesses(false);
            }
            else 
            {
                if (!Engine.Instance.IsLoaded)
                    Engine.Instance.Load(false);

                if (!Engine.Instance.IsRegistered())
                    LpLogger.LOG_I("OnApplicationResumed(): Engine.IsRegistered = FALSE");
            }                
        }

        public void OnApplicationDeactivated()
        {
            // nothing
        }

        public void ShowDebugScreen()
        {
            Frontend.ShowDebugScreen();
        }

        public void CopyPublicId(string publicId)
        {
            Frontend.CopyPublicId(publicId);
        }

        public void OpenCheckConnectionSettings()
        {
            Frontend.OpenCheckConnectionSettings();
        }

        [SkipAnalytics]
        public new void UpdateMainScreen()
        {
            Frontend.UpdateMainScreen();
        }
    }
}
