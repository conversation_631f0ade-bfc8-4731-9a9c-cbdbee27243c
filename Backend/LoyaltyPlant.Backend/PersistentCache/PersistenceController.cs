using System;
using System.Collections.Generic;
using System.IO;
using System.IO.IsolatedStorage;
using System.Linq;
using System.Threading.Tasks;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Cache;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.CoreImpl.Cache;
using Newtonsoft.Json;

/*
 * 109, 110, 111 - versions have json file "JpersistenceFields" and client if in string "model_data"
 * 108 - have binary "persistenceFields" file, and xml "model_data"
 */

namespace LoyaltyPlant.Backend.PersistentCache
{
    public class PersistenceController
    {
        private const int ANALYSIS_LOADING_CLIENT_ID_FROM_FILE_TIME_LIMIT = 1;

        private readonly ICacheController _cacheController;

        private const string LegacyBuildCachePrefix = "build_";
        public const string LegacyIsSurveyShowedName = "IsSurveyShowed";

        public const string LegacyClientIdFileName = "model_data";
        public const string PersistenceFileName = "JpersistenceFields";

        public PersistenceController(ICacheController cacheController)
        {
            _cacheController = cacheController;
        }

        public async Task<bool> Save(PersistenceFields persistenceFields)
        {
            try
            {
                if (!string.IsNullOrEmpty(Engine.Instance.Credentials.ClientId))
                {
                    persistenceFields.ClientId = Engine.Instance.Credentials.ClientId;
                }
                persistenceFields.AppBuild = Engine.Instance.Platform.AppBuild;
                LpLogger.LOG_D($"Save persistence fields: {persistenceFields}");
                await SaveJson(persistenceFields);
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("Save: ", e);
                return false;
            }

            return true;
        }

        public void Delete()
        {
            _cacheController.Delete(PersistenceFileName);
        }

        public async Task<PersistenceFields> Load()
        {            
            DateTime timeBeforeLoadingPersistenceFields = DateTime.Now;
            
            LpLogger.LOG_I("PersistenceController.Load(): Loading all Persistence fields... (await LoadJson)");
            var persistenceFields = await LoadJson(PersistenceFileName) ?? new PersistenceFields();
            
            TimeSpan persistenceFieldsLoadingTime = DateTime.Now.Subtract(timeBeforeLoadingPersistenceFields);

            if (persistenceFieldsLoadingTime.TotalSeconds <= ANALYSIS_LOADING_CLIENT_ID_FROM_FILE_TIME_LIMIT)
                LpLogger.LOG_I($"PersistenceController.Load(): Loaded! ANALYSIS_LOADING_CLIENT_ID_FROM_FILE_TIME = {persistenceFieldsLoadingTime.ToString(@"ss\:ff")} seconds");
            else
                LpLogger.LOG_W($"PersistenceController.Load(): Loaded! ANALYSIS_LOADING_CLIENT_ID_FROM_FILE_TIME_TOO_LONG = {persistenceFieldsLoadingTime.ToString(@"ss\:ff")} seconds");

            if (string.IsNullOrEmpty(persistenceFields.ClientId))
            {
                LpLogger.LOG_W("PersistenceController.Load(): ClientId is null or empty, trying to load client ID fields... (LoadLastClientId())");
                persistenceFields.ClientId = await LoadLastClientId();
            }

            var builds = GetAllBuilds();
            if (builds.Count > 0)
            {
                if (persistenceFields.AppBuild == 0)
                {
                    persistenceFields.AppBuild = builds.Max();
                }
                _cacheController.DeleteAllFilesWithPrefix(LegacyBuildCachePrefix);
            }

            if (_cacheController.IsFileExists(LegacyIsSurveyShowedName))
            {
                LpLogger.LOG_I("PersistenceController.Load(): _cacheController.IsFileExists(LegacyIsSurveyShowedName ?!!");
                var surveyFile = await _cacheController.LoadAsync<string>(LegacyIsSurveyShowedName);
                persistenceFields.IsSurveyShowed = Convert.ToBoolean(surveyFile);
                _cacheController.Delete(LegacyIsSurveyShowedName);
            }

            LpLogger.LOG_D($"PersistenceController.Load(): finished! {persistenceFields}");
            return persistenceFields;
        }

        private async Task<PersistenceFields> LoadJson(string fileName)
        {
            var jsonSerializer = new JsonSerializer
            {
                TypeNameHandling = TypeNameHandling.None,
                MissingMemberHandling = MissingMemberHandling.Ignore,                
            };
            var jsonCacheController = new JsonCacheController(_cacheController.Directory, jsonSerializer);

            try
            {
                return await jsonCacheController.LoadAsync<PersistenceFields>(fileName);
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("LoadJson: ", e);
            }

            return null;
        }
        
        private async Task SaveJson(PersistenceFields persistenceFields)
        {
            var jsonSerializer = new JsonSerializer
            {
                TypeNameHandling = TypeNameHandling.All
            };
            
            var jsonCacheController = new JsonCacheController(_cacheController.Directory, jsonSerializer);
            try
            {
                await jsonCacheController.SaveAsync(PersistenceFileName, persistenceFields);
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("SaveJson: ", e);
            }
        }

        [Obsolete]
        private async Task<string> LoadLastClientId()
        {
            string content = null;

            try
            {
                content = await _cacheController.LoadAsync<string>(LegacyClientIdFileName);
                if (!string.IsNullOrWhiteSpace(content))
                {
                    LpLogger.LOG_I("Got last client id from IronMan - Kraken versions");
                    _cacheController.Delete(LegacyClientIdFileName);
                    return content;
                }
            }
            catch
            {
                // ignored
            }

            // Backport for Hercules versions:
            try
            {
                using (var fileReader = new StreamReader(IsolatedStorageFile.GetUserStoreForApplication()
                    .OpenFile(LegacyClientIdFileName, FileMode.Open, FileAccess.Read, FileShare.Read)))
                {
                    content = await fileReader.ReadToEndAsync();
                }

                if (string.IsNullOrWhiteSpace(content))
                {
                    return null;
                }

                LpLogger.LOG_I("Got last client id from Hercules version");

                if (content.StartsWith("<string xmlns"))
                {
                    content = content.Replace("<string xmlns=\"http://schemas.microsoft.com/2003/10/Serialization/\">",
                            "")
                        .Replace("</string>", "");
                }

                IsolatedStorageFile.GetUserStoreForApplication().DeleteFile(LegacyClientIdFileName);
            }
            catch (Exception)
            {
                LpLogger.LOG_D($"Cannot read client id from isolated storage {LegacyClientIdFileName}");
            }

            if (string.IsNullOrWhiteSpace(content))
                return null;

            return content.DecryptString();
        }

        [Obsolete]
        private List<int> GetAllBuilds()
        {
            var builds = new List<int>();
            var filenames = _cacheController.GetFilesWithPattern("", LegacyBuildCachePrefix + "*");
            foreach (var filename in filenames)
            {
                var name = filename.Replace(_cacheController.Directory + Path.DirectorySeparatorChar, "");
                if (!name.StartsWith(LegacyBuildCachePrefix)) 
                    continue;

                if (int.TryParse(name.Replace(LegacyBuildCachePrefix, ""), out var build))
                {
                    LpLogger.LOG_I("Prev build found: " + build);
                    builds.Add(build);
                }
            }
            return builds;
        }
    }
}