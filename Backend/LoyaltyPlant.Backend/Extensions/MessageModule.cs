using System;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Backend.Extensions
{
    public static class MessagesModuleExtension
    {
        public static void CreateCheckConnectionSettingsAtFirstLaunchMessage(this MessagesModule module)
        {
            module.CreateTwoButtonsMessage("", I18N.CHECK_WIFI_CONNECTION, I18N.SETTINGS, I18N.TRY_AGAIN,
                BackendModule.Instance.Controller.OpenCheckConnectionSettings,
                BackendModule.Instance.Restart);
        }

        public static void CreateLpServerFailedToConnectMessage(this MessagesModule module)
        {
            module.CreateTwoButtonsMessage(I18N.LP_SERVER_FAILED_FOR_FIRST_LAUNCH_WITH_LOGS_BUTTON, I18N.OK, I18N.SEND_ERROR_LOGS, null,
                        () => { _ = LpLogger.Instance.SendErrorLogsViaEmail(I18N.SEND_ERROR_LOGS_MESSAGE); });
        }

        public static void CreateInternalErrorMessage(this MessagesModule module, string errorMessage = null)
        {
            module.CreateOneButtonMessage(errorMessage ?? I18N.INTERNAL_SERVER_ERROR_TEXT, I18N.TRY_AGAIN,
                                                BackendModule.Instance.Restart);
        }

        public static void CreateNotRegisteredMessage(this MessagesModule module, string errorMessage = null)
        {
            module.CreateOneButtonMessage(errorMessage ?? I18N.NOT_REGISTERED_SERVER_ERROR, I18N.TRY_AGAIN,
                                                BackendModule.Instance.Restart);
        }

        public static void CreateBadRequestMessage(this MessagesModule module, string errorMessage = null)
        {
            module.CreateOneButtonMessage("", errorMessage ?? I18N.BAD_REQUEST_ERROR_TEXT, I18N.TRY_AGAIN,
                                                BackendModule.Instance.Restart);
        }

        public static void CreatePleaseRestartAppMessage(this MessagesModule module)
        {
            module.CreateOneButtonMessage("", I18N.PLEASE_RESTART_APP, I18N.OK, BackendModule.Instance.Restart);
        }

        public static void CreateRegistrationRejectedMessage(this MessagesModule module, string rejectReason)
        {
            var text = rejectReason ?? string.Format(I18N.FAILED_FIRST_LAUNCH, I18N.SUPPORT_EMAIL);
            module.CreateOkMessage(text, BackendModule.Instance.Controller.DeleteAllAndExitApplication);
        }

        public static void CreateSignInErrorMessage(this MessagesModule module, string message)
        {
            module.CreateOneButtonMessage("", message, I18N.TRY_AGAIN, () => BackendModule.Instance.Restart());
        }

        public static void CreateCafeWiFiInternetAtFirstLaunchMessage(this MessagesModule module)
        {
            var networkTestUrl = "http://loyaltyplant.com/internetTest?partnerId=" + Engine.Instance.Configuration.PartnerId;
            module.CreateTwoButtonsMessage("", I18N.CHECK_CAFE_INTERNET_ACCESS, I18N.CONTINUE, I18N.TRY_AGAIN,
                () => { module.Controller.OpenUrl(networkTestUrl, true); },
                BackendModule.Instance.Restart);
        }

        public static void CreateServiceUnavailableFirstLaunchMessage(this MessagesModule module)
        {
            module.CreateOneButtonMessage("", I18N.FAILED_FIRST_LAUNCH_SERVICE_UNVAILABLE,
                I18N.TRY_AGAIN, BackendModule.Instance.Restart);
        }

        public static void CreateFirstLaunchNoInternetMessage(this MessagesModule module, string text)
        {
            module.CreateTwoButtonsMessage("", text, I18N.SETTINGS, I18N.TRY_AGAIN,
                BackendModule.Instance.Controller.OpenCheckConnectionSettings,
                BackendModule.Instance.Restart);
        }

        public static void CreateGoSettingsLocationMessage(this MessagesModule module)
        {
            var applicationName = PartnerModule.Instance.ApplicationName;
            var text = string.Format(I18N.GO_SETTINGS_FOR_LOCATION, applicationName);
            module.CreateTwoButtonsMessage(text,
                I18N.SETTINGS, I18N.CANCEL,
                () => { module.Controller.OpenPermissionsSettings(); });
        }

        public static void CreateFirstLaunchTimeoutMessage(this MessagesModule module)
        {
            var text = I18N.FIRST_LAUNCH_NO_INTERNET;

            if (Engine.Instance.Platform.IsCellularDataEnabled)
            {
                module.CreateFirstLaunchNoInternetMessage(text + "\n" + I18N.FIRST_LAUNCH_NO_INTERNET_PROXY);
                return;
            }

            if (Engine.Instance.Platform.IsWiFiEnabled)
            {
                module.CreateFirstLaunchNoInternetMessage(text + "\n" + I18N.TRY_USE_MOBILE_CONNECTION);
                return;
            }

            module.CreateFirstLaunchNoInternetMessage(text + "\n" + I18N.TRY_USE_WIFI_CONNECTION);
        }

        public static void CreateHardClearMessageWithRestart(this MessagesModule module)
        {
            module.CreateOneButtonMessage("", I18N.PLEASE_RESTART_APP, I18N.OK,
                async () =>
                {
                    module.Controller.ShowLoadingIndicator(I18N.PLEASE_WAIT);
                    await Engine.Instance.DeleteAllAsync();
                    BackendModule.Instance.Restart();
                });
        }

        public static void CreateFirstLaunchFailedUpMessage(this MessagesModule module)
        {
            try
            {
                module.CreateOneButtonMessage("", I18N.FAILED_FIRST_LAUNCH_TRY_AGAIN1, I18N.TRY_AGAIN,
                    async () =>
                    {
                        module.Controller.ShowLoadingIndicator(I18N.PLEASE_WAIT);
                        await LpLogger.Instance.SendLogsInPacksUntilDeletedOrFailedAsync(true);
                        BackendModule.Instance.Restart();
                    });
                BackendModule.Instance.Controller.IsFirstLaunchFailedMessageWasShown = true;
            }
            catch (Exception e)
            {
                LpLogger.LOG_D("Cannot show start fail message", e);
            }
        }

        public static void CreateAppLoadingFailedMesage(this MessagesModule module)
        {
            module.CreateTwoButtonsMessage(I18N.LAUNCH_ERROR_TEXT, I18N.SEND_ERROR_LOGS, I18N.RESTART,
                () => { _ = LpLogger.Instance.SendErrorLogsViaEmail(I18N.SEND_ERROR_LOGS_MESSAGE); },
                BackendModule.Instance.Restart);
        }
    }
}
