using LoyaltyPlant.Backend.Tests.Utils;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.ConsoleClient;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Auth;

namespace LoyaltyPlant.Backend.Tests
{
    public abstract class BackendBaseTest
    {
        protected readonly ScreenNavigator ScreenNavigator;
        protected readonly BackendController Controller;
        protected readonly MockCacheController ModelCacheController;
        protected readonly MockWebController Mwc;
        protected MockLoggerWriter MockLpLogWriter { get; set; }

        public BackendBaseTest()
        {
            MockLpLogWriter = new MockLoggerWriter();
            Mwc = new MockWebController();
            ModelCacheController = new MockCacheController();
            Engine.Initialize(
                Mwc,
                ModelCacheController,
                new MockConfiguration(),
                new MockPlatformIos(),
                MockLpLogWriter);
            
            ScreenNavigator = new ScreenNavigator();
            var stubConsoleClient = new MockConsoleClient();
            Program.InitEngineModules(stubConsoleClient, ScreenNavigator);

            Controller = BackendModule.Instance.Controller;
        }
        
        public static void ClientIsRegisteredBefore()
        {
            var engineModel = new EngineModel
            {
                Credentials = new Credentials
                {
                    PublicId = 1414,
                    ClientId = "1414",
                },
            };
            Engine.Instance.SetModel(engineModel);
        }
    }
}