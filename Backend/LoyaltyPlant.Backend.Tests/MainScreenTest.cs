using System.Linq;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Core;
using LoyaltyPlant.Images;
using LoyaltyPlant.Images.Model;
using Moq;
using Xunit;

namespace LoyaltyPlant.Backend.Tests
{
    public class MainScreenTest : BackendBaseTest
    {
        public MainScreenTest()
        {
            Mwc.AddResponse("sync", "Responses/sync_with_child_sections.xml");
        }

        [Fact]
        public async void GetMainScreenElements_Sync_SortSections()
        {
            await Engine.Instance.SyncAsync();
            
            var screenElements = BackendModule.Instance.Controller.GetMainScreenElements();

            var parentSections = screenElements
                .Where(e => e.ScreenCardType == MainScreenElementType.SectionElement)
                .Cast<ParentSectionElement>()
                .ToList();
            
            Assert.Equal(7, parentSections.Count()); // parent sections
            Assert.Equal(8, screenElements.Count()); // with logo

            Assert.Equal(25668, parentSections[0].ParentSection.Id);
            Assert.Equal(25635, parentSections[1].ParentSection.Id);
            Assert.Equal(25630, parentSections[2].ParentSection.Id);
            Assert.Equal(25634, parentSections[3].ParentSection.Id);
        }

        [Fact]
        public async void GetMainScreenImages_FirstFour()
        {
            MockLpLogWriter.DisableExceptionByErrorLogs = true;
            await Engine.Instance.SyncAsync();

            var imageSizes = new Mock<IImageSizes>();
            var imageEngine = new Mock<IImageEngine>();
            Engine.Instance.AddModule(new ImagesModule(imageSizes.Object, imageEngine.Object));

            var screenElements = BackendModule.Instance.GetMainScreenImages()
                .Select(i => i.ImageUrl.Replace("&from=_client_9811593", "")).ToList();

            Assert.Contains("md5=11013", screenElements); // coupon 346529173
            Assert.Contains("md5=11009", screenElements); // coupon 346529171
            Assert.Contains("child_background_3", screenElements); // child section 25664
            Assert.Contains("child_background_2", screenElements); // child section 25665
        }
    }
}