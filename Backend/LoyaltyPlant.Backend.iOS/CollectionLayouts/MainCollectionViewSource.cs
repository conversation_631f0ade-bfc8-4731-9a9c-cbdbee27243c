using System;
using System.Collections.Generic;
using System.Linq;
using Foundation;
using LoyaltyPlant.Backend.iOS.Elements;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.UI;
using UIKit;

namespace LoyaltyPlant.Backend.iOS
{
    public class MainCollectionViewSource : UICollectionViewSource
	{
		public List<IMainScreenElement> cards;
        public UICollectionView CollectionView;
        public ApplicationScreenType type;

        public string BigHeaderCellIdentifier = "BigHeaderCellIdentifier";
		public string SimpleCardsCellIdentifier = "SimpleCardsCellIdentifier";

		public MainCollectionViewSource (List<IMainScreenElement> _cards, UICollectionView _collectionView, ApplicationScreenType _type)
		{
			this.type = _type;
			this.CollectionView = _collectionView;
			this.cards = _cards;

            this.CollectionView.RegisterClassForCell(typeof(BigHeaderCell), BigHeaderCellIdentifier);
            this.CollectionView.RegisterNibForCell (UINib.FromName (SimpleCardCell.Key, NSBundle.MainBundle), SimpleCardsCellIdentifier);
        }

		public void UpdateScreen (List<IMainScreenElement> _cards)
		{
			this.cards = _cards;	
			this.CollectionView.ReloadData ();

			UIView.PerformWithoutAnimation(() => 
			{ 
                this.CollectionView.ReloadItems(CollectionView.IndexPathsForVisibleItems);
			});
		}

		public override UICollectionViewCell GetCell (UICollectionView collectionView, NSIndexPath indexPath)
		{
			var card = cards[indexPath.Row];

			IMainScreenCell cell = null;

            try
            {
                switch (card.ScreenCardType)
                {
                    case MainScreenElementType.SimpleCardElement:
                        cell = (SimpleCardCell)(collectionView.DequeueReusableCell(SimpleCardsCellIdentifier, indexPath) as UICollectionViewCell);

						if (card is CardElement)
                        {
							(cell as UICollectionViewCell).IsAccessibilityElement = true;
							(cell as UICollectionViewCell).AccessibilityLabel = (card as CardElement).Card.PreviewText;
							(cell as UICollectionViewCell).AccessibilityTraits = UIAccessibilityTrait.Button;
						}

						break;

                    case MainScreenElementType.BigHeader:
                        cell = (BigHeaderCell)(collectionView.DequeueReusableCell(BigHeaderCellIdentifier, indexPath) as UICollectionViewCell);
                        break;
                }
            }
            catch (Exception e)
            {
                LpLogger.LOG_D("Cannot create card for collection view", e);
            }

			if (cell != null) 
			{
				cell.Update (card);
				(cell as UICollectionViewCell).Layer.ShouldRasterize = true;
				(cell as UICollectionViewCell).Layer.RasterizationScale = UIScreen.MainScreen.Scale;
			}
			
			return cell == null ? new UICollectionViewCell() : cell as UICollectionViewCell;
		}

		public override nint NumberOfSections(UICollectionView collectionView)
		{
			return 1;
		}

		public override nint GetItemsCount (UICollectionView collectionView, nint section)
		{
			return cards.Count ();
		}
	}
}

