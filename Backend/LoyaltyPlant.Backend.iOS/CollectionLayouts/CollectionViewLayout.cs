using System;
using System.Linq;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Utils;
using UIKit;

namespace LoyaltyPlant.Backend.iOS
{
    public class CollectionViewLayout : UICollectionViewFlowLayout
	{
        public override UICollectionViewLayoutAttributes[] LayoutAttributesForElementsInRect(CGRect rect)
        {
            var attributes = base.LayoutAttributesForElementsInRect(rect);

            // attributes[0] - это header
            // header есть не всегда. если attributes[0] с шириной во весь экран, то это header. а иначе - нифига это не header
            // изменяем только если в коллекции 1 элемент, потому что иначе он будет посередине.
            // если элементов больше 1, то все отображается норм
            if (attributes.Count() == 2)
            {
                if (attributes[0].Frame.Width > Dimensions.Width / 2)
	                attributes[1].Frame = SetCellFrameX(attributes[1].Frame);
            }

            // attributes[1] - это description, если он есть
            // + тут дополнительная проверка по ширине
            if (attributes.Count() == 3 && attributes[1].Frame.Width == Dimensions.Width)
            {
                var descriptionCellFrame = attributes[1].Frame;
                descriptionCellFrame.X = 0;
                attributes[1].Frame = descriptionCellFrame;

                attributes[2].Frame = SetCellFrameX(attributes[2].Frame);
            }

            return attributes;
        }

        private CGRect SetCellFrameX(CGRect firstCellFrame)
        {
	        firstCellFrame.X = Settings.IsLtrSelected ? 0 : Dimensions.WidthPadding - firstCellFrame.Width;
	        return firstCellFrame;
        }

        private CGRect SetSecondCellFrameX(CGRect secondCellFrame)
        {
            secondCellFrame.X = Settings.IsLtrSelected ? Dimensions.WidthPadding - secondCellFrame.Width : 0;
            return secondCellFrame;
        }

        public override nfloat MinimumLineSpacing {
			get {
				return Dimensions.Padding;
			}
			set {
				base.MinimumLineSpacing = Dimensions.Padding;
			}
		}

		public override bool ShouldInvalidateLayoutForBoundsChange(CGRect newBounds)
		{
			return true;	
		}

	}
}

