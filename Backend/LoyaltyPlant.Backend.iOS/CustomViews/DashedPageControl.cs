using System;
using System.Linq;
using System.Collections.Generic;
using LoyaltyPlant.App.iOS.Utils;
using CoreGraphics;
using UIKit;

namespace LoyaltyPlant.Backend.iOS.CustomViews
{
    public enum PageControlIndicator
    {
        SelectedPage,
        UnselectedPage
    }

    public class DashedPageControl : UIView
    {
        private const float Spacing = 8f;
        private const float Height = 6f;
        private const float Width = 2 * Height + Spacing;

        private readonly int _numberOfDashes;
        private List<UIView> _selectedIndicatorViews = new List<UIView>();
        private List<UIView> _unselectedIndicatorViews = new List<UIView>();
        private int _currentPageIndex = 0;

        public DashedPageControl(int numberOfPages) : base(CGRect.Empty)
        {
            _numberOfDashes = numberOfPages;
            SetupPageControl();
            CreateSelectedPageView();
        }

        private void SetupPageControl()
        {
            Frame = new CGRect(0, 0, (_numberOfDashes * Spacing) + (_numberOfDashes + 1) * Height, Height);
            for (int i = 0; i <= _numberOfDashes; i++)
            {
                var circleView = new UIView()
                {
                    Frame = new CGRect((i * (Spacing + Height)), 0, Height, Height),
                    BackgroundColor = Colors.BackgroundTextColor.ColorWithAlpha(0.2f)
                };

                circleView.Layer.CornerRadius = Height / 2;
                _unselectedIndicatorViews.Add(circleView);
                AddSubview(circleView);
            }
        }

        private void CreateSelectedPageView()
        {
            var selectedIndicatorView = new UIView()
            {
                Frame = new CGRect(0, 0, Width, Height),
                BackgroundColor = Colors.AccentColor
            };

            selectedIndicatorView.Layer.CornerRadius = Height / 2;
            _selectedIndicatorViews.Add(selectedIndicatorView);
            AddSubview(selectedIndicatorView);
        }

        public void UpdateSelectedPageViewPosition(int index)
        {
            if ((index >= _numberOfDashes) || (_selectedIndicatorViews.Count <= 0))
                return;
   
            _selectedIndicatorViews.First().SetLeft(index * (Spacing + Height));
            _currentPageIndex = index;
            LayoutIfNeeded();
        }

        public int GetCurrentPageIndex()
        {
            return _currentPageIndex;
        }

        public int GetNumberOfPages()
        {
            return _numberOfDashes;
        }

        public void SetIndicatorColorFor(PageControlIndicator indicator, UIColor color, float alpha = 1.0f)
        {
            List<UIView> views;

            switch (indicator)
            {
                case PageControlIndicator.SelectedPage:
                    views = _selectedIndicatorViews;
                    break;

                case PageControlIndicator.UnselectedPage:
                    views = _unselectedIndicatorViews;
                    break;

                default:
                    return;
            }
            foreach (var view in views)
            {
                view.BackgroundColor = color.ColorWithAlpha(alpha);
            }
        }
    }
}

