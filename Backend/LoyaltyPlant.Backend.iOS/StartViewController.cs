using System;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.UI;
using UIKit;

namespace LoyaltyPlant.Backend.iOS
{
    public class StartViewController : BaseViewController
    {
        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            NavigationController.NavigationBarHidden = true;
            NavigationItem.HidesBackButton = true;
            View.BackgroundColor = Colors.PrimaryColor;
            this.NavigationItem.BackBarButtonItem = new UIBarButtonItem("", UIBarButtonItemStyle.Plain, null);

            var image = new UIImageView(View.Bounds);
            image.ContentMode = UIViewContentMode.ScaleAspectFill;
            image.Hidden = true;
            View.AddSubview(image);

            LpLogger.LOG_D($"LaunchImage height = {Height}");
            try
            {
                if (UIDevice.CurrentDevice.UserInterfaceIdiom == UIUserInterfaceIdiom.Pad)
                {
                    // iPad models
                    if (Height == 1366)
                        image.Image = UIImage.FromFile("LaunchImage-900-Portrait-1366h@2x~ipad");
                    else if (Height == 1194)
                        image.Image = UIImage.FromFile("LaunchImage-700-Portrait-1194h@2x");
                    else if (Height == 1133)
                        image.Image = UIImage.FromFile("LaunchImage-700-Portrait-744w-1133h@2x~ipad");
                    else if (Height == 1112)
                        image.Image = UIImage.FromFile("LaunchImage-700-Portrait-834w-1112h@2x~ipad");
                    else
                        image.Image = UIImage.FromFile("LaunchImage-700-Portrait@2x~ipad");
                }
                else
                {
                    // iPhone models
                    if (Height <= 480)
                        image.Image = UIImage.FromFile("LaunchImage-700");
                    else if (Height == 568)
                        image.Image = UIImage.FromFile("LaunchImage-700-568h");
                    else if (Height == 667)
                        image.Image = UIImage.FromFile("LaunchImage-800-667h");
                    else if (Height == 812)
                        image.Image = UIImage.FromFile("LaunchImage-1100-Portrait-2436h@3x");
                    else
                        image.Image = UIImage.FromFile("LaunchImage-800-Portrait-736h");

                }

                image.Hidden = false;
            }
            catch(Exception ex)
            {
                LpLogger.LOG_E("StartVC UIImage.FromFile failed", ex);
            }
        }

        public override void ViewWillAppear(bool animated)
        {
            base.ViewWillAppear(animated);
            NavigationController.NavigationBarHidden = true;
        }

        protected override ApplicationScreenType GetApplicationScreenType()
        {
            return ApplicationScreenType.LoadingScreen;
        }
    }
}
