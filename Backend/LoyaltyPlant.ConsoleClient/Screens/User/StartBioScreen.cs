using System.Collections.Generic;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.User;

namespace LoyaltyPlant.ConsoleClient.Screens.User
{
    public class StartBioScreen : BaseScreen
    {
        public override ApplicationScreenType ApplicationScreenType => ApplicationScreenType.BioScreen;

        public StartBioScreen() : base("StartBioScreen")
        {
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();

            commands.Add(new Command("save", "to save all fields with random (almost :D) values",
                                     (s) =>
            { 
                var fields = UserModule.Instance.GetPersonalBioFields();

                foreach (var field in fields)
                {
                    SaveFieldWithRandomAnswer(field);
                }

                UserModule.Instance.Controller.OnSaveStartBio(fields); 
            }));

            return commands;
        }

        public override void PrintContent()
        {
            var fields = UserModule.Instance.GetPersonalBioFields();

            foreach (var field in fields)
            {
                WriteScreenContentElement(field.Title);
            }
        }
    }
}
