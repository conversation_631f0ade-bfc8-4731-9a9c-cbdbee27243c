using System.Collections.Generic;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.User;

namespace LoyaltyPlant.ConsoleClient.Screens.User
{
    public class ProfileScreen : BaseScreen
    {
        private List<IField> _fields;
        public override ApplicationScreenType ApplicationScreenType => ApplicationScreenType.UserInfoScreen;

        public ProfileScreen() : base("ProfileScreen")
        {
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();

            if (UserModule.Instance.LinkedAccounts.Count > 0)
            {
                commands.Add(new Command("accounts", "to show list of linked accounts",
                                         (s) => UserModule.Instance.Controller.OnShowLinkedAccounts()));
            }

            if (UserModule.Instance.LinkedDevices.Count > 0)
            {
                commands.Add(new Command("devices", "to show list of linked devices",
                                         (s) => UserModule.Instance.Controller.OnShowLinkedDevices()));
            }

            //todo bank cards

            commands.Add(new Command("save", "to save all fields",
                                     (s) => UserModule.Instance.Controller.OnClickedSaveButtonOnProfile(_fields)));

            return commands;
        }

        public override void PrintContent()
        {
            _fields = UserModule.Instance.GetPersonalBioFields();

            foreach (var field in _fields)
            {
                WriteScreenContentElement(field.Title);
            }
        }
    }
}
