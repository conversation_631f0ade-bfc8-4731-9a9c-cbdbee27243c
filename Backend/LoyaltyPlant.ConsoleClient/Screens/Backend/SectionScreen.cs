using System;
using System.Collections.Generic;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Backend.UI.Factories;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.View;

namespace LoyaltyPlant.ConsoleClient.Screens.Backend
{
    public class SectionScreen : BaseScreen
    {
        private readonly Section _section;
        private List<IMainScreenElement> _cards;

        public SectionScreen(Section section) : base("Section")
        {
            _section = section;
            _cards = BackendModule.Instance.GetSectionCards(_section);
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();
            
            commands.Add(new Command("card-$id", "to open specific card screen",
                (s) => 
                {
                    var id = Convert.ToInt32(s.Replace("card-", ""));
                    var card = ContentModule.Instance.GetCard(id);
                    card.OnClicked();
                }));
            
            return commands;
        }

        public override void PrintContent()
        {
            WriteScreenContentElement(_section.HeaderText);

            foreach (var element in _cards)
            {
                if (element is CardElement cardElement)
                {
                    var card = cardElement.Card;
                    WriteScreenContentElement("--- card: " + card + " - " + card.PreviewText + " (" + card.GetType().Name + ")");
                }
            }
        }
    }
}
