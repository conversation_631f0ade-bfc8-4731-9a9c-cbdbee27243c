using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Languages;

namespace LoyaltyPlant.ConsoleClient.Screens.Languages
{
    public class LanguagesScreen : BaseScreen
    {
        public override ApplicationScreenType ApplicationScreenType => ApplicationScreenType.SelectLanguageScreen;

        public LanguagesScreen() : base("LanguagesScreen")
        {
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();

            commands.Add(new Command("language-$id", "to select specific language",
                                     (s) =>
                                     {
                                        var id = Convert.ToInt32(s.Replace("language-", ""));
                                         LanguagesModule.Instance.Controller.OnSelectLanguage(id);
                                     }));

            return commands;
        }

        public override void PrintContent()
        {
            foreach (var lang in LanguagesModule.Instance.Model.Languages)
            {
                WriteScreenContentElement("Language: " + lang.Id.ToString() + " - " + lang.Name);
            }
        }
    }
}
