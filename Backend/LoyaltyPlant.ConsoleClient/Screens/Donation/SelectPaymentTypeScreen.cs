using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Donation;
using LoyaltyPlant.Donation.Model;

namespace LoyaltyPlant.ConsoleClient.Screens.Donation
{
    public class SelectPaymentTypeScreen : BaseScreen
    {
        private readonly DonationCard _card;

        public SelectPaymentTypeScreen(DonationCard card) : base("SelectPaymentTypeScreen")
        {
            _card = card;
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();

            foreach (var paymentPurpose in _card.PaymentPurposes)
            {
                commands.Add(new Command(paymentPurpose.Id.ToString(), paymentPurpose.Text,
                    (s) => DonationModule.Instance.Controller.OnSelectDonationType(paymentPurpose.Id, _card)));
            }

            return commands;
        }

        public override void PrintContent()
        {
            WriteScreenContentElement(_card.PreviewText);
        }
    }
}