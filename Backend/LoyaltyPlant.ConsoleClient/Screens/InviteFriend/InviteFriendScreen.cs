using System.Collections.Generic;
using LoyaltyPlant.Backend;
using LoyaltyPlant.InviteFriend;
using LoyaltyPlant.InviteFriend.Model;

namespace LoyaltyPlant.ConsoleClient.Screens.InviteFriend
{
    public class InviteFriendScreen : BaseScreen
    {
        private readonly InviteFriendCard _inviteFriendCard;

        public InviteFriendScreen(InviteFriendCard inviteFriendCard) : base("InviteFriendScreen")
        {
            _inviteFriendCard = inviteFriendCard;
        }

        public override List<Command> GetCommands()
        {
            var commands = new List<Command>();

            commands.Add(new Command("share", "to share wall text",
                                     (s) => InviteFriendModule.Instance.Controller.OnSharePromocode(_inviteFriendCard)));

            return commands;
        }

        public override void PrintContent()
        {
            WriteScreenContentElement(_inviteFriendCard.Header);
            WriteScreenContentElement("Your promocode: " + InviteFriendModule.Instance.Model.PersonalPromocode);

            foreach (var bonus in _inviteFriendCard.InviteFriendBonuses)
            {
                WriteScreenContentElement("- " + bonus.Points + " " + bonus.Description);
            }
        }
    }
}
