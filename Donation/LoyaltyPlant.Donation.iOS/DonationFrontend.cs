using Foundation;
using LoyaltyPlant.App.iOS;
using LoyaltyPlant.Donation.Model;
using UIKit;

namespace LoyaltyPlant.Donation.iOS
{
    public class DonationFrontend : AppFrontend, IDonationFrontend
    {
        public DonationFrontend(UIWindow window, UINavigationController navigationController) : base(window, navigationController)
        {
        }

        public void ShowDonationSelectionMessage(DonationCard card)
        {
            new NSObject().BeginInvokeOnMainThread(() => 
            {
                var view = new DonationSelectionView(card);
                view.Show(Window);
            });
        }

        public void ShowCheckoutScreen()
        {
            ShowViewController(new DonationCheckoutViewController());
        }
        
        public void ShowFailedPaymentStatusScreen(string message)
        {
            //new NSObject().BeginInvokeOnMainThread(
            //   () => LpFrontend.ShowViewController(new PaymentStatusViewController(message,
            //                                                                       () => LpBackend.Instance.Frontend.ShowMainScreen(),
            //                                                                       false)));
            //return;

            //new NSObject().BeginInvokeOnMainThread(() =>
            //{
            //    var loadingIndicator = LpFrontend.Indicator;
            //    if (loadingIndicator != null)
            //    {
            //        loadingIndicator.SetFailStatus(message);
            //    }
            //});
        }

        public void ShowSuccessPaymentStatusScreen(string message)
        {
            //new NSObject().BeginInvokeOnMainThread(
            //    () => LpFrontend.ShowViewController(new PaymentStatusViewController(message,
            //                                                                        () => LpBackend.Instance.Frontend.ShowMainScreen(),
            //                                                                        true)));
        }
    }
}
