using System;
using System.Collections.Generic;
using System.Globalization;
using System.Xml.Linq;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.Tasks;

namespace LoyaltyPlant.Donation.Model
{
    [Serializable]
    public class DonationCard : Card
    {
        public decimal PaymentPriceSuggest { get; set; }
        public decimal Commission { get; set; }
        public string PaymentPurposeText { get; set; }
        public List<DonationPurpose> PaymentPurposes { get; set; }

        public DonationCard(int id, long version, string previewText, int sectionId, long priority, string imageUrl)
            : base(id, version, previewText, sectionId, priority, imageUrl, null)
        {
        }

        public static DonationCard Create(XElement element)
        {
            var card = new DonationCard(Convert.ToInt32(element.GetAttributeValue("card-id")),
                  long.Parse(element.GetAttributeValue("version")),
                  element.GetCData("previewtext"),
                  Convert.ToInt32(element.GetAttributeValue("section-id")),
                  Convert.ToInt32(element.GetAttributeValueTag("priority")),
                  element.GetAttributeValue("image-url-part"));

            card.PaymentPriceSuggest = Convert.ToDecimal(element.GetAttributeValueTag("payment-price-suggest"), CultureInfo.InvariantCulture);
            card.PaymentPurposeText = element.GetCData("payment-purpose-text");
            card.PaymentPurposes = new List<DonationPurpose>();

            foreach (var pp in element.GetElementsList("payment-purposes", "payment-purpose"))
            {
                var text = pp.Value;
                var ppid = Convert.ToInt32(pp.GetAttributeValue("id"));
                card.PaymentPurposes.Add(new DonationPurpose(ppid, text));
            }

            if (element.Contains("commission"))
            {
                var element1 = element.GetElement("commission");
                card.Commission = Convert.ToDecimal(element1.GetAttributeValue("value"), CultureInfo.InvariantCulture);
            }

            return card;
        }

        public override string GetCardTypeString()
        {
            return "make-payment";
        }

        public override void OnClicked()
        {
            DonationModule.Instance.Controller.OnClickedCard(this);
        }
    }
}
