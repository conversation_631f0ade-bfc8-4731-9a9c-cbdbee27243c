using System;

namespace LoyaltyPlant.Cities.Model
{
    [Serializable]
    public class City : ILpCity
    {
        public int Id { get; }
        public string Name { get; }

        public City(int id, string name)
        {
            Id = id;
            Name = name;
        }

        public bool IsFeaturedDistrict => false;

	    public override string ToString()
        {
            return "City (" + Name + ", " + Id + ")";
        }
    }
}