using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using LoyaltyPlant.Cities.Model;
using LoyaltyPlant.Cities.Tasks;
using LoyaltyPlant.Core.Auth;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Partner;

namespace LoyaltyPlant.Cities
{
    public class CitiesModule : ControllableModule<CitiesModule, CitiesModel, CitiesController, ICitiesFrontend>,
        IPartnerInfoHandler, IRegisterHandler
    {
        public override bool Enabled => Model.IsFeatureEnabled;
        public int LastSelectedCityId => Model.LastSelectedCityId;
        public string TextToAskCity => Model.TextToAskCity;
        
        public bool IsOldMultiCityFeature()
        {
            if (Model.Districts.Count > 0)
                return false;
            return true;
        }
        
        public async Task<bool> ChangeCity(int id)
        {
            var response = await new LpTask<ChangeCityResponse>().ExecuteAsync(new ChangeCityRequest(id));
            if (response.Ok)
            {
                PartnerModule.Instance?.ShouldPartnerUpdate();
                Model.LastSelectedCityId = id;
                await SaveAsync();
            }
            return response.Ok;
        }

        public async Task<bool> GetCities()
        {
            var response = await new LpTask<GetCitiesResponse>().ExecuteAsync(new GetCitiesRequest());
            await SaveAsync();
            return response.Ok;
        }

        public IList<ILpCity> CitiesObjects
        {
            get
            {
                IList<ILpCity> citiesNames;
                if (IsOldMultiCityFeature())
                {
                    citiesNames = Model.Cities.Cast<ILpCity>().ToList();
                }
                else
                {
                    citiesNames = Model.Districts.Cast<ILpCity>().ToList();
                }
                return citiesNames;
            }
        }
        
        public bool IsChangeableCity()
        {
            if (!Model.IsFeatureEnabled)
                return false;
            
            if (Model.Districts.Count == 0 && Model.Cities.Count == 0)
            {
                return false;
            }

            if (Model.LastSelectedCityId > 0)
            {
                if (IsOldMultiCityFeature() &&
                    Model.Cities.Any((arg) => arg.Id == Model.LastSelectedCityId))
                {
                    return false;
                }
                else if (Model.Districts.Any((arg) => arg.Id == Model.LastSelectedCityId))
                {
                    return false;
                }
            }
            
            //auto select from one city
            if (IsOldMultiCityFeature())
            {
                if (Model.Cities.Count == 1)
                {
                    Model.LastSelectedCityId = Model.Cities[0].Id;
                    return false;
                }
            }
            else
            {
                if (Model.Districts.Count == 1)
                {
                    Model.LastSelectedCityId = Model.Districts[0].Id;
                    return false;
                }
            }

            return true;
        }
        
        public ILpCity GetSelectedCity()
        {
            var selectedCity = Model.LastSelectedCityId;
            if (IsOldMultiCityFeature())
            {
                return Model.Cities.FirstOrDefault((arg) => arg.Id == selectedCity);
            }
            return Model.Districts.FirstOrDefault((arg) => arg.Id == selectedCity);
        }
        
        public District GetDistrictByLocation(double latitude, double longitude)
        {
            foreach (var district in Model.Districts)
            {
                foreach (var districtPolygon in district.Polygons)
                {
                    if (districtPolygon.PointInPolygon(latitude, longitude))
                    {
                        return district;
                    }
                }
            }
            LpLogger.LOG_D($"GetDisctrictByLocation can't find district by latitude: {latitude}, longitude: {longitude}");
            return null;
        }

        public void ProceedWithPartnerInfoResponse(XElement element)
        {
            if (element.Contains("multi-city"))
                Model.IsFeatureEnabled = true;
        }

        public void ProceedWithRegisterResponse(XElement element)
        {
            if (element.Contains("multi-city"))
            {
                Model.IsFeatureEnabled = true;

                var multiCityElement = element.GetElement("multi-city");
                if (multiCityElement.Attribute("selected-city-id") != null)
                    Model.LastSelectedCityId = Convert.ToInt32(multiCityElement.GetAttributeValue("selected-city-id"));
                Save();
            }
        }

        public async Task<bool> UpdateCitiesIfNeeded()
        {
            if (Model.IsFeatureEnabled && (Model.LastSelectedCityId <= 0 || CitiesObjects.Count == 0))
            {
                LpLogger.LOG_D("Cities will be updated");
                var result = await GetCities();
                return result;
            }

            return false;
        }
        
        public List<ILpCity> GetCityBySubstring(string text)
        {
            var list = IsOldMultiCityFeature() ? Model.Cities.Cast<ILpCity>().ToList() : Model.Districts.Cast<ILpCity>().ToList();

            if (string.IsNullOrWhiteSpace(text))
                return list;

            text = text.Trim(' ');
            var simpleDistrictsList = new List<ILpCity>();
            var text1 = char.ToUpper(text[0]) + text.Substring(1).ToLower();
            simpleDistrictsList.AddRange(list.Where(district => district.Name.ToLower().StartsWith(text.ToLower())
                                                                || district.Name.Contains(text1)));
            return simpleDistrictsList.OrderBy(district => district.Name).ToList();
        }
    }
}
