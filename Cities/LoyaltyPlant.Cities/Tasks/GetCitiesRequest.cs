using System.Xml;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Tasks;

namespace LoyaltyPlant.Cities.Tasks
{
    public class GetCitiesRequest : BaseLpXmlRequest
    {
        public GetCitiesRequest() : base("get-cities")
        {
        }

        protected override void WriteXml(XmlWriter writer)
        {
            writer.WriteStartElement("request");
            writer.WriteAttributeString("type", Name);
            writer.WriteAttributeString("protocol-version", "9");
            writer.WriteAttributeString("client-id", Engine.Instance.Credentials.ClientId);
            writer.WriteEndElement();
        }
    }
}