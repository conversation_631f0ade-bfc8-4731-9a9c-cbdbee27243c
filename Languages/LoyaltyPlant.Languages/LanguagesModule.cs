using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;
using LoyaltyPlant.Content;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Auth;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Languages.Model;
using LoyaltyPlant.Languages.Tasks;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Languages
{
    public class LanguagesModule : ControllableModule<LanguagesModule, LanguagesModel, LanguagesController, ILanguagesFrontend>,
        IRegisterHandler, IPartnerInfoHandler
    {
        public ReadOnlyCollection<Language> Languages => Model.Languages.AsReadOnly();
        public override bool Enabled => Model.IsFeatureEnabled;
        public int LastSelectedLanguageId => Model.LastSelectedLanguageId;

        public Language GetSelectedLanguage()
        {
            var selectedLanguage = LastSelectedLanguageId;
            return Model.Languages.FirstOrDefault((arg) => arg.Id == selectedLanguage);
        }
        
        public int GetDefaultLanguageId()
        {
            var defaultLanguageId = Model.DefaultLanguageId;
            var configLanguageId = Engine.Instance.Configuration.LanguageId;
            return defaultLanguageId > 0 ? defaultLanguageId : configLanguageId;
        }

        public async Task<bool> ChangeLanguage(int id)
        {
            var response = await new LpTask<ChangeLanguagesResponse>().ExecuteAsync(new ChangeLanguageRequest(id));
            if (response.Ok)
            {
                PartnerModule.Instance?.ShouldPartnerUpdate();
                TextsModule.Instance?.ShouldTextsUpdate();
                ContentModule.Instance?.ShouldUpdateContent();
                SurveysModule.Instance?.UpdateSurveysIfNeeded(Model.LastSelectedLanguageId != id);
                Legal.LegalModule.Instance?.UpdateLegalInfo();
                Model.LastSelectedLanguageId = id;
                await SaveAsync();
            }
            return response.Ok;
        }

        public bool IsRtlSelected()
        {
            return Model.LastSelectedLanguageId == 25;
        }

        public bool IsDefaultLanguageSelected()
        {
            if (!Model.IsFeatureEnabled)
                return true;

            return Model.LastSelectedLanguageId == Model.DefaultLanguageId;
        }

        public async Task<bool> GetLanguages()
        {
            var response = await new LpTask<GetLanguagesResponse>().ExecuteAsync(new GetLanguagesRequest());
            await SaveAsync();
            return response.Ok;
        }

        public void ProceedWithRegisterResponse(XElement element)
        {
            if (element.Contains("multi-language"))
            {
                Model.IsFeatureEnabled = true;
                var multiLanguageElement = element.GetElement("multi-language");
                if (multiLanguageElement.Attribute("selected-language-id") != null)
                {
                    var selectedId = Convert.ToInt32(multiLanguageElement.GetAttributeValue("selected-language-id"));
                    Model.LastSelectedLanguageId = selectedId;
                }
                Save();
            }
        }

        public void ProceedWithPartnerInfoResponse(XElement element)
        {
            if (element.Contains("multi-language"))
                Model.IsFeatureEnabled = true;
        }

        public async Task<bool> UpdateLanguagesIfNeeded()
        {
            if (!Model.IsFeatureEnabled)
                return false;

            bool flowResult = LastSelectedLanguageId <= 0;
            if (Languages.Count == 0)
            {
                LpLogger.LOG_I("Languages will be updated");
                var result = await GetLanguages();
                flowResult &= result;
            }

            return flowResult;
        }
    }
}
