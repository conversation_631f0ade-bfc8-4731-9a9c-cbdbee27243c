using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Messages.Model.ServerMessages;

namespace LoyaltyPlant.Messages
{
    [Serializable]

    // !!! Обязательно прочти перед тем, как сохранить изменения в этом классе
    // Если тут что-то поменял в этом классе, то не забудь
    // УвЕлИчИтЬ на 1 CacheVersion !!!

    public class MessagesModel : IModuleModel
    {
        public int CacheVersion => 7;
		public List<ServerMessage> Messages { get; } = new List<ServerMessage>();

        public MessagesModel()
        {
        }
    }
}
