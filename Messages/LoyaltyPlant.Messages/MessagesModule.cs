using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Messages.Model;
using LoyaltyPlant.Messages.Model.ServerMessages;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Messages
{
    public partial class MessagesModule : ControllableModule<MessagesModule, MessagesModel, MessagesController, IMessagesFrontend>, 
        IMessageHandler, ISyncHandler
    {
        //Feature flag
        public const string REQUEST_IDFA_FROM_IPHONE_USERS = "REQUEST_IDFA_FROM_IPHONE_USERS";

        private bool _wasPoliteMessageShown;
        private List<Message> _messages = new List<Message>();
        public bool BlockMessages { get; set; }
        private ITrackingDialog _trackingDialog;

        public ReadOnlyCollection<Message> Messages
        {
            get
            {
                if (Model.Messages?.RemoveAll((obj) => obj.State == State.WasShown) > 0)
                {
                    Save();
                }

                if(Model.Messages != null)
                    _messages = _messages.Union(Model.Messages, Message.IdComparer).ToList();

                _messages.RemoveAll((obj) => obj.State == State.WasShown || obj.IsExpired());
                _messages.RemoveAll((obj) => (obj as CardMessage)?.IsExpiredCard() == true);

                _messages.Sort();

                return new ReadOnlyCollection<Message>(_messages);
            }
        }

        public MessagesModule()
        {
            
        }

        public MessagesModule(ITrackingDialog trackingDialog)
        {
            _trackingDialog = trackingDialog;
        }

        public void RequestAppTracking()
        {
            var isAllowRequestIDFA = Engine.Instance.IsFeatureActive(REQUEST_IDFA_FROM_IPHONE_USERS);
            if (isAllowRequestIDFA)
            {
                _trackingDialog.RequestAppTracking();
            }
        }

        public void AddMessage(Message message)
        {
            if (!string.IsNullOrWhiteSpace(message.Text) && _messages.Any(msg => msg.Text == message.Text))
            {
                if (!(message is CardMessage cardMessage) || _messages.Any(msg =>
                        msg is CardMessage cardMsg && cardMsg.CardId == cardMessage.CardId))
                {
                    LpLogger.LOG_W("MessagesModule: AddMessage(): " +
                                   $"Skipping showing duplicate message - '{message.Text}' is already in queue.");
                    return;
                }
            }
            
            _messages.Add(message);
            LpLogger.LOG_D($"Message add in queue: {message}, count: {_messages.Count}");
            if (message is ServerMessage serverMessage)
            {
                Model.Messages.Add(serverMessage);
            }
        }

        public Message GetMessageById(long id)
        {
            return Messages.FirstOrDefault(message => message.Id == id);
        }

        public void CreatePoliteMessage(UpdateInfo updateInfo)
        {
            if (_wasPoliteMessageShown)
                return;

            _wasPoliteMessageShown = true;

            var message = CreateTwoButtonsMessage("", I18N.POLITE_UPDATE,
                                                I18N.UPDATE, I18N.LATER,
                                                () => Controller.OpenUrl(updateInfo.UpdateUrl, true));
            message.Priority = Message.Priorities.Polite;
            message.ScreenRestriction = ApplicationScreenType.MainScreen;
        }

        public void CreateForceMessage(UpdateInfo updateInfo)
        {
            var message = CreateOneButtonMessage("", I18N.FORCE_UPDATE, I18N.UPDATE,
                () =>
                {
                    Controller.OpenUrl(updateInfo.UpdateUrl, true);
                });
            message.Priority = Message.Priorities.Force;
        }

        //HACK: Due to Android-realisation, messages would be presented on opened activity, so no need to show new messages asap.
        /// <summary>
        /// Add one-button message to queue
        /// By default, would be values to create ok-button
        /// </summary>
        /// <param name="text">Text to be shown</param>
        /// <param name="header">Header of the message</param>
        /// <param name="okButtonTitle">OK-button text</param>
        /// <param name="positiveAction">Action to be called after button click</param>
        public void CreatePostponedMessage(string text, string header, string okButtonTitle, Action positiveAction = null)
        {
            var message = new Message(text, header);
            message.SetPositiveButton(okButtonTitle, positiveAction);
            AddMessage(message);
        }
        
        public void CreateOkMessage(string text, Action positiveAction = null)
        {
            CreateOneButtonMessage(text, I18N.OK, positiveAction);
        }

        public void CreateOneButtonMessage(string text, string buttonText, Action positiveAction = null)
        {
            CreateOneButtonMessage("", text, buttonText, positiveAction);
        }

        public void CreateOkCancelMessage(string text, Action action = null, Action cancelAction = null)
        {
            CreateTwoButtonsMessage(null, text, I18N.OK, I18N.CANCEL, action, cancelAction);
        }

        public void CreateTwoButtonsMessage(string text, 
            string positiveButtonText, 
            string negativeButtonText, 
            Action positiveButtonAction = null, 
            Action negativeButtonAction = null)
        {
            CreateTwoButtonsMessage(null, text, positiveButtonText, negativeButtonText, positiveButtonAction,
                negativeButtonAction);
        }

        public Message CreateOneButtonMessage(string header, string text, string buttonText,
                                                  Action action = null)
        {
            var message = new Message(text, header);
            message.SetPositiveButton(buttonText, action);
            AddMessage(message);
            message.Show();
            return message;
        }

        public Message CreateTwoButtonsMessage(string header, string text,
                                                   string positiveText,
                                                   string negativeText,
                                                   Action positiveAction,
                                                   Action negativeAction = null)
        {
            var message = new Message(text, header);
            message.SetPositiveButton(positiveText, positiveAction);
            message.SetNegativeButton(negativeText, negativeAction);
            AddMessage(message);
            message.Show();
            return message;
        }

        public Message GetShowingMessage()
        {
            return Messages.FirstOrDefault((m) => m.IsShowing);
        }

        public Message GetMessageInQueue()
        {
            return Messages.FirstOrDefault(m => m.State != State.WasShown);
        }

        // get next message or current is showing
        public Message ShowNextMessage()
        {
            var messages = Messages;
            if (messages.Count == 0)
                return null;
            
            LpLogger.LOG_D($"Messages count: {messages.Count}");

            if (messages[0].Show())
                return messages[0];

            return GetShowingMessage();
        }

        public void MessageNotFound(Message _message)
        {
            _message?.OnHasBeenShown();
            ShowNextMessage();
        }

        public void ProceedWithSyncRequest(XmlWriter writer, SyncType syncType)
        {
            // nothing
        }

        public void ProceedWithSyncResponse(XElement rootElement)
        {
            if (rootElement.Contains("messages"))
            {
                foreach (var element in rootElement.GetElementsList("messages", "message"))
                {
                    ServerMessage message = null;
                    var messageType = element.GetAttributeValue("type");

                    var expirationTimestamp = Message.InfinityExpirationTime;
                    if (element.Attribute("expiration-timestamp") != null)
                        expirationTimestamp = Convert.ToInt64(element.GetAttributeValue("expiration-timestamp"));

                    switch (messageType)
                    {
                        case "text-plain":
                            {
                                var text = element.Value;
                                message = new TextMessage(text, expirationTimestamp);
                                break;
                            }

                        case "text-with-points":
                            {
                                var text = element.Value;
                                var points = Convert.ToInt32(element.GetAttributeValue("points"));
                                var pointsString = element.GetAttributeValue("points-string");
                                message = new PointsMessage(points, pointsString, text, expirationTimestamp);
                                break;
                            }
                        case "points-only":
                            {
                                var text = element.Value;
                                var points = Convert.ToInt32(element.GetAttributeValue("points"));
                                var pointsString = element.GetAttributeValue("points-string");
                                message = new PointsMessage(points, pointsString, text, expirationTimestamp);
                                break;
                            }

                        case "text-with-card":
                            {
                                var text = element.Value;
                                var cardId = Convert.ToInt32(element.GetAttributeValue("card-id"));
                                var cardMessage = new CardMessage(cardId, text, expirationTimestamp);
                                message = cardMessage;
                                var card = cardMessage.GetCard();
                                if (card != null)
                                {
                                    card.WasUsedInMessageWithCard = true;
                                }
                                break;
                            }

                        case "text-with-url":
                            {
                                var text = element.Value;
                                var url = element.GetAttributeValue("url");
                                var buttonText = element.Attribute("button-text") != null ? element.GetAttributeValue("button-text") : null;
                                message = new UrlMessage(url, text, buttonText, expirationTimestamp);
                                break;
                            }

                        case "text-with-phone-number":
                            {
                                var text = element.Value;
                                var phone = element.GetAttributeValue("phone");
                                message = new PhoneMessage(phone, text, expirationTimestamp);
                                break;
                            }
                    }

                    if (message != null)
                    {
                        message.ScreenRestriction = ApplicationScreenType.MainScreen;
                        AddMessage(message);
                    }
                }
                
                ShowNextMessage();
                Save();
            }
        }

        public void ShowPopup(string text)
        {
            Controller?.ShowPopupMesssage(text);
        }
    }
}
