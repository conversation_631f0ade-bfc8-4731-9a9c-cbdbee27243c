using System.Collections.Generic;
using LoyaltyPlant.Messages.ChooserMessages;
using LoyaltyPlant.Messages.Model;

namespace LoyaltyPlant.Messages.ChooserMessages
{
    public abstract class BaseChooserMessage<T> : Message where T : BaseChooserMessageItem
    {
        public List<T> Buttons { get; set; } = new List<T>();

        public BaseChooserMessage(string text, string header = null) : base(text, header)
        {
        }
    }
}
