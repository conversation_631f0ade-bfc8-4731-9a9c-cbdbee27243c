using System;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Messages.Model.ServerMessages
{
    [Serializable]
    public class CardMessage : ServerMessage
    {
        public readonly int CardId;

        public CardMessage(int cardId, string text, long expirationTimestamp)
            : base(ServerMessageTypes.TextWithCard, text, expirationTimestamp)
        {
            CardId = cardId;
        }

        public Card GetCard()
        {
            return ContentModule.Instance?.GetCard(CardId);
        }

        protected override void SetButtonsAndActions()
        {
            SetPositiveButton(I18N.MORE_INFO, () =>
            {
                var card = ContentModule.Instance?.GetCard(CardId);

                if (card == null)
                {
                    LpLogger.LOG_W("Cannot find card from message in model: " + CardId);
                    return;
                }

                card.OnClicked();
            });

            SetNegativeButton(I18N.CLOSE, null);
        }

        public bool IsExpiredCard()
        {
            var card = GetCard();
            if (card == null)
                return true;

            //TODO Интервалы отсутствуют допилить в будущем
            //доразобраться "if interval == null then return XXXX"
            //проверить что type == interval
            
            //var interval = card.ActualPeriod as PeriodInterval;
            //if (interval == null)
            //    return true;

            //var offer = interval.IsIntervalAvailableOffer();
            //var present = interval.IsIntervalAvailablePresent();

            //return !(offer || present);
            return false;
        }
    }
}