using System;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Messages.Model.ServerMessages
{
    [Serializable]
    public class TextMessage : ServerMessage
    {
        public TextMessage(string text, long expirationTimestamp)
            : base(ServerMessageTypes.TextOnly, text, expirationTimestamp)
        {
        }

        protected override void SetButtonsAndActions()
        {
            SetPositiveButton(I18N.OK, null);
        }
    }
}