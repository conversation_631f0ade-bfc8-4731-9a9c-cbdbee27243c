using System;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Messages.Model;

namespace LoyaltyPlant.Messages.Tests
{
    public class MessagesWrongFrontend : MockBaseFrontend, IMessagesFrontend
    {
        public MessagesWrongFrontend()
        {
        }

        public void OpenPermissionsSettings()
        {
            
        }

        public void OpenCheckConnectionSettings()
        {
        }

        public bool TryShowMessage(Message message)
        {
            //frontend can't show message
            return false;
        }

        public void ShowPopup(string message)
        {
            
        }

        public void DismissMessage(Message message)
        {
            
        }
    }
}
