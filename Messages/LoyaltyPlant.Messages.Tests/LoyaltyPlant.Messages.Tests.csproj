<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netcoreapp6.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="coverlet.msbuild" Version="2.8.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
<PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.1" />
    <DotNetCliToolReference Include="dotnet-xunit" Version="2.3.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Base\LoyaltyPlant.Base.Tests\LoyaltyPlant.Base.Tests.csproj" />
    <ProjectReference Include="..\..\Loyalty\LoyaltyPlant.Loyalty\LoyaltyPlant.Loyalty.csproj" />
    <ProjectReference Include="..\LoyaltyPlant.Messages\LoyaltyPlant.Messages.csproj" />
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Texts\LoyaltyPlant.Texts.csproj" />
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Core\LoyaltyPlant.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="sync_response.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="sync_response_update_message.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>