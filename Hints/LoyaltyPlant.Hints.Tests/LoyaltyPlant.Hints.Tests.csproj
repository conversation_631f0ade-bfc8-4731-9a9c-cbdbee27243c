<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netcoreapp6.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <OutputType>Exe</OutputType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="coverlet.msbuild" Version="2.8.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
<PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Base\LoyaltyPlant.Base.Tests\LoyaltyPlant.Base.Tests.csproj" />
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Core\LoyaltyPlant.Core.csproj" />
    <ProjectReference Include="..\LoyaltyPlant.Hints\LoyaltyPlant.Hints.csproj" />
  </ItemGroup>
     <ItemGroup>
        <None Update="get-partner-info.xml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
  </ItemGroup>
</Project>
