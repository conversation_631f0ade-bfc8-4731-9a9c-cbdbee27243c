<?xml version="1.0" encoding="UTF-8" ?>
<response protocol-version="9" type="get-partner-info">   
    <version value="1529323972000"/>   
    <app-name><![CDATA[Pizza Hut Spb]]></app-name>
    
    <partner id="1748" applications-market-link="https://itunes.apple.com/us/app/pizza-hut-ru/id950690880"/>   
    <country-info tech-support-phone-to-call="+78003332133" tech-support-phone-to-show="8 (800) 333-21-33" phone-prefix="+7" multiplier="1" currency-format="{0} руб.">      
        <multiplier>1</multiplier>   
    </country-info>   
    
    <hints>      
        <hint type="main-screen-bonuscard">         
            <header>
                <![CDATA[Теперь карта постоянного гостя у тебя в телефоне!]]>
            </header>         
            <text1 image="hand_qr">
                <![CDATA[Когда официант принесет счет, нажми на этот пункт, чтобы предъявить карту.]]>
            </text1>         
            <text2 image="gift_coin">
                <![CDATA[Ты получишь баллы, которые можно обменять на подарки!]]>
            </text2>      
        </hint>
        
        <hint type="main-screen-first-present">         
            <text1 image="gift">
                <![CDATA[Первый подарок ты можешь получить прямо сейчас!]]>
            </text1>      
        </hint>
        
        <hint type="present-screen-how-to-get-present">         
            <header>
                <![CDATA[Как получить подарок?]]>
            </header>         
            <text1 image="talk">
                <![CDATA[При заказе сообщи официанту, что ты хочешь получить подарок]]>
            </text1>         
            <text2 image="hand_qr">
                <![CDATA[Нажми кнопку "Получить". На экране телефона появится QR-код]]>
            </text2>         
            <text3 image="gift">
                <![CDATA[Официант считает код с экрана телефона и выдаст подарок!]]>
            </text3>      
        </hint>
        
        <hint type="sum-screen-how-to-use-bc">         
            <header>
                <![CDATA[Как пользоваться картой?]]>
            </header>         
            <text1 image="hand_figures">
                <![CDATA[Когда официант принесет счет, введите сумму заказа]]>
            </text1>         
            <text2 image="hand_qr">
                <![CDATA[На экране вашего телефона появится QR-код. Покажите его официанту]]>
            </text2>         
            <text3 image="gift_coin">
                <![CDATA[Официант считает код с экрана телефона и ты получишь баллы!]]>
            </text3>      
        </hint>
        
        <hint type="main-screen-do-not-forget-bc">         
            <text1 image="gift_coin">
                <![CDATA[Не забудь получить баллы за свой заказ!]]>
            </text1>      
        </hint>   
    </hints>   
    
    <features>     
        <feature name="show-restaurant-business-hints"/>  
    </features>
</response>