using System;
using System.Collections;
using System.Collections.Generic;
using Android.App;
using Android.Content;
using Android.Gms.Common;
using Android.Gms.Common.Apis;
using Android.Gms.Location;
using Android.Gms.Maps;
using Android.Gms.Maps.Model;
using Android.Graphics;
using Android.Graphics.Drawables;
using Android.OS;
using AndroidX.Core.Content;
using Android.Util;
using Android.Views;
using Android.Widget;
using Java.Lang.Annotation;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base.Utils;
using LoyaltyPlantApp.Droid.Customviews;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;
using Toolbar = AndroidX.AppCompat.Widget.Toolbar;

namespace LoyaltyPlantApp.Droid.Feedback
{
    // TODO androidX migration. Здесь place with potential bugs.
    public class FeedbackMapFragment : SafeDialogFragment, GoogleApiClient.IConnectionCallbacks,
        GoogleApiClient.IOnConnectionFailedListener, IOnMapReadyCallback
    {
        private GoogleApiClient _googleApiClient;

        private GoogleMap _map;
        private SupportMapFragment _mapFragment;
        private LatLngBounds.Builder _latLngBounds;
        private Hashtable _outletsToMarkerAssociations;

        private NavigationButtonsView _navigationButtons;

        public override void OnCreate(Bundle savedInstanceState)
        {
            SetStyle(StyleNoTitle, Resource.Style.LoyaltyPlantDialogTheme);
            base.OnCreate(savedInstanceState);
        }

        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            var root = inflater.Inflate(Resource.Layout.feedback_map_fragment, container, false);

            _outlets = PartnerModule.Instance.VisibleOutlets;
            
            _latLngBounds = new LatLngBounds.Builder();
            _outletsToMarkerAssociations = new Hashtable();

            _navigationButtons = root.FindViewById<NavigationButtonsView>(Resource.Id.navigation_buttons);
            _navigationButtons.Visibility = ViewStates.Gone;
            root.FindViewById<View>(Resource.Id.marker_description).Visibility = ViewStates.Gone;

            _mResolvingError = savedInstanceState != null && savedInstanceState.GetBoolean(StateResolvingError, false);

            // TODO androidX migration. Здесь place with potential bugs.
            _googleApiClient = new GoogleApiClient.Builder(Context)
                .AddConnectionCallbacks(this)
                .AddOnConnectionFailedListener(this)
                .AddApi(LocationServices.API)
                .Build();

            var options = new GoogleMapOptions();
            options.InvokeZoomControlsEnabled(true);
            options.InvokeZoomGesturesEnabled(true);

            _mapFragment = SupportMapFragment.NewInstance(options);
            var fragmentTransaction = ChildFragmentManager.BeginTransaction();
            fragmentTransaction.Add(Resource.Id.map_with_overlay, _mapFragment);
            fragmentTransaction.Commit();
            _mapFragment.GetMapAsync(this);

            Toolbar toolbar = root.FindViewById<Toolbar>(Resource.Id.feedback_toolbar);
            if (toolbar != null)
            {
                toolbar.Title = I18N.FEEDBACK_SEARCH_OUTLET_MAP_HEADER;
                toolbar.SetTitleTextColor(Context.GetColorEx(Resource.Color.PrimaryTextColor));
                var icon = ContextCompat.GetDrawable(Context, Resource.Drawable.ic_close_black_24dp);
                icon.SetColorFilterEx(Context.GetColorEx(Resource.Color.PrimaryTextColor));
                toolbar.NavigationIcon = icon;
                toolbar.NavigationClick += ((sender, args) =>
                {
                    Dialog.Dismiss();
                });
            }

            return root;
        }

        public void OnMapReady(GoogleMap googleMap)
        {
            _map = googleMap;
            _map.MyLocationEnabled = true;

            AddMarkersToMap(googleMap);

            var bounds = _latLngBounds.Build();
            var x = Math.Abs(bounds.Northeast.Longitude - bounds.Southwest.Longitude);
            var y = Math.Abs(bounds.Northeast.Latitude - bounds.Southwest.Latitude);

            var diam = Math.Sqrt(x * x + y * y);

            CameraUpdate cameraUpdate;

            if (diam > 0.004)
            {
                var ah = Activity.GetActionBarHeightPx();
                var sh = Activity.GetScreenHeightPx();
                var width = Activity.GetScreenWidthPx();
                var statusBarPadding = TypedValue.ApplyDimension(ComplexUnitType.Dip, 25, Resources.DisplayMetrics);

                var height = (int)(sh - ah - statusBarPadding);

                cameraUpdate = CameraUpdateFactory.NewLatLngBounds(bounds,
                    width,
                    height,
                    Resources.GetDimensionPixelSize(Resource.Dimension.base_margin_forty_eight));
            }
            else
            {
                var outlet = _outlets[0];
                cameraUpdate = CameraUpdateFactory.NewLatLngZoom(
                    new LatLng(outlet.Latitude, outlet.Longitude), 14);
            }

            // Move the map so that it is showing the markers we added above.
            // Zoom level chooses so that all markers are on screen + "sum_input_button_height" as padding.
            _map.AnimateCamera(cameraUpdate);

            googleMap.MarkerClick += (sender, args) =>
            {
                args.Marker.ShowInfoWindow();
                MapOnMarkerClick(args.Marker);
            };

            googleMap.MapClick += (sender, args) =>
            {
                Dialog.Window.FindViewById<View>(Resource.Id.marker_description).Visibility = ViewStates.Gone;
                _navigationButtons.Visibility = ViewStates.Gone;
            };
        }

        private void AddMarkersToMap(GoogleMap googleMap)
        {
            var bmp = BitmapFactory.DecodeResource(Resources, Resource.Drawable.outlet_marker);
            foreach (var outlet in _outlets)
            {
                var icon = BitmapDescriptorFactory.FromBitmap(bmp);//Resource(Resource.Drawable.outlet_marker);
                var mapOption = new MarkerOptions()
                    .SetPosition(new LatLng(outlet.Latitude, outlet.Longitude))
                    .SetIcon(icon)
                    .SetTitle(outlet.Name);
                var marker = googleMap.AddMarker(mapOption);
                _outletsToMarkerAssociations.Add(marker.Id, outlet);
                _latLngBounds.Include(new LatLng(outlet.Latitude, outlet.Longitude));
            }
            bmp.Recycle();
        }

        private void MapOnMarkerClick(Marker marker)
        {
            //var projection = _map.Projection;
            //_clickedMarkerPoint = projection.ToScreenLocation(marker.Position);
            var outlet = (Outlet)_outletsToMarkerAssociations[marker.Id];

            _navigationButtons.Visibility = ViewStates.Visible;

            var description = Dialog.Window.FindViewById<View>(Resource.Id.marker_description);
            description.Visibility = ViewStates.Visible;

            _navigationButtons.SetButtons(I18N.CANCEL,
                (sender, args) =>
                {
                    Dialog.Window.FindViewById<View>(Resource.Id.marker_description).Visibility = ViewStates.Gone;
                    _navigationButtons.Visibility = ViewStates.Gone;
                    marker.HideInfoWindow();
                }
                , I18N.SELECT, (sender, args) =>
                {
                    // TODO androidX migration. Здесь place with potential bugs.
                    if (TargetFragment != null)
                    {
                        var intent = new Intent();
                        intent.PutExtra(FeedbackMainFragment.SelectedOutletIdKey, outlet.Id);
                        TargetFragment.OnActivityResult(TargetRequestCode, (int) Result.Ok, intent);
                    }
                    Dismiss();
                });

            Dialog.Window.FindViewById<TextView>(Resource.Id.outlet_name).Text = outlet.Name;
            Dialog.Window.FindViewById<TextView>(Resource.Id.outlets_address_text).Text = outlet.Address;
        }

        public override void OnStart()
        {
            base.OnStart();

            if (Dialog != null)
            {
                Dialog.Window.SetLayout(ViewGroup.LayoutParams.MatchParent, ViewGroup.LayoutParams.MatchParent);
                Dialog.Window.SetBackgroundDrawable(new ColorDrawable(Color.Transparent));
            }

            _googleApiClient.Connect();
        }

        public override void OnStop()
        {
            base.OnStop();
            _googleApiClient.Disconnect();
        }

        //Everything below is about handling cases when something is wrong with Google Play Services.
        //According to https://developers.google.com/android/guides/api-client#Starting

        // Request code to use when launching the resolution activity
        private const int RequestResolveError = 1001;
        // Unique tag for the error dialog fragment
        private const string DialogError = "dialog_error";
        // Bool to track whether the app is already resolving an error
        private bool _mResolvingError = false;
        private List<Outlet> _outlets;
        private const string StateResolvingError = "STATE_RESOLVING_ERROR";

        public void OnConnected(Bundle connectionHint)
        {
            //everything is ok
        }

        public void OnConnectionSuspended(int cause)
        {
        }

        public void OnConnectionFailed(ConnectionResult result)
        {
            if (_mResolvingError)
            {
                // Already attempting to resolve an error.
                return;
            }

            if (result.HasResolution)
            {
                try
                {
                    _mResolvingError = true;
                    result.StartResolutionForResult(Context as Activity, RequestResolveError);
                }
                catch (IntentSender.SendIntentException e)
                {
                    // There was an error with the resolution intent. Try again.
                    _googleApiClient.Connect();
                }
            }
            else
            {
                // Show dialog using GoogleApiAvailability.getErrorDialog()
                ShowErrorDialog(result.ErrorCode);
                _mResolvingError = true;
            }
        }

        // The rest of this code is all about building the error dialog

        // Creates a dialog for an error message 
        private void ShowErrorDialog(int errorCode)
        {
            // Create a fragment for the error dialog
            var dialogFragment = new SupportErrorDialogFragment();
            // Pass the error that should be displayed
            var args = new Bundle();
            args.PutInt(DialogError, errorCode);
            dialogFragment.Arguments = args;
            dialogFragment.Show(Activity.SupportFragmentManager, "errordialog");
        }

        // Called from ErrorDialogFragment when the dialog is dismissed. 
        public void OnDialogDismissed()
        {
            _mResolvingError = false;
        }
    }
}