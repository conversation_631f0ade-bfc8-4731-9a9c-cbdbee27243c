using Android.App;
using Android.Content;
using Android.OS;
using AndroidX.AppCompat.App;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Messages.ChooserMessages;
using LoyaltyPlantApp.Droid.Base.Screencontroller;
using LoyaltyPlantApp.Droid.Base.Utils;
using LoyaltyPlantApp.Droid.Customviews;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;
using AlertDialog = AndroidX.AppCompat.App.AlertDialog;
using LoyaltyPlant.Texts;

namespace LoyaltyPlantApp.Droid.Messages
{
    public class AccentButtonMessageFragment : SafeDialogFragment, IMessageDialogFragment
    {
        private View _rootLayout;
        private AccentButtonMessage _message;
        private const string MessageId = "MessageId";

        public static AccentButtonMessageFragment NewInstance(long messageId)
        {
            var frag = new AccentButtonMessageFragment();
            var args = new Bundle();
            args.PutLong(MessageId, messageId);
            frag.Arguments = args;
            return frag;
        }

        public override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            var id = Arguments.GetLong(MessageId);
            _message = MessagesModule.Instance.GetMessageById(id) as AccentButtonMessage;
            if (_message == null)
            {
                LpLogger.LOG_E($"Action Sheet Message didn't find message (id: {id})");
                Dismiss();
            }
        }

        public override Dialog OnCreateDialog(Bundle savedInstanceState)
        {
            var builder = new AlertDialog.Builder(Context, Resource.Style.LpAlertDialogStyle);
            var inflater = LayoutInflater.From(builder.Context);
            builder.SetView(InflateView(inflater));
            return builder.Create();
        }

        private View InflateView(LayoutInflater inflater)
        {
            _rootLayout = inflater.Inflate(Resource.Layout.fragment_accent_button_message, null);

            var messageText = _rootLayout.FindViewById<TextView>(Resource.Id.message_text);
            messageText.SetTextAndVisibility(_message.Text);

            var messageHeader = _rootLayout.FindViewById<TextView>(Resource.Id.message_header);
            messageHeader.SetTextAndVisibility(_message.Header);

            // костыль для отступов для APP-3945 чтобы не рисовать лишнюю View
            if (messageHeader.Text.Contains(I18N.AVAILABILITY_INTERVALS_TEXT))
            {
                messageText.SetLineSpacing(32.0f, 1.0f);
            }

            foreach (var messageButton in _message.Buttons)
            {
                switch (messageButton.Type)
                {
                    case ButtonMessageItem.ButtonType.Accent:
                        var accentButton = _rootLayout.FindViewById<BottomButton>(Resource.Id.message_button_accent);
                        accentButton.Visibility = ViewStates.Visible;
                        accentButton.ActionText.Text = messageButton.Text;
                        accentButton.ActionText.SetAutoSizeTextTypeUniformWithConfiguration(12, 24, 1, 1);
                        accentButton.SetActionButtonClick((s, a) =>
                        {
                            messageButton.Action.Invoke();
                        });
                        break;
                    case ButtonMessageItem.ButtonType.Cancel:
                        var cancelButton = _rootLayout.FindViewById<TextView>(Resource.Id.message_button_cancel);
                        cancelButton.Visibility = ViewStates.Visible;
                        cancelButton.Text =  messageButton.Text;
                        cancelButton.SetAutoSizeTextTypeUniformWithConfiguration(12, 24, 1, 1);
                        cancelButton.Click += (sender, args) =>
                        {
                            messageButton.Action.Invoke();
                        };
                        break;
                    case ButtonMessageItem.ButtonType.Hint:
                        var hintButton = _rootLayout.FindViewById<TextView>(Resource.Id.message_button_hint);
                        hintButton.Visibility = ViewStates.Visible;
                        hintButton.Text =  messageButton.Text;
                        hintButton.SetAutoSizeTextTypeUniformWithConfiguration(12, 24, 1, 1);
                        hintButton.Click += (sender, args) =>
                        {
                            messageButton.Action.Invoke();
                        };
                        break;
                }
            }
            
            return _rootLayout;
        }

        public override void OnCancel(IDialogInterface dialog)
        {
            _message?.OnPositiveAction();
            base.OnCancel(dialog);
        }

        public bool TryShowMessage(long messageId)
        {
            var tag = MessageDialogFragment.MessageTag + messageId;
            return this.TryShowFragment(ScreenController.Instance.Context as AppCompatActivity, tag);
        }
    }
}