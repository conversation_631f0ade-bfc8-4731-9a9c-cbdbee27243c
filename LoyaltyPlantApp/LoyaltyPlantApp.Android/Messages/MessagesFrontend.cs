using Android.App;
using Android.Content;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Hints.Model;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Messages.ChooserMessages;
using LoyaltyPlant.Messages.Model;
using LoyaltyPlant.Messages.Model.ServerMessages;
using LoyaltyPlant.Texts;
using LoyaltyPlant.TieredLoyalty.Model;
using LoyaltyPlantApp.Droid.Base;
using LoyaltyPlantApp.Droid.Base.Screens;
using LoyaltyPlantApp.Droid.Base.Screencontroller;
using LoyaltyPlantApp.Droid.Feedback;
using LoyaltyPlantApp.Droid.Hints;
using LoyaltyPlantApp.Droid.TieredLoyalty;
using LoyaltyPlant.TieredLoyalty;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.Utils;
using System.Linq;

namespace LoyaltyPlantApp.Droid.Messages
{
    public class MessagesFrontend : AppFrontend, IMessagesFrontend
    {
        public void OpenPermissionsSettings()
        {
            Context.StartActivity(new Intent(
                Android.Provider.Settings.ActionApplicationDetailsSettings,
                Android.Net.Uri.Parse("package:" + Android.App.Application.Context.PackageName)));
        }

        public bool TryShowMessage(Message message)
        {
            if (!message.IsAllowOnScreen(ScreenController.Instance.CurrentScreenType))
            {
                return false;
            }
            var isAchievementsCampaignAvailable = PartnerModule.Instance.IsNeedToShowAchievementsMessage(out var achievementMessageId);

            IMessageDialogFragment fragment;
            switch (message)
            {
                case CardMessage cardMessage:
                {
                    if (TieredLoyaltyModule.Instance.IsTieredLoyaltyV2Enabled)
                    {
                        var currentTier = TieredLoyaltyModule.Instance.GetCurrentTier();
                        if (currentTier?.CouponId == cardMessage.CardId)
                        {
                            fragment = AnimatedTierPointsMessageFragment.NewInstance(cardMessage.Id);
                            break;
                        }
                    }

                    if (isAchievementsCampaignAvailable && achievementMessageId.ContainsKey(cardMessage.CardId))
                    {
                        var timeLimitForAnimation = PartnerModule.Instance.GetAchievementTimeBlock();
                        fragment = AnimatedAchievementsLoyaltyFragment.NewInstance(cardMessage.Id, achievementMessageId[cardMessage.CardId], timeLimitForAnimation); 
                        break;
                    }

                    fragment = TextWithCardMessageDialogFragment.NewInstance(cardMessage.Id);
                    break;
                }
                case PointsMessage pointsMessage:
                {
                    fragment = AnimatedPointsMessageFragment.NewInstance(pointsMessage.Id);
                    break;
                }
                case TieredLoyaltyMessage tieredLoyaltyMessage:
                {
                    fragment = AnimatedTieredLoyaltyFragment.NewInstance(tieredLoyaltyMessage.Id);
                    break;
                }
                case HintMessage hintMessage:
                {
                    fragment = HintGeneralFragment.NewInstance(hintMessage.Id);
                    break;
                }
                case FeedbackMessage feedbackMessage:
                {
                    fragment = FeedbackMainFragment.NewInstance(feedbackMessage.Id);
                    break;
                }
                case ActionSheetMessage actionSheetMessage:
                {
                    fragment = ActionSheetMessageFragment.NewInstance(actionSheetMessage.Id);
                    break;
                }
                case AccentButtonMessage accentButtonMessage:
                {
                    fragment = AccentButtonMessageFragment.NewInstance(accentButtonMessage.Id);
                    break;
                }
                default:
                {
                    fragment = PlainTextMessageDialogFragment.NewInstance(message.Id);
                    break;
                }
            }
            
            return fragment.TryShowMessage(message.Id);
        }

        public void ShowPopup(string text)
        {
            if (Context is Activity activity)
            {
                activity.RunOnUiThread(() =>
                {
                    var toast = Toast.MakeText(Context, text, ToastLength.Long);
                    toast.Duration = ToastLength.Long;

                    //подсказки для экранов валидации сдвигаем наверх, чтобы они не заслоняли QR-код. //todo move gravity to backend
                    if (text == I18N.BC_VALIDATION_HINT || text == I18N.PRESENT_VALIDATION_HINT)
                    {
                        toast.SetGravity(GravityFlags.Top | GravityFlags.CenterHorizontal, 0, 20);
                    }

                    toast.Show();
                });
            }
        }

        public void DismissMessage(Message message)
        {
            if (Context is BaseActivity fragmentActivity && !fragmentActivity.IsFinishing)
            {
                var fragment = MessageDialogFragment.FindFragment(fragmentActivity, message.Id);
                if (fragment == null)
                {
                    LpLogger.LOG_E($"Didn't find fragment for message: {message}");
                    return;
                }
                fragment.Dismiss();
            }
        }
    }
}