using Android.App;
using Android.Content;
using Android.Gms.Common;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Geofencing;
using LoyaltyPlantApp.Droid.Base.Utils.GoogleApi;

namespace LoyaltyPlantApp.Droid.Geofencing
{
    /// <summary>
    /// GeofenceBootReceiver class
    /// Receives BOOT_COMPLETED event
    /// </summary>
    [BroadcastReceiver(Exported = false)]
    [IntentFilter(new[] { Intent.ActionBootCompleted })]
    public class GeofenceBootReceiver : BroadcastReceiver
    {
        /// <summary>
        /// On boot completed restores all persisted regions
        /// </summary>
        /// <param name="context"></param>
        /// <param name="intent"></param>
        public override void OnReceive(Context context, Intent intent)
        {
            if (context.IsPlayServicesAvailable())
            {
                GeofencingModule.Instance.UpdateGeofenceZones();
            }
        }
    }
}