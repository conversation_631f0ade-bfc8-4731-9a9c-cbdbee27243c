using System;
using Android.App;
using Android.Content;
using Google.Android.Material.BottomSheet;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Legal;
using LoyaltyPlant.Legal.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Base.Screens;
using LoyaltyPlantApp.Droid.Base.Utils;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Legal
{
    public class LegalFrontend : AppFrontend, ILegalFrontend
    {
        public void ShowLegalDocumentScreen(EulaScreenAction action)
        {
            if (Context is Activity activity)
            {
                Intent intent = new Intent(Context, typeof(EulaActivity));
                intent.PutExtra(EulaActivity.EulaUrlKey, action.Url);
                intent.PutExtra(EulaActivity.EulaTypeKey, (int)action.DocumentType);
                activity.StartActivityForResult(intent, EulaActivity.EulaScreenRequestCode);
            }
        }

        public void ShowLegalDocumentWasUpdatedScreen(UpdateDocumentAction action)
        {
            Context.StartIntentInRootActivity(activity =>
            {
                var eulaFragment = EulaWasUpdatedFragment.NewInstance(action.ToBundle());
                eulaFragment.ShowInContainerActivity(activity);
            });
        }

        public void CreateConfirmationMessage(EulaConfirmationMessage message)
        {
            if (Context is Activity activity)
            {
                var bottomSheetDialog = new BottomSheetDialog(activity);
                var rootView = activity.LayoutInflater.Inflate(Resource.Layout.fragment_bottom_sheet, null);

                var buttonsContainer = rootView.FindViewById<ViewGroup>(Resource.Id.buttons_container);

                var header = rootView.FindViewById<TextView>(Resource.Id.header);
                header.SetTextAndVisibility(message.Header);

                var description = rootView.FindViewById<TextView>(Resource.Id.description);
                description.Text = message.Description;
                description.Visibility = string.IsNullOrEmpty(message.Description) ? ViewStates.Gone : ViewStates.Visible;

                foreach (var messageButton in message.Actions)
                {
                    View view;
                    if (messageButton.Item1 == I18N.CANCEL)
                    {
                        view = activity.LayoutInflater.Inflate(Resource.Layout.layout_bottom_sheet_cancel_item, null, false);
                    }
                    else
                    {
                        view = activity.LayoutInflater.Inflate(Resource.Layout.layout_bottom_sheet_item, null, false);
                    }
                    FillItem(view, messageButton);
                    buttonsContainer.AddView(view);
                }

                message.OnCancel += () =>
                {
                    bottomSheetDialog.Dismiss();
                };

                bottomSheetDialog.SetContentView(rootView);
                bottomSheetDialog.Show();
            }
        }

        public void ShowEulaError(string errorText)
        {
            if (Context is BaseActivity baseActivity)
            {
                baseActivity.OnEditTextError(errorText);
            }
        }

        private void FillItem(View view, Tuple<string, Action> meessageButton)
        {
            var textView = view.FindViewById<TextView>(Resource.Id.item_text);
            textView.Text = meessageButton.Item1;
            view.Click += (sender, args) =>
            {
                meessageButton.Item2?.Invoke();
            };
        }
    }
}