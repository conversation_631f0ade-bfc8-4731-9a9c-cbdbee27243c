using LoyaltyPlant.Cities;
using LoyaltyPlant.Cities.Model;
using LoyaltyPlantApp.Droid.Base;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Cities
{
    public class CitiesFrontend : AppFrontend, ICitiesFrontend
    {
        public void ShowSelectCityScreen(SelectCityAction action)
        {
            var canGoBack = action.CanGoBack;
            Context.StartIntentInRootActivity((activity =>
            {
                var selectCity = SelectCityFragment.NewInstance(action.ToBundle());
                selectCity.ShowInContainerActivity(activity, canGoBack);
            }));
        }
    }
}