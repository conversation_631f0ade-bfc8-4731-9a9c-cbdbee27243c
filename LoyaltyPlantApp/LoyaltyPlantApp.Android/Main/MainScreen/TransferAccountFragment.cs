using System;
using Android.OS;
using Android.Views;
using Android.Widget;
using AndroidX.Fragment.App;
using Google.Android.Material.BottomSheet;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Customviews;
using LoyaltyPlantApp.Droid.Surveys.Views;
using LoyaltyPlantApp.Droid.User;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Main.MainScreen
{
    public class TransferAccountFragment : Fragment
    {
        private EmailField _emailField;
        private FormEmailInput _formEmailInput;
        private LinearLayout _questionsContainer;

        #region Overridables 
        public override View OnCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState)
        {
            var rootView = inflater.Inflate(Resource.Layout.sign_in_form_fragment, null);

            var subtitleView = rootView.FindViewById<TextView>(Resource.Id.subtitle_text);
            subtitleView.Text = string.Format(subtitleView.Text, PartnerModule.Instance.Model.ApplicationName);

            InitQuestionBlock();
            _questionsContainer = rootView.FindViewById<LinearLayout>(Resource.Id.questions_container);
            _questionsContainer.AddView(_formEmailInput);

            var bottomButton = rootView.FindViewById<BottomButton>(Resource.Id.bottom_panel);
            bottomButton.SetActionButtonClick(OnClick_BottomButton);

            return rootView;
        }

        public override void OnResume()
        {
            base.OnResume();
            _formEmailInput.LayoutParameters.Width = ViewGroup.LayoutParams.MatchParent;
        }
        #endregion

        private void InitQuestionBlock()
        {
            _emailField = new EmailField(-1, -1, false,
                string.Empty, Meta.Email);
            _formEmailInput = new FormEmailInput(Activity, _emailField);
            _formEmailInput.FindViewById(Resource.Id.title).Visibility = ViewStates.Gone;
        }

        private async void OnClick_BottomButton(object sender, EventArgs args)
        {
            if (_emailField == null)
                return;

            string answer = _formEmailInput.EditText.Text;

            if (string.IsNullOrEmpty(answer))
            {
                _formEmailInput.SetError(string.Format(I18N.SURVEY_ERROR_INCORRECT_BIO, _emailField.Placeholder.ToLower()));
            }
            else if (_emailField.CheckAndApplyAnswer(answer, out var errorMessage))
            {
                Activity.RunOnUiThread(() => _formEmailInput.EditText.HideSoftKeyboard());
                await BackendModule.Instance.RestoreAccount(answer, () =>
                {
                    (Activity as TransparentBottomSheetActivity)._blackoutView.Visibility = ViewStates.Gone;
                    Activity.Finish();
                    return;
                }, () =>
                {
                    Activity.RunOnUiThread(() => _formEmailInput.EditText.ShowKeyboard());
                });
            }
            else
                _formEmailInput.SetError(errorMessage);
        }
    }
}
