using System;
using System.Collections.Generic;
using System.Linq;
using Android.Graphics;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Content.Model.Customization;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.TieredLoyalty;
using LoyaltyPlantApp.Droid.Main.MainScreen.CardContainers;
using LoyaltyPlantApp.Droid.TieredLoyalty;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Main.MainScreen.ViewHolders
{
    public class DefaultSectionViewHolder : SectionViewHolder
    {
        public bool SectionWithTieredLoyalty { get; private set; }
        public bool SectionWithTieredLoyaltyV2 { get; private set; }
        public bool TieredLoyaltyV2Updated { get; set; }
        
        public DefaultSectionViewHolder(IntPtr javaReference, JniHandleOwnership transfer) : base(javaReference, transfer)
        {
        }

        //costyl for bottom section
        public DefaultSectionViewHolder(View sectionView, ParentSection section) : base(sectionView)
        {
            Section = section;
            var cardsContainer = sectionView.FindViewById<ViewGroup>(Resource.Id.cards_container);
            Background = sectionView.FindViewById<ImageView>(Resource.Id.section_background);

            var verticalPadding = sectionView.Resources.GetDimensionPixelSize(Resource.Dimension.bottom_section_padding);
            cardsContainer.SetPadding(0, verticalPadding, 0, 0);

            float contentHeight = cardsContainer.PaddingTop + cardsContainer.PaddingBottom;
            var cardRects = ContentModule.Instance.Controller.GetCardRectsInSection(Section);
            FillCards(section.ParentSectionCustomization, cardRects, cardsContainer, ref contentHeight);
            SetSectionSize(contentHeight);
            
            var sectionContainer = sectionView.FindViewById<LinearLayout>(Resource.Id.section_container);
            (sectionContainer.LayoutParameters as FrameLayout.LayoutParams).Gravity = GravityFlags.Center;
            cardsContainer.LayoutParameters.Width = ViewGroup.LayoutParams.WrapContent;
            sectionContainer.LayoutParameters.Width = ViewGroup.LayoutParams.WrapContent;
        }
        
        public DefaultSectionViewHolder(View sectionView, int sectionId) : base(sectionView)
        {
            Section = ContentModule.Instance.GetSection(sectionId) as ParentSection;
            if (Section == null)
                return;
            
            var cardsContainer = sectionView.FindViewById<ViewGroup>(Resource.Id.cards_container);
            Background = sectionView.FindViewById<ImageView>(Resource.Id.section_background);

            if (!Section.NeedToShowHeader())
                cardsContainer.SetTopPadding(sectionView.Resources.GetDimensionPixelSize(Resource.Dimension.base_margin_sixteen));
            
            var cardRects = ContentModule.Instance.Controller.GetCardRectsInSection(Section);
            float contentHeight = 0;
            if (cardRects.Count > 0)
            {
                contentHeight = cardsContainer.PaddingTop +  cardsContainer.PaddingBottom;
            }
            else
            {
                cardsContainer.Visibility = ViewStates.Gone;
            }
            
            FillCards(CustomizationInfo, cardRects, cardsContainer, ref contentHeight);
            SetSectionHeader(ref contentHeight);
            SetChildSections(Section.ChildSections, ref contentHeight);
            SetSectionSize(contentHeight);
        }

        private void FillCards(ParentSectionCustomization customizationInfo, Dictionary<Card, LpRect> cardRects,
            ViewGroup cardsContainer, ref float contentHeight)
        {
            float maxCardsHeight = 0;
            LpRect tieredLoyaltyRectView = null;
            SectionWithTieredLoyaltyV2 = false;
            foreach (var cardAndRect in cardRects)
            {
                var rect = cardAndRect.Value;
                var card = cardAndRect.Key;

                var cardViewContainer = CardViewContainer.CreateContainerByCard(card);
                if (cardViewContainer is TieredLoyaltyCardContainer)
                {
                    SectionWithTieredLoyalty = true;
                    rect.Height = cardsContainer.Context.Resources.GetDimensionPixelSize(Resource.Dimension.card_view_tiered_loyalty_height);
                }
                else if (cardViewContainer is TieredLoyaltyV2CardContainer)
                {
                    SectionWithTieredLoyaltyV2 = true;
                    rect.Height = GetTLv2ContainerHeight(cardsContainer.Context);
                    if (!TieredLoyaltyModule.Instance.IsTieredLoyaltyV2Enabled || TieredLoyaltyModule.Instance.Model?.TierV2Data == null)
                    {
                        TieredLoyaltyModule.Instance.Model.Card.State = Card.States.Hidden;
                        cardsContainer.Visibility = ViewStates.Gone;
                    }
                }
                else
                    cardViewContainer = CardViewContainer.CreateContainerByCardStyle(customizationInfo.CardsStyle);

                if (cardViewContainer != null)
                {
                    var cardView = cardViewContainer.Inflate(cardsContainer, rect);

                    maxCardsHeight = Math.Max(maxCardsHeight, rect.Bottom);

                    cardViewContainer.Setup(customizationInfo);
                    CardViewContainers.Add(cardViewContainer);
                    
                    cardView.Click += (sender, args) =>
                    {
                        card.OnClicked();
                    };
                    cardsContainer.AddView(cardView);
                }
            }

            contentHeight += maxCardsHeight;
        }

        public void UpdateCards()
        {
            if (Section == null)
                return;

            var i = 0;
            var cards = Section.GetCards();
            foreach (var cardViewContainer in CardViewContainers)
            {
                if (i < cards.Count)
                    cardViewContainer.Bind(cards[i]);

                i++;
            }

            BindHeaderText();
            LoadSectionBackgroundImage();
            BindChildSections();
        }

        public override void Bind(int position, IMainScreenElement dataItem)
        {
            UpdateCards();
        }

        private float GetTLv2ContainerHeight(Android.Content.Context context)
        {
            var activeCoinHeight = context.Resources.GetDimensionPixelSize(Resource.Dimension.current_tier_icon_height);
            var headerPartCardContent = new List<int>()
            {
                Resource.Dimension.general_line_height,
                Resource.Dimension.header_text_line_spacing,
                Resource.Dimension.base_margin_four,
                Resource.Dimension.general_line_height
            };
            var tierMainInformationHeight = headerPartCardContent.
                Select(resourceId => context.Resources.GetDimensionPixelSize(resourceId)).
                Sum();

            var bottomPartCardContent = new List<int>()
            {
                Resource.Dimension.base_margin_eight,
                Resource.Dimension.general_line_height,
                Resource.Dimension.base_margin_four,
                Resource.Dimension.active_progress_bar_height,
                Resource.Dimension.active_progress_bar_margin,
                Resource.Dimension.active_progress_bar_margin,
                Resource.Dimension.card_content_cadding,
                Resource.Dimension.card_content_cadding,
                Resource.Dimension.base_margin_four
            };
            var sumContentHeight = bottomPartCardContent.
                Select(resourceId => context.Resources.GetDimensionPixelSize(resourceId)).
                Sum();
            sumContentHeight += Math.Max(activeCoinHeight, tierMainInformationHeight);

            var initialProgrammedHeight = context.Resources.GetDimensionPixelSize(Resource.Dimension.card_view_tiered_loyalty_v2_height);
            LpLogger.LOG_I($"TLv2 Card Height: Calculated: {sumContentHeight} VS Initial: {initialProgrammedHeight}");

            return (sumContentHeight + initialProgrammedHeight) / 2;
        }
    }
}