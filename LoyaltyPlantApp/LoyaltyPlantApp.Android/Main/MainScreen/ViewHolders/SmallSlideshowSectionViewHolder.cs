using System;
using System.Linq;
using System.Text;

using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Runtime;
using AndroidX.Core.Content;
using AndroidX.Core.View;
using AndroidX.RecyclerView.Widget;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Content;
using LoyaltyPlant.Core;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Content.Model.Customization;
using LoyaltyPlant.Core.UI;
using LoyaltyPlantApp.Droid.Base.Utils;
using LoyaltyPlantApp.Droid.Customviews;
using Card = Stripe.Card;

namespace LoyaltyPlantApp.Droid.Main.MainScreen.ViewHolders
{
    public class SmallSlideshowSectionViewHolder : SectionViewHolder
    {
        private readonly HorizontalSlideShowAdapter _adapter;

        public SmallSlideshowSectionViewHolder(IntPtr javaReference, JniHandleOwnership transfer) : base(javaReference, transfer)
        {
            
        }
        
        public SmallSlideshowSectionViewHolder(View sectionView, int sectionId) : base(sectionView)
        {
            Background = sectionView.FindViewById<ImageView>(Resource.Id.section_background);
            
            var indicator = sectionView.FindViewById<CirclePageIndicator>(Resource.Id.indicator);
            var recyclerView = sectionView.FindViewById<RecyclerView>(Resource.Id.inner_recyclerview);

            var contentModule = ContentModule.Instance;
            Section = contentModule.GetSection(sectionId) as ParentSection;
            if (Section == null)
                return;
            
            if (!Section.NeedToShowHeader())
                recyclerView.SetPadding(recyclerView.PaddingLeft, 
                    sectionView.Context.Resources.GetDimensionPixelSize(Resource.Dimension.base_margin_sixteen), 
                    recyclerView.PaddingRight, recyclerView.PaddingBottom);
            
            var cardRects = contentModule.Controller.GetCardRectsInSection(Section).First();
            var cardRect = cardRects.Value;
            var bottomPadding = CustomizationInfo.CardsBorder == ParentSectionCustomization.CardsBorders.Shadows ||
                                CustomizationInfo.CardsBorder == ParentSectionCustomization.CardsBorders.BordersWithShadows
                ? sectionView.Resources.GetDimensionPixelSize(Resource.Dimension.custom_shadow_padding)
                : 0;
            recyclerView.LayoutParameters.Height = bottomPadding +
                (int) cardRect.Height + recyclerView.PaddingTop + recyclerView.PaddingBottom;
            
            ViewCompat.SetNestedScrollingEnabled(recyclerView, false);
            _adapter = new HorizontalSlideShowAdapter(CustomizationInfo)
            {
                Rect = cardRect
            };
            recyclerView.SetAdapter(_adapter);
            recyclerView.SetLayoutManager(new LinearLayoutManager(recyclerView.Context, LinearLayoutManager.Horizontal, false));
            var pagerSnapHelper = new LpPagerSnapHelper();
            pagerSnapHelper.AttachToRecyclerView(recyclerView);

            var fillColor = new Color(ContextCompat.GetColor(sectionView.Context, Resource.Color.BackgroundTextColor))
            {
                A = 126
            };
            indicator.SetSnapHelper(pagerSnapHelper, recyclerView);
            indicator.SetFillColor(fillColor);
            indicator.SetSnap(true);
            indicator.SetRadiusMultipier(4);

            float contentHeight = recyclerView.LayoutParameters.Height +
                                indicator.PaddingTop + indicator.PaddingBottom+ indicator.LayoutParameters.Height;
            SetSectionHeader(ref contentHeight);
            SetChildSections(Section.ChildSections, ref contentHeight);
            SetSectionSize(contentHeight);
        }

        public override void Bind(int position, IMainScreenElement dataItem)
        {
            if (Section == null)
                return;
            
            _adapter.Update(Section.GetCards());
            BindHeaderText();
            LoadSectionBackgroundImage();
            BindChildSections();
        }
    }
}