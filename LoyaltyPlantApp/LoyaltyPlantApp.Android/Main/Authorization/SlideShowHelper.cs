using System.Linq;
using Android.Content;
using Android.Content.Res;
using Android.Graphics.Drawables;
using AndroidX.Core.Content;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlantApp.Droid.Main.Authorization
{
    public class SlideshowHelper
    {
        private readonly Resources _resources;
        private readonly string _languageIsoName;
        private readonly Context _context;
        private readonly bool _isDefaultLanguage;
        private const string DefaultLanguagePath = "default";
        private const string SlideshowImageNamePrefix = "slideshow_";
        private const int MaxNumberOfSlides = 6;


        public SlideshowHelper(Resources resources, Context context, string languageIsoName, bool isDefaultLanguage)
        {
            _resources = resources;
            _languageIsoName = languageIsoName;
            _context = context;
            _isDefaultLanguage = isDefaultLanguage;
        }

        public int[] GetSlides(out int slidesCount)
        {
            var slides = _isDefaultLanguage
                ? LoadSlides(DefaultLanguagePath)
                : LoadSlides(_languageIsoName);

            slidesCount = GetSlidesCountForSlides(slides);

            if (slidesCount != 0 || _isDefaultLanguage)
                return slides;
            
            slides = LoadSlides(DefaultLanguagePath);
            slidesCount = GetSlidesCountForSlides(slides);

            return slides;
        }
        
        private int GetSlidesCountForSlides(int[] slides)
        {
            if (slides.IsNullOrEmpty() || slides.Contains(0))
                return 0;
            
            return slides
                .Select(slide => ContextCompat.GetDrawable(_context, slide) as BitmapDrawable)
                .Count(image => image != null && image.Bitmap.Height > 2);
        }

        private int[] LoadSlides(string languageIsoName)
        {
            var slides = new int[MaxNumberOfSlides];
            
            for (var i = 0; i < MaxNumberOfSlides; i++)
                slides[i] = GetResourceId($"{SlideshowImageNamePrefix}{languageIsoName}_{i + 1}");
            
            return slides;
        }

        private int GetResourceId(string resourceName)
        {
            return _resources.GetIdentifier(resourceName, "drawable", _context.PackageName);
        }
    }
}