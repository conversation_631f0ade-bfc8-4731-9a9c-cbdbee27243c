using Android.Content;
using Android.Graphics;
using Android.Graphics.Drawables;
using Android.OS;
using Android.Util;
using Android.Widget;

namespace LoyaltyPlantApp.Droid.Customviews
{
    public enum PreventRoundCornersSide
    {
        None,
        Bottom,
        Right
    }
    
    public class ImageRoundedView : ImageView
    {
        public float CornerRadius { get; set; }
        public PreventRoundCornersSide PreventRoundCornersSide { get; set; } = PreventRoundCornersSide.None;

        public override void SetImageDrawable(Drawable drawable)
        {
            if (Build.VERSION.SdkInt < BuildVersionCodes.Lollipop && CornerRadius > 0 && 
                (drawable is BitmapDrawable bitmapDrawable))
            {
                var roundedDrawable = new RoundCornersDrawable(bitmapDrawable.Bitmap, CornerRadius, PreventRoundCornersSide);
                base.SetImageDrawable(roundedDrawable);
            }
            else
            {
                base.SetImageDrawable(drawable);
            }
        }

        public ImageRoundedView(Context context) : base(context)
        {
            Initialize();
        }

        public ImageRoundedView(Context context, IAttributeSet attrs) : base(context, attrs)
        {
            Initialize();
        }

        public ImageRoundedView(Context context, IAttributeSet attrs, int defStyle) : base(context, attrs, defStyle)
        {
            Initialize();
        }

        void Initialize()
        {
        }
    }
    
        /**
     * Image with rounded corners
     *
     * You can find the original source here:
     * http://www.curious-creature.org/2012/12/11/android-recipe-1-image-with-rounded-corners/
     *
     * <AUTHOR> Mariotti (<EMAIL>)
     */
    public class RoundCornersDrawable : Drawable 
    {
        private  float _cornerRadius;
        private  RectF _rect = new RectF();
        private RectF _preventCornerRect1 = new RectF();
        private RectF _preventCornerRect2 = new RectF();
        private  Paint _paint;
        private readonly PreventRoundCornersSide _preventRoundCornersSide;

        public RoundCornersDrawable(Bitmap bitmap, float cornerRadius, PreventRoundCornersSide preventRoundCornersSide) 
        {
            _cornerRadius = cornerRadius;
            var bitmapShader = new BitmapShader(bitmap, Shader.TileMode.Clamp, Shader.TileMode.Clamp);
    
            _paint = new Paint();
            _paint.AntiAlias = true;
            _paint.SetShader(bitmapShader);
            _preventRoundCornersSide = preventRoundCornersSide;
        }
    
        protected override void OnBoundsChange(Rect bounds)
        {
            base.OnBoundsChange(bounds);
            _rect.Set(0, 0, bounds.Width(), bounds.Height());
            var halfWidth = (float) bounds.Width() / 2;
            var halfHeight = (float) bounds.Height() / 2;
            switch (_preventRoundCornersSide)
            {
                case PreventRoundCornersSide.Bottom:
                    _preventCornerRect1.Set( halfWidth, 
                        halfHeight,
                        bounds.Width(), 
                        bounds.Height());
                    _preventCornerRect2.Set( 0,  
                        halfHeight, 
                        halfWidth, 
                        bounds.Height());
                    break;
                case PreventRoundCornersSide.Right:
                    _preventCornerRect1.Set( halfWidth, 
                        halfHeight,
                        bounds.Width(), 
                        bounds.Height());
                    _preventCornerRect2.Set(halfWidth, 
                        0,
                        bounds.Width(),
                        halfHeight);
                    break;
            }
        }
    
        public override void Draw(Canvas canvas)
        {
            canvas.DrawRoundRect(_rect, _cornerRadius, _cornerRadius, _paint);
            switch (_preventRoundCornersSide)
            {
                case PreventRoundCornersSide.Right:
                case PreventRoundCornersSide.Bottom:
                    canvas.DrawRect(_preventCornerRect1, _paint); //only bottom-right corner not rounded
                    canvas.DrawRect(_preventCornerRect2, _paint); //only bottom-left corner not rounded
                    break;
            }
        }
    
        public override int Opacity => (int) Android.Graphics.Format.Translucent;
       
    
        public override void SetAlpha(int alpha) {
            _paint.Alpha = alpha;
        }
    
        public override void SetColorFilter(ColorFilter cf) {
            _paint.SetColorFilter(cf);
        }
    
    
    }
}
