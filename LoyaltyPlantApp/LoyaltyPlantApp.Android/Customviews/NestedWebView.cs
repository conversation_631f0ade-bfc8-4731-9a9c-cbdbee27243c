using System;
using Android.Content;
using AndroidX.Core.View;
using Android.Util;
using Android.Webkit;

namespace LoyaltyPlantApp.Droid.Customviews
{
    public class NestedWebView : WebView, INestedScrollingChild
    {
        public Action<int> OnVerticalScroll { get; set; }

        public NestedWebView(Context context) : this(context, null)
        {
            
        }

        public NestedWebView(Context context, IAttributeSet attrs) : 
            this(context, attrs, Android.Resource.Attribute.WebViewStyle)
        {
            
        }


        public NestedWebView(Context context, IAttributeSet attrs, int defStyleAttr) : base(context, attrs, defStyleAttr)
        {

        }

        protected override void OnOverScrolled(int scrollX, int scrollY, bool clampedX, bool clampedY)
        {
            base.OnOverScrolled(scrollX, scrollY, clampedX, clampedY);
            OnVerticalScroll?.Invoke(scrollY);
        }
    }
}