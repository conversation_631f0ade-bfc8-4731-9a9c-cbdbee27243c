using Android.App;
using Android.Content;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlantApp.Droid.Base.Screencontroller;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Base
{
    public class AppFrontend : IModuleFrontend
    {
        protected Context Context => ScreenController.Instance.Context ?? Application.Context;

        public void HideLoadingIndicator()
        {
            ScreenController.Instance.HideProgressBarDialog();
        }

        public void MakeCall(string phone)
        {
            Context.MakeCall(phone);
        }

        public void ExitApplication()
        {
            Context.StartIntentInRootActivity((activity => activity.Finish()));
        }

        public void ShowLoadingIndicator(string text, float backgroundAlpha) => ShowLoadingIndicator(text);

        public void ShowLoadingIndicator(string text)
        {
            ScreenController.Instance.SetProgressBarDialog(text);
        }
    }
}