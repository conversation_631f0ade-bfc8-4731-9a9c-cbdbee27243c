using Android.Graphics;

namespace LoyaltyPlantApp.Droid.Base.Utils.WebHelpers
{
    public class HtmlHelper
    {
        private string _html;

        public HtmlHelper(string sourceHtml)
        {
            _html = sourceHtml;
        }

        public string Html => _html;

        public HtmlHelper ColoredHtmlLinks(Color textColor, string bodyAttributes = "")
        {
            string textColorRgba = GetRgbaColor(textColor);
			_html = $"<head><style>a {{color: {textColorRgba};}}</style></head><body>{_html}</body>";
            return this;
        }

        public HtmlHelper WrapHtmlTag()
        {
            _html = "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01//EN\"\n" +
                   "\"http://www.w3.org/TR/html4/strict.dtd\"><html>" + _html +
                   "</html>";
            return this;
        }

        public static string GetRgbaColor(Color textColor)
        {
            return $"rgba({textColor.R},{textColor.G},{textColor.B},{textColor.A})";
        }
    }
}