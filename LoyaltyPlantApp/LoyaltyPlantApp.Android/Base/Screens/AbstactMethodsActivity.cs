using System.Collections.Generic;
using System.Linq;
using Android.OS;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.UI;
using LoyaltyPlantApp.Droid.Base.Screens.ViewHelpers;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;
using Toolbar = AndroidX.AppCompat.Widget.Toolbar;

namespace LoyaltyPlantApp.Droid.Base.Screens
{
    /* activity for payment methods and giftcertificates list */

    public abstract class AbstactMethodsActivity<T> : BaseActivity, IListUpdateble where T : class
	{
		protected AbstactListChooser<T> Chooser;
		private Toolbar _toolbar;
		private ViewGroup _listContainer;
		private ViewSwitcher _viewSwitcher;
		public override ApplicationScreenType ScreenType => ApplicationScreenType.PaymentStartScreen;

		protected override void OnCreate(Bundle bundle)
		{
			base.OnCreate(bundle);
			SetContentView(Resource.Layout.activity_payment_methods_profile);
			InitToolbar();
			_listContainer = FindViewById<ViewGroup>(Resource.Id.payments_list);
			_viewSwitcher = FindViewById<ViewSwitcher>(Resource.Id.view_switcher);
			_viewSwitcher.SetFadeAnimations();
			Chooser = InitChooser();
			InitEmptyImage();
			UpdatePaymentMethodsList();
		}

		private void InitToolbar()
		{
			_toolbar = FindViewById<Toolbar>(Resource.Id.toolbar);
			SetSupportActionBar(_toolbar);
			SupportActionBar.SetDisplayHomeAsUpEnabled(true);
			SupportActionBar.Title = ActivityTitle;
		}
       
		public void UpdatePaymentMethodsList()
		{
			var list = MethodsList;
			Chooser.SetupButtons(_listContainer, list);
			if (list.Any())
			{
				if (_viewSwitcher.NextView.Id == Resource.Id.nested_scroll_view)
				{
					_viewSwitcher.ShowNext();
				}
			}
			else
			{
				if (_viewSwitcher.NextView.Id == Resource.Id.empty_container)
				{
					_viewSwitcher.ShowNext();
				}
			}
		}

		private void InitEmptyImage()
		{
			if (EmptyImageResource > 0)
			{
				var emptyImage = FindViewById<ImageView>(Resource.Id.empty_image);
				emptyImage.SetImageResource(EmptyImageResource);
			}

			if (!string.IsNullOrEmpty(EmptyTitleText))
			{
				var emptyTitle = FindViewById<TextView>(Resource.Id.empty_title);
				emptyTitle.Text = EmptyTitleText;
			}

			if (!string.IsNullOrEmpty(EmptyDescriptionText))
			{
				var emptyDescription = FindViewById<TextView>(Resource.Id.empty_description);
				emptyDescription.Text = EmptyDescriptionText;
			}
		}

		protected virtual int EmptyImageResource => 0;
		protected virtual string EmptyTitleText => "";
		protected virtual string EmptyDescriptionText => "";
		
		protected abstract AbstactListChooser<T> InitChooser();
		protected abstract string ActivityTitle { get; }
		protected abstract IList<T> MethodsList { get; }
	}
    
	public interface IListUpdateble
	{
		void UpdatePaymentMethodsList();
	}
}
