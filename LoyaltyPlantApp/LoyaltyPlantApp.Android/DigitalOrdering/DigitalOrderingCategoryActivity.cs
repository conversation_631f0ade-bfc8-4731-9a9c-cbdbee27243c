using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Views;
using Android.Widget;
using AndroidX.AppCompat.App;
using AndroidX.Core.App;
using AndroidX.RecyclerView.Widget;
using LoyaltyPlant.Backend.UI;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Content.Model.Customization;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.Model.Menu;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlant.Texts;
using LoyaltyPlant.Core.Analytics;
using LoyaltyPlantApp.Droid.Base.Adapters;
using LoyaltyPlantApp.Droid.Base.Utils.Listhelpers;
using Toolbar = AndroidX.AppCompat.Widget.Toolbar;
using LoyaltyPlantApp.Droid.Main.MainScreen.CardContainers;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;
using LoyaltyPlant.TieredLoyalty;

namespace LoyaltyPlantApp.Droid.DigitalOrdering
{
    [Activity(Theme = "@style/LoyaltyPlantTheme.NoActionBar.MainBackground", LaunchMode = LaunchMode.SingleTop,
        ScreenOrientation = ScreenOrientation.Portrait)]
    public class DigitalOrderingCategoryActivity : DigitalOrderingBaseActivity
    {
        public const string CategoryId = "CategoryId";

        private CategoryMenuAdapter _adapter;
        private Category _category;
        private RecyclerView _recyclerView;
        private LinearLayout _layoutWithSearchView;

        public override ApplicationScreenType ScreenType => ApplicationScreenType.DoCategoryScreen;

        protected override void OnCreate(Bundle bundle)
        {
            base.OnCreate(bundle);
            SetContentView(Resource.Layout.activity_digital_ordering_main);

            Toolbar toolbar = FindViewById<Toolbar>(Resource.Id.toolbar);
            SetSupportActionBar(toolbar);
            SupportActionBar.SetDisplayHomeAsUpEnabled(true);
            ShowPointsAction = true;                

            if (_category == null)
            {
                if (DigitalOrderingModule.Instance.CurrentMenu == null)
                {
                    NullItemAction("CurrentMenu");
                    Finish();
                    return;
                }
                else if (Intent != null)
                {
                    var id = Intent.GetStringExtra(CategoryId);
                    _category = DigitalOrderingModule.Instance.CurrentMenu.GetCategory(id);
                    if (_category == null)
                    {
                        NullItemAction("Category in Menu");
                        Finish();
                        return;
                    }
                }
                else if (bundle != null)
                {
                    var id = bundle.GetString(CategoryId);
                    _category = DigitalOrderingModule.Instance.CurrentMenu.GetCategory(id);
                    if (_category == null)
                    {
                        NullItemAction("Intent and Category in Menu");
                        Finish();
                        return;
                    }
                }
            }

            SupportActionBar.Title = _category.Label;

            if (DigitalOrderingModule.Instance.IsNeedToShowSearchBar)
            {
                _layoutWithSearchView = FindViewById<LinearLayout>(Resource.Id.layout_search_view);
                _layoutWithSearchView.Visibility = ViewStates.Visible;

                var searchTextView = FindViewById<TextView>(Resource.Id.search_text);
                searchTextView.Text = I18N.SEARCH;

                _layoutWithSearchView.Click += LayoutWithSearchView_Click;
            }

            _recyclerView = FindViewById<RecyclerView>(Resource.Id.recycler_view);

            _adapter = new CategoryMenuAdapter(this, _category);

            _recyclerView.HasFixedSize = true;

            var mLayoutManager = new GridLayoutManager(this, 2);
            mLayoutManager.SetSpanSizeLookup(new ApplicationCardsSpanSizeLookup(_adapter));

            _recyclerView.SetLayoutManager(mLayoutManager);
            _recyclerView.AddItemDecoration(new CardsItemDecorator(this));
            _recyclerView.SetAdapter(_adapter);

            InitCartBar();
        }

        private void LayoutWithSearchView_Click(object sender, EventArgs e)
        {
            DigitalOrderingModule.Instance.Controller.ShowDoMenuSearchScreen();
        }

        protected override void OnResume()
        {
            base.OnResume();
            _adapter.Update();
        }

        protected override void OnDestroy()
        {
            if (DigitalOrderingModule.Instance.IsNeedToShowSearchBar && _layoutWithSearchView != null)
            {
                _layoutWithSearchView.Click -= LayoutWithSearchView_Click;
            }

            base.OnDestroy();
        }

        protected override void OnSaveInstanceState(Bundle outState)
        {
            outState.PutString(CategoryId, _category.Id);
            base.OnSaveInstanceState(outState);
        }

        // TODO отдельный класс
        private class CategoryMenuAdapter : ApplicationCardsAdapter
        {
            private readonly Category _category;

            public CategoryMenuAdapter(Context context, Category category) : base(context)
            {
                _category = category;
            }

            protected override IList<AdapterItem> GetAdapterItemList()
            {
                var mainScreenElements = _category?.GetMenuItemsViewsByCategory();
                return ConvertToAdapterItems(mainScreenElements);
            }

            protected override void OnSetupLayout(View rootView, AdapterViewHolder vh, int layoutId)
            {
                base.OnSetupLayout(rootView, vh, layoutId);
                switch (layoutId)
                {
                    //MainScreenElementType.DOMenuPosition
                    case Resource.Layout.do_item_card:
                        var cardView = vh.FindViewById<View>(Resource.Id.card_itself);
                        var parameters = (ViewGroup.MarginLayoutParams)cardView.LayoutParameters;
                        var padding = Context.Resources.GetDimensionPixelSize(Resource.Dimension.base_margin_eight);
                        var cardWidth = (Engine.Instance.Platform.ScreenWidthPx - 3 * padding) / 2;
                        var imageHeight = (int)(cardWidth * 2f / 3f);
                        var textHeight = Context.Resources.GetDimensionPixelSize(Resource.Dimension.do_card_text_height);
                        parameters.Height = imageHeight + textHeight;

                        cardView.Click += (sender, args) =>
                        {
                            OnViewHolderClick(sender, vh.AdapterPosition);
                        };

                        var plusButton = vh.FindViewById<View>(Resource.Id.cart_plus_button);
                        var minusButton = vh.FindViewById<View>(Resource.Id.cart_minus_button);
                        Action<int> updateScreenCounters = (position) =>
                        {
                            NotifyItemChanged(position);
                            (Context as DigitalOrderingBaseActivity).UpdateCartBar();
                        };
                        plusButton.Click += (sender, args) => DoQuickAddItem(sender, vh, updateScreenCounters);

                        minusButton.Click += (sender, args) => DoQuickRemoveItem(sender, vh, updateScreenCounters);
                        break;
                }
            }

            public override void ProceedWithDigitalOrderingItemElement(AdapterViewHolder viewHolder, IMainScreenElement dataItem)
            {
                var doMenuElement = dataItem as DoMenuItemElement;
                var menuItem = doMenuElement?.MenuItem;
                if (menuItem == null)
                    return;

                var cardMainTextView = viewHolder.FindViewById<TextView>(Resource.Id.card_text);
                cardMainTextView.Text = menuItem.Label;

                TextView itemPriceView;
                TextView moneyCoinView;
                if (Engine.Instance.IsFeatureActive(Feature.IS_QUICK_ADD_TURNED_ON))
                {
                    viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container_old).Visibility = ViewStates.Gone;
                    viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container).Visibility = ViewStates.Visible;
                    itemPriceView = viewHolder.FindViewById<TextView>(Resource.Id.card_price);
                    moneyCoinView = viewHolder.FindViewById<TextView>(Resource.Id.money_coin);
                }
                else
                {
                    viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container_old).Visibility = ViewStates.Visible;
                    viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container).Visibility = ViewStates.Gone;
                    itemPriceView = viewHolder.FindViewById<TextView>(Resource.Id.card_price_old);
                    moneyCoinView = viewHolder.FindViewById<TextView>(Resource.Id.money_coin_old);
                }
                itemPriceView.Visibility = ViewStates.Visible;
                itemPriceView.Text = menuItem.FormattedPrice;

                if (menuItem is GiftPosition present)
                {
                    bool shouldPriceBeVisible = !string.IsNullOrWhiteSpace(present.FormattedPrice) &&
                        present.LinkedPresent.ShouldPriceBeVisible();

                    if (shouldPriceBeVisible)
                    {
                        HandleDOPriceWidgets(shouldPriceBeVisible,
                        present.LinkedPresent.GetPriceInPoints(),
                        present.LinkedPresent.GetPriceForPresentIfNeeded(),
                        itemPriceView,
                        moneyCoinView);
                    }
                    else if (Engine.Instance.IsFeatureActive(Feature.IS_QUICK_ADD_TURNED_ON))
                        viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container).Visibility = ViewStates.Invisible;
                    else
                        viewHolder.FindViewById<LinearLayout>(Resource.Id.counter_container_old).Visibility = ViewStates.Invisible;

                    var presentPeriodView = viewHolder.FindViewById<TextView>(Resource.Id.period_present_text);
                    presentPeriodView.SetTextAndVisibility(present.LinkedPresent.GetPresentAvailableTimeLeftText());
                    if (presentPeriodView.Visibility == ViewStates.Visible)
                    {
                        presentPeriodView.SetBackground(CardViewContainer.GetPriceLabelBackground(Context,
                            CardViewContainer.PreprocessRTLPostion(ParentSectionCustomization.LabelPositions.TopLeft),
                            Context.GetColorEx(Resource.Color.NavigationDrawerAccentColor)));
                    }
                }

                var deliveryBadge = viewHolder.FindViewById<View>(Resource.Id.round_delivery_badge_view);
                var deliveryBadgeText = viewHolder.FindViewById<TextView>(Resource.Id.round_delivery_badge_text);

                var count = menuItem.GetFullCount();
                deliveryBadge.Visibility = count > 0 ? ViewStates.Visible : ViewStates.Gone;
                deliveryBadgeText.Text = count.ToString();

                var isBlockedGiftByTLv2 = menuItem is GiftPosition giftPosition &&
                    TieredLoyaltyModule.Instance != null &&
                    TieredLoyaltyModule.Instance.IsCardBlockedByTieredLoyaltyV2(giftPosition.LinkedPresent);

                var isAffectedByLimitRewardsFeature = menuItem is GiftPosition
                                                      && Engine.Instance.IsFeatureActive(Feature.LIMIT_REWARD_COUPONS_IN_ORDER);

                var lockView = viewHolder.FindViewById<View>(Resource.Id.tiered_loyalty_lock);
                var imageView = viewHolder.FindViewById<ImageView>(Resource.Id.card_picture);

                if (imageView != null)
                    ShowListImage(menuItem.Image, imageView);

                if (isBlockedGiftByTLv2)
                {
                    lockView.Visibility = ViewStates.Visible;
                    (lockView as ImageView).SetColorFilter(Context.GetColorEx(Resource.Color.CardTextColor));
                }
                else
                {
                    lockView.Visibility = ViewStates.Gone;
                    imageView.Foreground = null;
                }

                if (menuItem.IsQuickAddFeatureAvailable())
                    UpdateQuickAddCountersColor(viewHolder, count > 0, !isBlockedGiftByTLv2 && !isAffectedByLimitRewardsFeature);
            }

            //MainScreenElementType.SimpleCardElement:
            public override void ProceedWithSimpleCardElement(AdapterViewHolder viewHolder, IMainScreenElement element)
            {
                var menuItemElement = element as DoMenuItemElement;
                if (!(menuItemElement?.MenuItem is GiftPosition present))
                    return;

                var cardMainTextView = viewHolder.FindViewById<TextView>(Resource.Id.card_text);
                cardMainTextView.Text = present.Label;

                var priceTextView = viewHolder.FindViewById<TextView>(Resource.Id.card_price);
                viewHolder.FindViewById<TextView>(Resource.Id.period_badge).Visibility = ViewStates.Gone;

                bool shouldPriceBeVisible = !string.IsNullOrWhiteSpace(present.FormattedPrice) &&
                    present.LinkedPresent.ShouldPriceBeVisible();
                
                HandleDOPriceWidgets(shouldPriceBeVisible,
                    (int)present.Prices.PointsCount,
                    present.LinkedPresent.GetPriceForPresentIfNeeded(),
                    priceTextView,
                    viewHolder.FindViewById<TextView>(Resource.Id.money_coin));

                var deliveryBadge = viewHolder.FindViewById<View>(Resource.Id.round_delivery_badge_view);
                var deliveryBadgeText = viewHolder.FindViewById<TextView>(Resource.Id.round_delivery_badge_text);

                var count = present.GetFullCount();
                deliveryBadge.Visibility = count > 0 ? ViewStates.Visible : ViewStates.Gone;
                deliveryBadgeText.Text = count.ToString();

                viewHolder.FindViewById<TextView>(Resource.Id.period_present_text).Visibility = ViewStates.Gone;

                var imageView = viewHolder.FindViewById<ImageView>(Resource.Id.card_picture);
                if (imageView != null)
                {
                    ShowListImage(present.Image, imageView);
                }
            }

            // TODO Subscription почему-то стало unusing
            private void ProceedWithSubscriptionItemElement(AdapterViewHolder viewHolder, DoMenuSubscriptionItemElement element)
            {
                var card = element.SubCard;

                var periodBage = viewHolder.FindViewById<TextView>(Resource.Id.period_badge);
                if (periodBage != null)
                    periodBage.Visibility = ViewStates.Gone;

                var imageView = viewHolder.FindViewById<ImageView>(Resource.Id.card_picture);
                if (imageView != null)
                {
                    ShowListImage(card.PreviewImage, imageView);
                }

                var cardMainTextView = viewHolder.FindViewById<TextView>(Resource.Id.card_text);
                if (cardMainTextView != null)
                    cardMainTextView.Text = card.PreviewText;

                var deliveryBadge = viewHolder.FindViewById<View>(Resource.Id.round_delivery_badge_view);
                var deliveryBadgeText = viewHolder.FindViewById<TextView>(Resource.Id.round_delivery_badge_text);

                var gift = DigitalOrderingModule.Instance.GetGiftPositionBySubscriptionCard(card);
                var count = gift.GetFullCount();
                deliveryBadge.Visibility = count > 0 ? ViewStates.Visible : ViewStates.Gone;
                deliveryBadgeText.Text = count.ToString();

                var cardPresentCountLayout = viewHolder.FindViewById<LinearLayout>(Resource.Id.card_present_count_layout);
                if (cardPresentCountLayout != null)
                    cardPresentCountLayout.Visibility = ViewStates.Visible;

                var presentsCount = card.Presents.Count;
                var cardPresentCount = viewHolder.FindViewById<TextView>(Resource.Id.card_present_count);
                if (cardPresentCount != null)
                    cardPresentCount.Text = $"x{presentsCount}";
            }

            public override void ProceedWithDigitalOrderingCategoryDescription(AdapterViewHolder viewHolder, IMainScreenElement dataItem)
            {
                viewHolder.FindViewById<TextView>(Resource.Id.category_description).Text =
                    ((DoCategoryDescriptionElement)dataItem).Text;
            }

            protected override void OnClick(View sender, IMainScreenElement dataItem, MainScreenElementType itemType)
            {
                base.OnClick(sender, dataItem, itemType);
                switch (itemType)
                {
                    case MainScreenElementType.SimpleCardElement:
                    case MainScreenElementType.DoMenuPosition:
                        var menuItem = (dataItem as DoMenuItemElement)?.MenuItem;
                        if (!DigitalOrderingModule.Instance.Controller.EnsureMultiPresentSelectionDialog(menuItem as GiftPosition))
                        {
                            ClickOnCardWithTransition(menuItem, sender);
                        }
                        break;
                }
            }

            private void ClickOnCardWithTransition(MenuItem dataItem, View sender)
            {
                var intent = new Intent(Context, typeof(DigitalOrderingItemActivity));
                intent.PutExtra(DigitalOrderingItemActivity.ItemId, dataItem.Id);
                intent.PutExtra(DigitalOrderingItemActivity.CategoryId, _category.Id);

                LpAnalytics.OpenItemDoCategory(DigitalOrderingModule.Instance.GetFormattedDictionaryForAnalytics(dataItem));

                var activity = Context as AppCompatActivity;
                var imageView = sender.FindViewById(Resource.Id.card_picture);
                var lpImage = dataItem.Image;
                if (!dataItem.IsComboItem &&
                    lpImage.IsDownloaded() && imageView != null &&
                    activity != null)
                {
                    var pair = AndroidX.Core.Util.Pair.Create(imageView, imageView.TransitionName);
                    var options = ActivityOptionsCompat.MakeSceneTransitionAnimation(activity, pair);
                    activity.StartActivity(intent, options.ToBundle());
                }
                else
                    Context.StartActivity(intent);
            }

            protected override Task<IList<AdapterItem>> GetAdapterItemListAsync()
            {
                //TODO Need async implement
                throw new NotImplementedException();
            }
        }
    }
}
