using System;
using System.Collections.Generic;
using System.Linq;
using Android.Animation;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.Graphics;
using Android.Graphics.Drawables;
using Android.OS;
using Android.Speech;
using AndroidX.Core.Content;
using AndroidX.RecyclerView.Widget;
using Android.Views;
using Android.Views.Animations;
using Android.Widget;
using Java.Interop;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.DigitalOrdering.Model.Zones;
using LoyaltyPlant.DigitalOrdering.Utils.GoogleApis;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base.Screens;
using LoyaltyPlantApp.Droid.Base.Utils;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.DigitalOrdering
{
    [Activity(Theme = "@style/LoyaltyPlantTheme.NoActionBar.Transparent", 
        LaunchMode = LaunchMode.SingleTop,
        WindowSoftInputMode = SoftInput.AdjustResize)]
    public class DigitalOrderingSearchActivity : BaseActivity, ViewTreeObserver.IOnGlobalLayoutListener
    {
        public const string ExtraCircularRevealX = "EXTRA_CIRCULAR_REVEAL_X";
        public const string ExtraCircularRevealY = "EXTRA_CIRCULAR_REVEAL_Y";
        public const string ExtraProcessType = "EXTRA_PROCESS_TYPE";

        public const string ExtraOutletId = "EXTRA_OUTLET_ID";
        public const string ExtraPlace = "EXTRA_PLACE";

        public const int SpeechRequestCode = 9002;

        private int _revealX;
        private int _revealY;

        private View _rootLayout;
        private RecyclerView _recyclerView;
        private ISearchAdapter _adapter;
        private EditText _editQuery;
        private List<DOOutlet> _outlets;
        private ImageView _textActionButton;
        private DoProcessType _processType;
        
        private readonly MultiplyActionPreventer _selectPlacePreventer = new MultiplyActionPreventer(1000);

        public override ApplicationScreenType ScreenType => ApplicationScreenType.SearchDeliveryScreen;
        
        private bool _isClearAction;
        private bool IsClearAction
        {
            get => _isClearAction;
            set
            {
                if (value != IsClearAction)
                {
                    if (value)
                    {
                        _textActionButton.SetImageResource(Resource.Drawable.ic_close_black_24dp);
                        _textActionButton.ContentDescription = I18N.CLOSE;
                    }
                    else
                    {
                        _textActionButton.SetImageResource(Resource.Drawable.ic_microphone);
                        // TODO APP-4001 costyl microphone need to fix
                        _textActionButton.ContentDescription = "microphone";
                    }
                }
                _isClearAction = value;
            }
        }

        protected override void OnCreate(Bundle savedInstanceState)
        {
            base.OnCreate(savedInstanceState);
            if (Build.VERSION.SdkInt < BuildVersionCodes.O)
            {
                RequestedOrientation = ScreenOrientation.Portrait;
            }
            SetContentView(Resource.Layout.activity_do_search);

            _processType = (DoProcessType)Intent.GetIntExtra(ExtraProcessType, 0);

            _rootLayout = FindViewById(Resource.Id.root_layout);

            if (savedInstanceState == null && Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop &&
                Intent.HasExtra(ExtraCircularRevealX) &&
                Intent.HasExtra(ExtraCircularRevealY))
            {
                _rootLayout.Visibility = ViewStates.Invisible;

                _revealX = Intent.GetIntExtra(ExtraCircularRevealX, 0);
                _revealY = Intent.GetIntExtra(ExtraCircularRevealY, 0);

                ViewTreeObserver viewTreeObserver = _rootLayout.ViewTreeObserver;
                if (viewTreeObserver.IsAlive)
                {
                    viewTreeObserver.AddOnGlobalLayoutListener(this);
                }
            }
            else
            {
                _rootLayout.Visibility = ViewStates.Visible;
            }

            var backButton = FindViewById(Resource.Id.back_button);
            backButton.Click += (sender, args) =>
            {
                OnBackPressed();
            };
            backButton.ContentDescription = I18N.BACK;

            _editQuery = FindViewById<EditText>(Resource.Id.edit_query);
            _editQuery.Hint = _processType.IsDeliveryOrCatering() ? I18N.SEARCH_FOR_AN_ADDRESS : I18N.SEARCH_FOR_AN_OUTLET;
            _editQuery.TextChanged += (sender, args) =>
            {
                IsClearAction = !string.IsNullOrWhiteSpace(_editQuery.Text);
                _adapter.Update(_editQuery.Text);
            };

            var geocoder = DigitalOrderingModule.Instance.DeliveryPlaces.DirectGeocoder;
                if (geocoder is GoogleGeocoder googleGeocoder)
                    googleGeocoder.RefreshToken();

            _textActionButton = FindViewById<ImageView>(Resource.Id.microphone_button);
            _textActionButton.Click += (sender, args) =>
            {
                if (IsClearAction)
                {
                    _editQuery.Text = "";
                }
                else
                {
                    DisplaySpeechRecognizer();
                }
            };

            _recyclerView = FindViewById<RecyclerView>(Resource.Id.recycler_view);
            _recyclerView.SetLayoutManager(new LinearLayoutManager(this));

            if (_processType.IsDeliveryOrCatering())
            {
                _recyclerView.AddItemDecoration(new DividerItemDecorator(ContextCompat.GetDrawable(this, Resource.Drawable.divider_line_shape)));
                var adapter = new SearchAdapter<Place>(this, new List<Place>());
                adapter.ItemClick += ItemClick;
                _adapter = adapter;
            }
            else
            {
                _outlets = DigitalOrderingModule.Instance.GetZonesByProcessType<DOOutlet>(_processType);
                var adapter = new SearchAdapter<DOOutlet>(this, _outlets);
                adapter.ItemClick += (outlet) =>
                {
                    var intent = new Intent();
                    intent.PutExtra(ExtraOutletId, outlet.Id);
                    SuccessFinish(intent);
                };
                _adapter = adapter;
            }

            _recyclerView.SetAdapter(_adapter as RecyclerView.Adapter);
            _adapter.Update(string.Empty);

            Window.DecorView.SystemUiVisibility = (StatusBarVisibility)SystemUiFlags.LightStatusBar; // set status text dark
        }

        private async void ItemClick(Place place)
        {
            if (!_selectPlacePreventer.CanUse())
                return;
            
            await DigitalOrderingModule.Instance.DeliveryPlaces.SetPlaceCoordinatesByGoogleId(place);

            var intent = new Intent();
            intent.PutExtra(ExtraPlace, new PlaceParcable(place));
            SuccessFinish(intent);
        }

        // Create an intent that can start the Speech Recognizer activity
        private void DisplaySpeechRecognizer()
        {
            Intent intent = new Intent(RecognizerIntent.ActionRecognizeSpeech);
            intent.PutExtra(RecognizerIntent.ExtraLanguageModel, RecognizerIntent.LanguageModelFreeForm);
            // Start the activity, the intent will be populated with the speech text
            StartActivityForResult(intent, SpeechRequestCode);
        }

        protected override void OnActivityResult(int requestCode, Result resultCode, Intent data)
        {
            if (requestCode == SpeechRequestCode && resultCode == Result.Ok)
            {
                var results = data.GetStringArrayListExtra(RecognizerIntent.ExtraResults);
                
                if (_processType.IsDeliveryOrCatering())
                {
                    _editQuery.Text = results[0];
                }
                else
                {
                    var result = DigitalOrderingModule.Instance.FindOutletBySuggestions(results, _outlets);
                    if (result.Count == 1)
                    {
                        var intent = new Intent();
                        intent.PutExtra(ExtraOutletId, result[0].Id);
                        SuccessFinish(intent);
                    }
                    else
                    {
                        _editQuery.Text = results[0];
                    }
                }
            }
            base.OnActivityResult(requestCode, resultCode, data);
        }

        public void OnGlobalLayout()
        {
            RevealActivity(_revealX, _revealY);
            _rootLayout.ViewTreeObserver.RemoveOnGlobalLayoutListener(this);
        }

        private void RevealActivity(int x, int y)
        {
            // create the animator for this view (the start radius is zero)
            Animator circularReveal = ViewAnimationUtils.CreateCircularReveal(_rootLayout, x, y, 0, GetFinalRadius());
            circularReveal.SetDuration(400);
            circularReveal.SetInterpolator(new AccelerateInterpolator());

            // make the view visible and start the animation
            _rootLayout.Visibility = ViewStates.Visible;
            circularReveal.Start();
        }

        private float GetFinalRadius()
        {
            return (float)(Math.Max(_rootLayout.Width, _rootLayout.Height) * 1.1);
        }

        public void SuccessFinish(Intent intent)
        {
            SetResult(Result.Ok, intent);
            UnRevealActivity(_revealX, _revealY);
        }

        public override void OnBackPressed()
        {
            SetResult(Result.Canceled);
            UnRevealActivity(_revealX, _revealY);
        }

        private void UnRevealActivity(int x, int y)
        {
            // create the animator for this view (the start radius is zero)
            Animator circularReveal = ViewAnimationUtils.CreateCircularReveal(_rootLayout, x, y, GetFinalRadius(), 0);
            circularReveal.SetDuration(400);
            circularReveal.SetInterpolator(new AccelerateInterpolator());

            // make the view visible and start the animation
               
            circularReveal.AnimationEnd += (sender, args) =>
            {
                _rootLayout.Visibility = ViewStates.Invisible;
                Finish();
            };
            circularReveal.Start();
        }
    }

    public class DividerItemDecorator : RecyclerView.ItemDecoration
    {
        private readonly Drawable _drawable;

        public DividerItemDecorator(Drawable drawable)
        {
            _drawable = drawable;
        }

        public override void OnDraw(Canvas canvas, RecyclerView parent, RecyclerView.State state)
        {
            int dividerLeft = parent.PaddingLeft;
            int dividerRight = parent.Width - parent.PaddingRight;

            int childCount = parent.ChildCount;
            var adapter = parent.GetAdapter() as SearchAdapter<Place>;
            
            for (int i = 0; i < childCount; i++)
            {
                View child = parent.GetChildAt(i);

                RecyclerView.LayoutParams p = (RecyclerView.LayoutParams) child.LayoutParameters;

                if (i > 0 && i + 1 < childCount && !adapter.GetItem(i + 1).IsTemporary && adapter.GetItem(i).IsTemporary)
                {
                    int dividerTop = child.Bottom + p.BottomMargin;
                    int dividerBottom = dividerTop + _drawable.IntrinsicHeight;

                    _drawable.SetBounds(dividerLeft, dividerTop, dividerRight, dividerBottom);
                    _drawable.Draw(canvas);
                    break;
                }
            }
        }
    }

    public class PlaceParcable : Java.Lang.Object, IParcelable
    {
        public Place Place { get; }

        [ExportField("CREATOR")]
        public static PlaceParcableCreator InititalizeCreator()
        {
            return new PlaceParcableCreator();
        }

        public PlaceParcable(Place place)
        {
            Place = place;
        }

        public int DescribeContents()
        {
            return 0;
        }

        public void WriteToParcel(Parcel dest, ParcelableWriteFlags flags)
        {
            dest.WriteString(Place.Address);
            dest.WriteString(Place.MainAddress);
            dest.WriteString(Place.Zip);
            dest.WriteString(Place.Country);
            dest.WriteString(Place.City);
            dest.WriteDouble(Place.Latitude);
            dest.WriteDouble(Place.Longitude); 
            dest.WriteString(Place.StreetNumber);
            dest.WriteString(Place.Street);
            dest.WriteString(Place.State);
        }

        public class PlaceParcableCreator : Java.Lang.Object, IParcelableCreator
        {
            public Java.Lang.Object CreateFromParcel(Parcel source)
            {
                var place = new Place
                {
                    Address = source.ReadString(),
                    MainAddress = source.ReadString(),
                    Zip = source.ReadString(),
                    Country = source.ReadString(),
                    City = source.ReadString(),
                    Latitude = source.ReadDouble(),
                    Longitude = source.ReadDouble(),
                    StreetNumber = source.ReadString(),
                    Street = source.ReadString(),
                    State = source.ReadString()
                };
                return new PlaceParcable(place);
            }

            public Java.Lang.Object[] NewArray(int size)
            {
                return new Java.Lang.Object[size];
            }
        }
    }

    interface ISearchAdapter
    {
        void Update(string query);
    }

    public class SearchAdapter<T> : RecyclerView.Adapter, ISearchAdapter
    {
        private IList<T> _filteredItems;
        public Action<T> ItemClick;
        private readonly Activity _activity;
        private List<T> _items;
        private bool _hasRecentItems;

        public class ViewHolder : RecyclerView.ViewHolder
        {
            // each data item is just a string in this case
            public TextView FirstLineAddress { get; }
            public TextView SecondLineAddress { get; }
            public TextView Icon { get; }

            public ViewHolder(View itemView, Action<int> clickAction) : base(itemView)
            {
                FirstLineAddress = itemView.FindViewById<TextView>(Resource.Id.address_first_line);
                SecondLineAddress = itemView.FindViewById<TextView>(Resource.Id.address_second_line);
                Icon = itemView.FindViewById<TextView>(Resource.Id.icon_place);
                ItemView.Click += (sender, args) =>
                {
                    clickAction.Invoke(AdapterPosition);
                };
            }
        }
        
        public class HintViewHolder : RecyclerView.ViewHolder
        {
            public HintViewHolder(View itemView) : base(itemView)
            {
                var padding16 = itemView.Context.Resources.GetDimensionPixelSize(Resource.Dimension.material_horizontal_margin);
                var textView = (itemView as TextView);
                textView.Text = I18N.RECENT_SEARCH.ToUpper();
                textView.SetTextColor(itemView.Context.GetColorEx(Resource.Color.BaseHintTextColor));
                textView.SetPadding(padding16, textView.PaddingTop, textView.PaddingRight, textView.PaddingBottom);
            }
        }

        public override int GetItemViewType(int position)
        {
            return _hasRecentItems && position == 0 ? 1 : 0;
        }

        public SearchAdapter(Activity activity, List<T> items)
        {
            _activity = activity;
            _items = items;
            _filteredItems = new List<T>();
        }

        public void Update(string query)
        {
            if (typeof(T) == typeof(DOOutlet))
            {
                _filteredItems = DigitalOrderingModule.Instance.SearchByOutlets(query, _items.Cast<DOOutlet>().ToList())
                    .Cast<T>().ToList();
                NotifyDataSetChanged();

            }
            else if (typeof(T) == typeof(Place))
            {
                DigitalOrderingModule.Instance.DeliveryPlaces.GetLocationSearchResults(query, 
                    (s, places) =>
                {
                    _activity.RunOnUiThread(() =>
                    {
                        _filteredItems = places.Cast<T>().ToList();
                        _hasRecentItems = places.Any(p => p.IsTemporary);
                        NotifyDataSetChanged();
                    });
                });
            }
        }

        public override RecyclerView.ViewHolder OnCreateViewHolder(ViewGroup parent, int viewType)
        {
            if (viewType == 0)
            {
                var v = LayoutInflater.From(parent.Context).Inflate(Resource.Layout.do_address_list_element, parent, false);
                return new ViewHolder(v, OnClick);
            }
            else
            {
                var v = LayoutInflater.From(parent.Context).Inflate(Resource.Layout.form_hint_text, parent, false);
                return new HintViewHolder(v);
            }
        }

        public override void OnBindViewHolder(RecyclerView.ViewHolder holder, int position)
        {
            var vh = holder as ViewHolder;
            if (typeof(T) == typeof(DOOutlet))
            {
                var outlet = _filteredItems[position] as DOOutlet;
                vh.FirstLineAddress.Text = outlet.Name;
                vh.SecondLineAddress.Text = outlet.Address;
            }
            else if (typeof(T) == typeof(Place))
            {
                if (GetItemViewType(position) == 0)
                {
                    var place = GetItem(position) as Place;
                    vh.Icon.Visibility = place.IsTemporary ? ViewStates.Visible : ViewStates.Gone;
                    vh.SecondLineAddress.Visibility = place.IsTemporary ? ViewStates.Gone : ViewStates.Visible;
                    vh.FirstLineAddress.Text = place.MainAddress;
                    vh.SecondLineAddress.Text = place.SecondAddress;
                }   
            }
        }

        public T GetItem(int position)
        {
            return _filteredItems[position - (_hasRecentItems ? 1 : 0)];
        }

        public override int ItemCount => _filteredItems.Count + (_hasRecentItems ? 1 : 0);

        private void OnClick(int adapterPosition)
        {
            ItemClick?.Invoke(GetItem(adapterPosition));
        }
    }
}