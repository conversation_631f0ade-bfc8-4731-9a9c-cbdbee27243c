using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Content.Model.Customization;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.Model.Cart;
using LoyaltyPlant.DigitalOrdering.Model.Menu.Items;
using LoyaltyPlantApp.Droid.Base.Adapters;
using LoyaltyPlantApp.Droid.Images;
using LoyaltyPlantApp.Droid.Main.MainScreen.CardContainers;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.DigitalOrdering
{
    class UpsellCartMenuAdapter : UniversalRecyclerAdapter<CartMenuItem>
    {
        const int ItemLayoutId = Resource.Layout.do_cart_upsell_item_element;

        public event EventHandler<MenuPosition> ItemClick;

        public UpsellCartMenuAdapter()
        {
            HasStableIds = false;
        }

        protected override IList<AdapterItem> GetAdapterItemList()
        {
            var adapterItemsList = new List<AdapterItem>();
            var upSellItems = DigitalOrderingModule.Instance.GetUpsellItemsList();

            if (!upSellItems.Any())
                return adapterItemsList;

            foreach (var dataItem in upSellItems)
            {
                var adapterItem = new AdapterItem(ItemLayoutId, dataItem.Id.GetHashCode());
                adapterItem.DataItem = dataItem;
                adapterItemsList.Add(adapterItem);
            }

            return adapterItemsList;
        }

        protected override void OnSetupLayout(View rootView, AdapterViewHolder vh, int layoutId)
        {
            rootView.Click += (sender, args) =>
            {
                var element = Items[vh.AdapterPosition];
                ItemClick?.Invoke(this, element.DataItem.MenuItem as MenuPosition);
            };

        }

        protected override void ProceedWithItem(AdapterViewHolder viewHolder, AdapterItem adapterItem)
        {
            var itemImage = viewHolder.FindViewById<ImageView>(Resource.Id.item_image);
            var priceItem = viewHolder.FindViewById<TextView>(Resource.Id.item_price);
            var itemTitle = viewHolder.FindViewById<TextView>(Resource.Id.item_title);

            var dataItem = adapterItem.DataItem;
            if (dataItem.Image != null)
            {
                var androidImageLoader = AndroidImageLoader.Get(dataItem.Image, itemImage);
                androidImageLoader.CenterCrop = true;
                androidImageLoader.Animate = false;
                androidImageLoader.Show();
                itemImage.Visibility = ViewStates.Visible;
            }

            itemTitle.Text = dataItem.Label;

            priceItem.Text = dataItem.MenuItem.FormattedPrice;
            priceItem.SetBackground(CardViewContainer.GetPriceLabelBackground(priceItem.Context, ParentSectionCustomization.LabelPositions.BottomLeft));

        }

        protected override Task<IList<AdapterItem>> GetAdapterItemListAsync()
        {
            //TODO Need async implement
            throw new NotImplementedException();
        }
    }
}
