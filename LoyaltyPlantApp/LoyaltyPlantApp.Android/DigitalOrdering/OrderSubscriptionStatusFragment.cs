using System.Linq;
using Android.Graphics.Drawables;
using Android.OS;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base.Screens;
using LoyaltyPlantApp.Droid.Customviews;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.DigitalOrdering
{
    public class OrderSubscriptionStatusFragment : RootScreenFragment
	{
		private CommonAction _orderSubStatusScreen;

		public override ApplicationScreenType ScreenType => ApplicationScreenType.DoSubscriptionStatusScreen;
		public override int StatusBarColor => Resource.Color.BaseTintDarkPrimaryColor;
		protected override int LayoutResource => Resource.Layout.fragment_do_subscription_success;

		public static OrderSubscriptionStatusFragment NewInstance(Bundle fragmentArguments)
		{
			var frag = new OrderSubscriptionStatusFragment { Arguments = fragmentArguments };
			return frag;
		}

		protected override void SetupAction(BundleAction bundleAction)
		{
			_orderSubStatusScreen = new CommonAction(bundleAction);
		}

		protected override void SetupView(View rootView)
		{
			var order = DigitalOrderingModule.Instance.Orders.FirstOrDefault(o => o.OrderId == _orderSubStatusScreen.Id);
			if (order == null)
			{
				Close();
				return;
			}
			
			var buttonsContainer = rootView.FindViewById(Resource.Id.buttons_container);
			buttonsContainer.Background = BottomButton.GetGradientBackground(Context);

			var statusHeader = rootView.FindViewById<TextView>(Resource.Id.sub_status_header);
			statusHeader.SetTextAndVisibility(I18N.DO_SUCCESS_FASTORDER_HEADER);

			var customText = rootView.FindViewById<TextView>(Resource.Id.sub_status_description);
			customText.Text = I18N.DO_SUCCESS_FASTORDER_DESCRIPTION;

			var bottomButton = rootView.FindViewById<BottomButton>(Resource.Id.bottom_panel);
			
			if (order.IsApprovable())
			{
				bottomButton.SetActionButtonClick((sender, e) =>
				{
					DigitalOrderingModule.Instance.Controller.ShowMainScreen();
				});

				var textsContainer = rootView.FindViewById(Resource.Id.texts_container);
				textsContainer.Visibility = ViewStates.Gone;
			}
			else
			{
				bottomButton.SetActionButtonClick((sender, args) =>
				{
					DigitalOrderingModule.Instance.Controller.ShowMainScreen();
				});

				var header = rootView.FindViewById(Resource.Id.header_container);
				header.LayoutParameters.Height = Activity.GetScreenHeightPx() / 2;
			}

			LpLogger.LOG_I("OrderSubscriptionStatusFragment was opened");
		}
	}
}