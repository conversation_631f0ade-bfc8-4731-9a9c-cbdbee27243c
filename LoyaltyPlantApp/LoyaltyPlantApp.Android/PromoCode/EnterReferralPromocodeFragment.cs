using System;
using Android.OS;
using Android.Text;
using Android.Text.Method;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.Utils;
using LoyaltyPlant.PromoCode;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Base.ViewHelpers;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.PromoCode
{
    public class EnterReferralPromocodeFragment : EnterPromocodeFragment
    {
        public override ApplicationScreenType ScreenType => ApplicationScreenType.PromocodeScreen;

        public static new EnterReferralPromocodeFragment NewInstance()
        {
            var action = new CommonAction()
            {
                Description = I18N.PROMOCODE_UPDATED_SUBTITLE
            };
            var bundle = new Bundle();
            bundle.PutActionBundle(action);
            return new EnterReferralPromocodeFragment
            {
                Arguments = bundle,
            };
        }

        protected override bool BackButton => false;

        protected override void SetupView(View rootView)
        {
            base.SetupView(rootView);

            _dontHavePromocodeButton.SetTextAndVisibility(I18N.PROMOCODE_DONT_HAVE_BUTTON);
            _dontHavePromocodeButton.Click += (sender, args) =>
            {
                PromoCodeModule.Instance.Controller.OnClickedNoPromocode();
            };

            _logoImageView.Visibility = ViewStates.Visible;

            ((ViewGroup.MarginLayoutParams)_logoImageView.LayoutParameters).TopMargin = 0;

            BottomButton.ActionText.Text = I18N.SUBMIT;
        }

        protected override void BottomButtonClick(object sender, EventArgs e)
        {
            _ = PromoCodeModule.Instance.Controller.OnEnterReferralPromocode(Code);
        }
    }
}
