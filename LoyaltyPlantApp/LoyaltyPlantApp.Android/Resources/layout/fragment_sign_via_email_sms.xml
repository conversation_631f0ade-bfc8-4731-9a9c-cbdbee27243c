<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/SecondaryBackgroundColor"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:theme="@style/LpOverlayActionBar" />

    </com.google.android.material.appbar.AppBarLayout>

    <loyaltyplantapp.droid.customviews.StyledTextView
        style="@style/subtitle_1"
        app:i18n_text="SOCIAL_LOGIN_HEADER"
        android:gravity="center"
        tools:text="Sign in via email phone"
        android:padding="@dimen/material_horizontal_margin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_width="match_parent"
        android:layout_height="0dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/answers_input_wrapper"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="@dimen/material_horizontal_margin"
            app:errorEnabled="true"
            android:layout_marginEnd="@dimen/material_horizontal_margin"
            android:theme="@style/LpTextInputLayout">

            <loyaltyplantapp.droid.customviews.StyledEditText
                android:id="@+id/answers_input"
                style="@style/body_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="16dp"
                android:maxLines="1"
                tools:text="grtgrtggtrg">

                <requestFocus/>

            </loyaltyplantapp.droid.customviews.StyledEditText>

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

    <loyaltyplantapp.droid.customviews.BottomButton
        android:id="@+id/bottom_panel"
        android:animateLayoutChanges="true"
        android:layout_width="match_parent"
        app:i18n_action_text="CONTINUE"
        android:layout_height="@dimen/bottom_button_container_height"
        app:visibility_price="false" />


</LinearLayout>