<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:paddingTop="32dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/address_field"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/material_horizontal_margin"
        android:layout_marginEnd="@dimen/material_horizontal_margin"
        android:paddingBottom="@dimen/base_margin_eight">

        <loyaltyplantapp.droid.customviews.StyledTextView
            style="@style/subtitle_1_medium"
            app:i18n_text="ADDRESS"
            android:lines="1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_margin_eight"
            tools:text="Delivery time" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/address_change_field">

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/address_text"
                style="@style/subtitle_1_medium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="6dp"
                android:paddingBottom="@dimen/base_margin_eight"
                tools:text="From 2:00 PM to 3:00 PM"
                android:layout_weight="1"
                android:paddingStart="@dimen/zero_dp"
                android:paddingEnd="@dimen/zero_dp"
                android:background="@drawable/edit_text_background"/>

            <ImageView
                android:id="@+id/address_change_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center"
                android:src="@drawable/location_arrow"
                android:layout_marginStart="@dimen/material_horizontal_margin"
                android:visibility="gone" />

        </LinearLayout>
    </LinearLayout>

    <include android:id="@+id/address_details_input"
             layout="@layout/form_text_input_item"/>

    <LinearLayout
        android:id="@+id/time_input_selector"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/material_horizontal_margin"
        android:layout_marginEnd="@dimen/material_horizontal_margin"
        android:layout_marginBottom="18dp"
        >
        <loyaltyplantapp.droid.customviews.StyledTextView
            style="@style/subtitle_1_medium"
            android:id="@+id/time_input_title"
            android:textAlignment="viewStart"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/base_margin_eight"
            tools:text="title" />

        <android.support.v7.widget.AppCompatSpinner
            android:id="@+id/spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textDirection="locale"
            android:textAlignment="viewStart"
            android:paddingStart="@dimen/zero_dp"
            android:paddingEnd="@dimen/zero_dp"
            android:minHeight="@dimen/min_ada_height"
            android:popupBackground="@color/SecondaryBackgroundColor"
            android:layout_gravity="center_vertical"
            android:background="@drawable/edit_text_background"
            android:theme="@style/StyledEditTextTheme"/>
    </LinearLayout>
    
    <LinearLayout
        android:id="@+id/pickers_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/min_ada_height"
        android:layout_marginStart="@dimen/material_horizontal_margin"
        android:layout_marginEnd="@dimen/material_horizontal_margin"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatSpinner
            android:id="@+id/date_picker"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:paddingStart="@dimen/zero_dp"
            android:popupBackground="@color/SecondaryBackgroundColor"
            android:background="@drawable/edit_text_background"
            android:theme="@style/StyledEditTextTheme" />

        <androidx.appcompat.widget.AppCompatSpinner
            android:id="@+id/time_picker"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:paddingStart="@dimen/zero_dp"
            android:layout_marginStart="@dimen/base_margin_four"
            android:popupBackground="@color/SecondaryBackgroundColor"
            android:background="@drawable/edit_text_background"
            android:theme="@style/StyledEditTextTheme" />

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/time_input"
            style="@style/subtitle_18_medium"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAlignment="viewStart"
            android:layout_marginStart="@dimen/base_margin_four"
            android:background="@drawable/edit_text_background"
            tools:text="gteqwq2"
            android:lines="1"
            android:paddingBottom="@dimen/base_margin_eight"
            android:gravity="center_vertical"/>
        <!--<item name="background">@drawable/spinner_textfield_background_material</item>-->
    </LinearLayout>

</LinearLayout>
