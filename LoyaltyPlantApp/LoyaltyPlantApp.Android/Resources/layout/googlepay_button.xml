<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_margin="16dp"
    android:layout_gravity="bottom"
    android:layout_height="48sp"
    android:background="@drawable/googlepay_button_background"
    android:paddingTop="2dp"
    android:contentDescription="@string/googlepay_button_content_description">
    <LinearLayout
        android:duplicateParentState="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:weightSum="2"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/google_pay_content"
        	android:contentDescription="@string/text_content_description"
        	android:paddingHorizontal="24sp"
        	android:paddingVertical="11sp"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            android:duplicateParentState="true"
            android:src="@drawable/buy_with_googlepay_button_content"/>
    </LinearLayout>
    <ImageView
    	android:contentDescription="@string/overlay_content_description"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        android:duplicateParentState="true"
        android:src="@drawable/googlepay_button_overlay"/>
</LinearLayout>
