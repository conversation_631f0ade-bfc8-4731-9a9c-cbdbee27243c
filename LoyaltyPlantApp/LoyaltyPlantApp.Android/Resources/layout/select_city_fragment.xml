<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/gradient_secondary_background"
    android:animateLayoutChanges="true"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:fitsSystemWindows="@bool/fits_system_view_21_true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:theme="@style/LpOverlayActionBar"
            android:fitsSystemWindows="@bool/fits_system_view_21_true"
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/min_ada_height"
            />

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:id="@+id/suggest_city"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <loyaltyplantapp.droid.customviews.StyledTextView
            style="@style/subtitle_1"
            app:i18n_text="SELECT_CITY_SUGGEST"
            tools:text="Are you in this city?"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:paddingTop="16dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/suggest_city_name"
            style="@style/h5_headline"
            tools:text="Phoenix"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:paddingTop="12dp"
            android:paddingBottom="14dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="14dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.legacy.widget.Space
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/yes_this_city"
                style="@style/button"
                android:textColor="@color/AccentColor"
                app:i18n_text="YES"
                android:textAllCaps="true"
                tools:text="yes"
                android:minWidth="64dp"
                android:padding="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="36dp" />

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/no_another_city"
                android:textColor="@color/AccentColor"
                style="@style/button"
                app:i18n_text="NO_ANOTHER_CITY"
                android:textAllCaps="true"
                tools:text="No, another city"
                android:minWidth="64dp"
                android:gravity="center"
                android:padding="8dp"
                android:layout_width="wrap_content"
                android:layout_height="36dp" />

        </LinearLayout>

        <View style="@style/HorizontalLine" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/cities_list"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none" />

</LinearLayout>