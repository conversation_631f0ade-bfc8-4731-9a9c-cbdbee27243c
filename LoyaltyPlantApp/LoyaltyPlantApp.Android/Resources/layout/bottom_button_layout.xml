<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/block_text"
        android:layout_alignParentBottom="true"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_button_height"
        android:minHeight="@dimen/min_ada_height"
        android:gravity="center"
        android:textColor="@color/AccentColor"
        style="@style/body_2"
        tools:text="getger wgrgtrt gr2"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/action_button"
        android:minHeight="@dimen/min_ada_height"
        android:layout_alignParentBottom="true"
        android:layoutDirection="ltr"
        style="@style/LoyaltyPlantCardView.Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_button_height"
        android:layout_margin="16dp"
        app:cardBackgroundColor="@color/AccentColor">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:foreground="?attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:baselineAligned="false">

                <loyaltyplantapp.droid.customviews.StyledTextView
                    android:id="@+id/action_text"
                    style="@style/button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:lines="1"
                    android:layout_weight="1"
                    tools:text="Blah" />

				<loyaltyplantapp.droid.customviews.StyledTextView
					android:id="@+id/old_price_text"
					style="@style/button"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginStart="4dp"
					android:layout_marginEnd="4dp"
					tools:text="1234"
					tools:visibility="visible"
					android:importantForAccessibility="no"
					android:visibility="gone"/>

                <loyaltyplantapp.droid.customviews.StyledTextView
                    android:id="@+id/price_text"
                    style="@style/button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:layoutDirection="ltr"
                    android:gravity="end"
                    android:lines="1"
                    tools:text="1 000. uhy"
                    app:textAllCaps="false"/>

                <loyaltyplantapp.droid.customviews.StyledCoinView
                    android:id="@+id/price_coin"
                    style="@style/button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"/>

                <ImageView
                    android:id="@+id/tiered_loyalty_lock"
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:src="@drawable/tiered_loyalty_lock"
                    android:visibility="gone"
                    tools:visibility="visible"/>

            </LinearLayout>
        </FrameLayout>
    </androidx.cardview.widget.CardView>

    <View
        android:id="@+id/rectangle_animation"
        android:layout_alignParentBottom="true"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/bottom_button_height"
        android:layout_margin="16dp"
        app:cardBackgroundColor="@color/AccentColor" />
</merge>
