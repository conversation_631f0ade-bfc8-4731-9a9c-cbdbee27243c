<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/subsection_card_itself"
    android:layout_width="match_parent"
    android:layout_height="@dimen/subsection_height"
    app:cardBackgroundColor="@color/BackgroundColor"
    style="@style/LoyaltyPlantCardView">

  <FrameLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:foreground="?attr/selectableItemBackground">

    <ImageView
        android:id="@+id/subsection_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:src="@android:drawable/ic_delete"
        android:scaleType="centerCrop" />

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/subsection_text"
        style="@style/body_2"
        android:textColor="@color/MainScreenSecondaryDelimitersTextColor"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:lines="1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        tools:text="getwgtwgrtwgrtw"
        tools:background="@color/MainScreenSecondaryDelimitersColor" />

  </FrameLayout>

</androidx.cardview.widget.CardView>