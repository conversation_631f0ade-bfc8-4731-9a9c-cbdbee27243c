<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView android:id="@+id/card_view"
    style="@style/LoyaltyPlantCardView"
    app:cardElevation="@dimen/zero_dp"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:foreground="?attr/selectableItemBackground">

		<loyaltyplantapp.droid.customviews.ImageRoundedView
			android:id="@+id/slide_image"
			android:scaleType="centerCrop"
			android:layout_width="match_parent"
			android:layout_height="match_parent" />

		<RelativeLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:id="@+id/tl_white_background"
			android:background="@color/transparent_white_color"
			android:visibility="gone"
			tools:visibility="visible">
			<ImageView
				android:layout_width="18dp"
				android:layout_height="18dp"
				android:src="@drawable/tiered_loyalty_lock"
				android:layout_alignParentBottom="true"
				android:layout_alignParentEnd="true"
				android:layout_margin="@dimen/base_margin_four"/>
		</RelativeLayout>
                         
        <LinearLayout
            android:id="@+id/card_price_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            tools:background="@color/PriceLabelColor"
            android:gravity="center_vertical|start"
            android:layout_gravity="bottom|start"
            tools:visibility="visible"
            android:visibility="gone">

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/card_price"
                style="@style/small_text"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/label_badge_height"
                android:gravity="center_vertical"
                android:paddingStart="@dimen/base_margin_eight"
                android:paddingEnd="@dimen/base_margin_four"
                android:textColor="@color/PriceLabelTextColor"
                android:lines="1"
                tools:text="100" />

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/price_coin"
                style="@style/small_text_price"
                android:paddingEnd="@dimen/base_margin_eight"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layoutDirection="ltr"
                android:visibility="gone"
                android:textColor="@color/PriceLabelTextColor"
                tools:visibility="visible"
                android:lines="1"
                tools:text="$"
                app:textAllCaps="false" />

			<loyaltyplantapp.droid.customviews.StyledTextView
				android:id="@+id/money_coin"
				style="@style/small_text"
				android:layout_width="wrap_content"
				android:layout_height="wrap_content"
				android:gravity="center_vertical"
				android:layoutDirection="ltr"
				android:visibility="gone"
				android:lines="1"
                android:textColor="@color/PriceLabelTextColor"
				tools:text="+ 1 000. uhy"
				app:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/card_present_count_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginRight="12dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical|end"
            android:layout_gravity="bottom|end"
            tools:visibility="visible"
            android:visibility="gone">

            <ImageView
                android:src="@drawable/subscription_icon"
                android:layout_width="28dp"
                android:layout_height="39dp"/>

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/card_present_count"
                style="@style/body_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textColor="@color/PriceLabelTextColor"
                android:textSize="20sp"
                android:lines="1"
                tools:text="x10" />
        </LinearLayout>

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/period_badge_offer"
            style="@style/caption"
            android:textColor="@color/InfoLabelTextColor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/label_badge_height"
            android:lines="1"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:gravity="center"
            android:layout_gravity="end|top"
            tools:text="Today 2:30 PM"
            tools:visibility="visible"
            android:visibility="gone" />

		<loyaltyplantapp.droid.customviews.StyledTextView
			android:id="@+id/period_present_text"
			style="@style/small_text"
			android:textColor="@color/InfoLabelTextColor"
			android:layout_width="wrap_content"
			android:layout_height="@dimen/label_badge_height"
			android:layout_gravity="start|top"
            android:gravity="center_vertical"
			android:background="@color/NavigationDrawerAccentColor"
			android:paddingStart="@dimen/base_margin_eight"
			android:paddingEnd="10dp"
			tools:text="21.09"
			android:visibility="gone"
			tools:visibility="visible"/>
    </FrameLayout>

</androidx.cardview.widget.CardView>