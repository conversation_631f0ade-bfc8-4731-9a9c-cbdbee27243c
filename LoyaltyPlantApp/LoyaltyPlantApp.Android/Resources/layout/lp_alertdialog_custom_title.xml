<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/MessageboxAccentTextColor"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/date_picker_year_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        style="@style/body_2"
        android:paddingTop="14dp"
        android:paddingBottom="8dp"
        android:paddingStart="16dp"
        tools:text="gtgwrw g2g2 gf42"/>

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/date_picker_month_day_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="16dp"
        android:paddingBottom="16dp"
        android:textColor="@android:color/white"
        style="@style/h4_headline"
        tools:text="gtgwrw g2g2 gf42"/>

</LinearLayout>