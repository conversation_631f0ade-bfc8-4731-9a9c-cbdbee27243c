<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:foreground="?attr/selectableItemBackground">

    <ImageView
        android:id="@+id/subsection_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone"
        tools:src="@android:drawable/ic_delete" />

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/subsection_text"
        style="@style/body_2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:lines="1"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:textColor="@color/MainScreenSecondaryDelimitersTextColor"
        tools:background="@color/MainScreenSecondaryDelimitersColor"
        tools:text="getwgtwgrtwgrtw" />

</FrameLayout>
