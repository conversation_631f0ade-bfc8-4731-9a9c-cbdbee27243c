<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="16dp">

    <loyaltyplantapp.droid.customviews.StyledTextView
        style="@style/h6_headline"
        android:paddingBottom="24dp"
        android:paddingStart="@dimen/material_horizontal_margin"
        android:paddingEnd="@dimen/material_horizontal_margin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:i18n_text="PRESENT_HINT_TITLE"
        tools:text="Как получить подарок за баллы?" />

    <LinearLayout
        android:id="@+id/in_store_part"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/material_horizontal_margin"
            android:paddingEnd="@dimen/material_horizontal_margin"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/icon_fonts"
                android:gravity="center"
                android:text="&quot;"
                android:textColor="@color/DefaultTextColor"
                android:textSize="@dimen/h5_text_size" />

            <loyaltyplantapp.droid.customviews.StyledTextView
                style="@style/h6_headline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:i18n_text="PRESENT_HINT_PRECEPT_HEADER"
                tools:text="Если вы в заведении" />

        </LinearLayout>

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/in_store_hint_description"
            style="@style/subtitle_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/material_horizontal_margin"
            android:layout_marginRight="@dimen/material_horizontal_margin"
            tools:text="1. Tell our staff member that you  want to get this reward.\n 2.Tap and hold on the button to show  a QR-code. This reward will disapper  from the app after that
" />

    </LinearLayout>

    <LinearLayout
        android:paddingTop="24dp"
        android:id="@+id/online_part"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/material_horizontal_margin"
            android:paddingEnd="@dimen/material_horizontal_margin"
            android:paddingBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:fontFamily="@font/icon_fonts"
                android:gravity="center"
                android:text="\\"
                android:textColor="@color/DefaultTextColor"
                android:textSize="@dimen/h5_text_size" />

            <loyaltyplantapp.droid.customviews.StyledTextView
                style="@style/h6_headline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:i18n_text="PRESENT_HINT_DO_PRECEPT_HEADER"
                tools:text="Если вы делаете онлайн заказ" />

        </LinearLayout>

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/online_hint_description"
            style="@style/subtitle_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/material_horizontal_margin"
            android:layout_marginRight="@dimen/material_horizontal_margin"
            tools:text="1. Tell our staff member that you  want to get this reward.\n 2.Tap and hold on the button to show  a QR-code. This reward will disapper  from the app after that
" />

    </LinearLayout>

    <loyaltyplantapp.droid.customviews.BottomButton
        android:id="@+id/bottom_panel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_button_container_height"
        android:background="@color/SecondaryBackgroundColor"
        android:theme="@style/SelectableItemTheme"
        app:gradient_background="false"
        app:i18n_action_text="PRESENT_HINT_GOT_IT"
        app:visibility_price="false" />

</LinearLayout>