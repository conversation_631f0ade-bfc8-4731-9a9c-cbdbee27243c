<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="@bool/fits_system_view_21_true"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.airbnb.lottie.LottieAnimationView
        android:fitsSystemWindows="@bool/fits_system_view_21_true"
        android:id="@+id/lottie_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:lottie_fileName="pointsAnimation.json"
        app:lottie_autoPlay="true" />

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="120dp">

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/text_points"
            android:textColor="@color/AccentTextColor"
            android:minLines="2"
            android:maxLines="4"
            style="@style/subtitle_1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/base_margin_twenty_four"
            tools:text="Поздравляем " />

        <LinearLayout
            android:id="@+id/points_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/points"
                style="@style/h2_headline"
                android:textColor="@color/AccentTextColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textDirection="ltr"
                tools:text="+10000" />

            <TextView
                android:textSize="@dimen/h2_text_size"
                app:fontFamily="@font/coin_font"
                android:text="@string/coin"
                android:textColor="@color/AccentTextColor"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>