<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/SecondaryBackgroundColor"
	android:orientation="vertical">

	<com.google.android.material.appbar.AppBarLayout
		android:layout_width="match_parent"
		android:layout_height="wrap_content">

		<androidx.appcompat.widget.Toolbar
			android:id="@+id/toolbar"
			android:layout_width="match_parent"
			android:layout_height="?actionBarSize"
			android:theme="@style/LpOverlayActionBar" />

	</com.google.android.material.appbar.AppBarLayout>

	<androidx.core.widget.NestedScrollView
		android:layout_width="match_parent"
		android:layout_height="@dimen/zero_dp"
		android:layout_weight="1"
		android:fillViewport="true">

		<LinearLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:orientation="vertical">
			<ImageView
				android:id="@+id/logo_image"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:src="@drawable/featured_graphic"
				android:adjustViewBounds="true"
				android:scaleType="centerCrop"/>

			<LinearLayout
				android:layout_width="match_parent"
				android:layout_height="@dimen/zero_dp"
				android:layout_weight="1"
				android:orientation="vertical"
				android:layout_marginTop="18dp">

				<loyaltyplantapp.droid.customviews.StyledTextView
					android:id="@+id/input_code_subtitle"
					style="@style/subtitle_1"
					android:layout_width="match_parent"
					android:layout_height="wrap_content"
					android:gravity="center"
					android:padding="@dimen/material_horizontal_margin"
					tools:text="Enter the promocode if you have one" />

				<FrameLayout
					android:id="@+id/edit_text_container"
					android:layout_width="match_parent"
					android:layout_height="match_parent">

					<LinearLayout
						android:layout_gravity="center"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						android:orientation="vertical">

						<FrameLayout
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:gravity="center_vertical"
							android:orientation="horizontal"
							android:theme="@style/LpTextInputLayout">

							<!--inputType set programmatically-->
							<loyaltyplantapp.droid.customviews.StyledEditText
								android:id="@+id/edit_text"
								android:paddingBottom="8dp"
								style="@style/h4_headline"
								android:imeOptions="actionDone"
								android:background="@android:color/transparent"
								android:paddingStart="@dimen/material_horizontal_margin"
								android:paddingEnd="@dimen/material_horizontal_margin"
								android:layout_width="wrap_content"
								android:layout_height="wrap_content"
								android:layout_gravity="center_horizontal"
								android:gravity="center"
								android:minWidth="100dp"
								tools:text="tr43" />

							<View
								android:layout_marginStart="@dimen/material_horizontal_margin"
								android:layout_marginEnd="@dimen/material_horizontal_margin"
								android:layout_gravity="bottom"
								android:layout_height="2dp"
								style="@style/HorizontalLine" />

						</FrameLayout>

						<loyaltyplantapp.droid.customviews.StyledTextView
							android:id="@+id/error_edit_text"
							style="@style/caption"
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:gravity="center"
							android:paddingBottom="2dp"
							android:textColor="@color/AccentColor"
							tools:text="error text kaakoy to" />

					</LinearLayout>
				</FrameLayout>
			</LinearLayout>

			<LinearLayout
				android:id="@+id/bottom_buttons_container"
				android:animateLayoutChanges="true"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical">

				<loyaltyplantapp.droid.customviews.BottomButton
					android:id="@+id/bottom_panel"
					android:layout_width="match_parent"
					android:layout_height="@dimen/bottom_button_container_height"
					app:visibility_price="false">

					<requestFocus/>
				</loyaltyplantapp.droid.customviews.BottomButton>

				<loyaltyplantapp.droid.customviews.StyledTextView
					android:id="@+id/second_bottom_button"
					style="@style/button"
					android:minHeight="@dimen/min_ada_height"
					android:layout_width="match_parent"
					android:layout_marginStart="@dimen/material_horizontal_margin"
					android:layout_marginEnd="@dimen/material_horizontal_margin"
					android:layout_height="wrap_content"
					android:layout_marginBottom="16dp"
					android:background="@drawable/rounded_bottom_button_border"
					android:gravity="center"
					android:visibility="gone"
					android:paddingBottom="10dp"
					android:paddingTop="10dp"
					android:textStyle="bold"
					android:textColor="@color/text_color_for_button"
					tools:visibility="visible"
					tools:text="resend code" />
			</LinearLayout>
		</LinearLayout>
	</androidx.core.widget.NestedScrollView>
</LinearLayout>