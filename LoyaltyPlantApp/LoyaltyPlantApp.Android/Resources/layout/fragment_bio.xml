<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_secondary_background">

    <androidx.core.widget.NestedScrollView
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:id="@+id/nested_scroll_view"
        android:layout_width="match_parent"
        android:layout_marginBottom="60dp"
        android:layout_height="wrap_content">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <loyaltyplantapp.droid.surveys.views.LpFieldsLayout
                android:id="@+id/fields_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="32dp"
                android:paddingBottom="@dimen/base_margin_sixteen"
                app:fields_type="bio" />

            <include layout="@layout/layout_privacy_policy_checkbox" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/LpOverlayActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="@null"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:titleEnabled="false">

            <FrameLayout
                app:layout_collapseMode="parallax"
                android:id="@+id/image_container"
                android:paddingTop="?actionBarSize"
                android:layout_width="match_parent"
                android:layout_height="32dp">

            </FrameLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/LpOverlayActionBarPopup"/>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!--anchored by custom behavior-->
    <loyaltyplantapp.droid.customviews.BottomButton
        android:id="@+id/bottom_panel"
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_button_container_height"
        android:layout_gravity="bottom"
        app:i18n_action_text="NEXT"
        app:visibility_price="false" />

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/bio_fragment_title"
        style="@style/h6_headline"
        android:layout_width="wrap_content"
        android:layout_height="?actionBarSize"
        android:elevation="@dimen/base_margin_eight"
        android:gravity="center_vertical|start"
        android:textColor="@color/PrimaryTextColor"
        app:i18n_text="NAVIGATION_USER_INFO"
        tools:text="Profile" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>