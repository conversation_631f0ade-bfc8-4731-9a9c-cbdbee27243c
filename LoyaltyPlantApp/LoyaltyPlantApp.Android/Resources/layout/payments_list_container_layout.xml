<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/dialog_title"
        style="@style/main_text_semibold"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:textColor="@color/BaseDefaultTextColor"
        android:paddingStart="@dimen/material_horizontal_margin"
        android:paddingEnd="@dimen/material_horizontal_margin"
        android:paddingTop="16dp"
        android:paddingBottom="8dp"
        tools:text="Payments" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_weight="1"
        android:layout_height="@dimen/zero_dp"
        android:orientation="vertical">

		<LinearLayout
			android:id="@+id/payments_list_container"
			android:orientation="vertical"
			android:layout_width="match_parent"
			android:layout_height="wrap_content">

			<LinearLayout
				android:id="@+id/payments_list_online"
				android:orientation="vertical"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"/>

			<LinearLayout
				android:id="@+id/payments_list_offline"
				android:orientation="vertical"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"/>
		</LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>