<?xml version="1.0" encoding="UTF-8" ?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/title_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/BaseDefaultTextColor"
            tools:text="label name text"
            style="@style/body_1"/>

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/description_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Description text of item "
            android:textColor="@color/BaseSecondaryTextColor"
            style="@style/caption"/>

    </LinearLayout>

</LinearLayout>