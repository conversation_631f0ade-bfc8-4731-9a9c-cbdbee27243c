<?xml version="1.0" encoding="UTF-8" ?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/image_item"
            android:gravity="center"
            android:layout_marginEnd="12dp"
            android:textSize="24dp"
            tools:visibility="gone"
            android:textColor="@color/BaseSecondaryTextColor"
            android:layout_width="40dp"
            android:layout_height="40dp" />

        <androidx.cardview.widget.CardView
            app:cardCornerRadius="24dp"
            android:layout_marginEnd="24dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/image"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:layout_width="48dp"
                android:layout_height="48dp" />

        </androidx.cardview.widget.CardView>


        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/title_item"
            style="@style/body_1"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingEnd="24dp"
            android:textColor="@color/BaseDefaultTextColor"
            android:layout_gravity="center_vertical"
            tools:text="label name text fgrtgrtg grtgrtg grtgrtgr grtgrtgr" />

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/extra_text"
            android:layout_gravity="center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="$ 4.79"
            android:textColor="@color/BaseSecondaryTextColor"
            style="@style/body_1"/>

    </LinearLayout>

    <View style="@style/HorizontalLine" />

</LinearLayout>