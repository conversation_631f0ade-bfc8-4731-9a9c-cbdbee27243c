<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinator_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:statusBarBackground="@null"
    android:fitsSystemWindows="@bool/fits_system_view_21_true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/app_bar_height"
        android:background="@android:color/transparent"
        android:fitsSystemWindows="@bool/fits_system_view_21_true"
        android:theme="@style/LpOverlayActionBar">
        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@null"
            app:statusBarScrim="@null"
            app:titleEnabled="false"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">
            <ImageView
                android:id="@+id/item_image"
                android:scaleType="centerCrop"
                android:transitionName="@string/do_position_transition_name"
                android:fitsSystemWindows="@bool/fits_system_view_21_true"
                app:layout_collapseMode="parallax"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize"
                app:layout_collapseMode="pin" />
            <!--android:background="@color/PrimaryColor"-->
            <View
                android:id="@+id/stub_status_bar"
                android:visibility="gone"
                app:layout_collapseMode="pin"
                android:fitsSystemWindows="@bool/fits_system_view_21_true"
                android:layout_width="match_parent"
                android:layout_height="18dp" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <loyaltyplantapp.droid.customviews.LpNestedScrollView
        android:id="@+id/nested_scroll_view"
        android:overScrollMode="never"
        android:clipToPadding="false"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        android:paddingBottom="@dimen/padding_above_bottom_button"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:id="@+id/description_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/material_horizontal_margin"
            android:orientation="vertical">
            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/item_name"
                style="@style/title_17_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginTop="16dp"
                tools:text="Lorem Ipsum" />
            <FrameLayout
                android:id="@+id/top_description_wrapper"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <loyaltyplantapp.droid.customviews.StyledTextView
                    android:id="@+id/top_description"
                    style="@style/small_text"
                    tools:text="Short description bla bla chutka tut napishem"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </FrameLayout>

            <loyaltyplantapp.droid.customviews.StyledTextView
                android:id="@+id/bottom_description"
                style="@style/small_text"
                tools:text="Long description bla bla mnogo tut napishem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="@dimen/base_margin_eight"/>
			<androidx.recyclerview.widget.RecyclerView
				android:id="@+id/stages_container"
                android:layout_marginStart="12dp"
				android:layout_width="match_parent"
				android:layout_height="wrap_content" />
            <LinearLayout
                android:id="@+id/modifiers_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/bottom_button_with_gradient"
                android:background="@android:color/transparent"/>
        </LinearLayout>
    </loyaltyplantapp.droid.customviews.LpNestedScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/SecondaryBackgroundColor"
        android:layout_gravity="bottom">

        <TextView
            android:id="@+id/availability_text_view"
            android:textColor="@color/BackgroundTextColor"
            android:textColorLink="@color/BackgroundTextColor"
            android:alpha="0.5"
            tools:text="Short description bla bla chutka tut napishem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/material_horizontal_margin"
            android:layout_marginRight="@dimen/material_horizontal_margin"
            android:textAlignment="center"
            android:visibility="gone"/>

        <!--anchored by custom behavior-->
        <loyaltyplantapp.droid.customviews.BottomCounterControlPanel
            android:id="@+id/bottom_panel"
            android:layout_width="match_parent"
            android:layout_height="@dimen/bottom_button_with_gradient" />

    </LinearLayout>

    
</androidx.coordinatorlayout.widget.CoordinatorLayout>
