<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingStart="10dp"
    android:paddingEnd="@dimen/material_horizontal_margin"
    android:orientation="horizontal"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/is_selected"
        tools:text="frqgetq"
        style="@style/body_2"
        android:paddingStart="8dp"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:gravity="center_vertical"
        android:minHeight="@dimen/min_ada_height"
        android:layout_height="wrap_content" />

    <loyaltyplantapp.droid.customviews.StyledTextView
        android:id="@+id/price"
        style="@style/body_2"
        android:textColor="@color/HintTextColor"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        tools:text="$3.00" />

</LinearLayout>