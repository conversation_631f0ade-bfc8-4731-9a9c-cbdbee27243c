<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/order_statuses_view"
    android:orientation="horizontal"
    android:paddingTop="16dp"
    android:paddingLeft="@dimen/special_delivery_view_horizontal_padding"
    android:paddingRight="@dimen/special_delivery_view_horizontal_padding">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_horizontal" >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/unconfirmed_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_gravity="center_horizontal"
                app:srcCompat="@drawable/unconfirmed_fill" />

            <View
                android:id="@+id/view_01"
                android:background="@color/medium_gray"
                android:layout_width="40dp"
                android:layout_height="2dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/cooking_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_gravity="center_horizontal"
                app:srcCompat="@drawable/cooking_unfill"
                android:layout_marginLeft="6dp" />

            <View
                android:id="@+id/view_12"
                android:background="@color/medium_gray"
                android:layout_width="40dp"
                android:layout_height="2dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/awaiting_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_gravity="center_horizontal"
                app:srcCompat="@drawable/awaiting_pickup_unfill"
                android:layout_marginLeft="6dp" />

            <View
                android:id="@+id/view_23"
                android:background="@color/medium_gray"
                android:layout_width="40dp"
                android:layout_height="2dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp" />

             <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/completed_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_gravity="center_horizontal"
                app:srcCompat="@drawable/completed_unfill" />
        
        </LinearLayout>

        <TextView
            android:id="@+id/order_status_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/AccentColor"
            android:textSize="16dp"
            android:textAlignment="center"
            android:textStyle="bold"
            android:paddingTop="12dp"
            android:paddingBottom="12dp" />
    </LinearLayout>
    
</LinearLayout>