<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:background="?attr/selectableItemBackground"
    android:paddingTop="@dimen/base_margin_eight"
    android:paddingBottom="@dimen/base_margin_eight"
    android:layout_marginBottom="4dp"
    android:paddingStart="@dimen/base_margin_sixteen"
    android:paddingEnd="@dimen/base_margin_sixteen">

    <TextView
        android:id="@+id/connection_type_icon"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:textColor="@color/AccentColor"
        tools:text="A"
        android:textSize="@dimen/h5_text_size"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/button_preview_text"
            style="@style/body_1"
            android:ellipsize="end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="oqnbp vebyv bgbtyrvb "
            android:layout_marginBottom="@dimen/base_margin_eight"/>

        <loyaltyplantapp.droid.customviews.StyledTextView
            android:id="@+id/content"
            style="@style/body_2"
            android:lines="1"
            android:textColor="@color/AccentColor"
            android:ellipsize="end"
            android:layout_width="wrap_content"
            tools:text="asdajetj"
            android:layout_height="wrap_content" />

    </LinearLayout>
</LinearLayout>