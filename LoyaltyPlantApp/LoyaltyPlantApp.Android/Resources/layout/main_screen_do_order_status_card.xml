<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="wrap_content"
	android:gravity="center">
	<androidx.cardview.widget.CardView
		android:id="@+id/card_order_status"
		style="@style/LoyaltyPlantCardView"
		android:layout_marginBottom="@dimen/base_margin_sixteen"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		app:cardBackgroundColor="@color/CardColor">
		<FrameLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:foreground="?attr/selectableItemBackground">

			<LinearLayout
				android:id="@id/content_container"
				android:layout_width="match_parent"
				android:layout_height="wrap_content"
				android:orientation="vertical"
				android:paddingTop="@dimen/base_margin_sixteen"
				android:paddingBottom="@dimen/base_margin_sixteen">

				<loyaltyplantapp.droid.customviews.StyledTextView
					android:id="@+id/order_header"
					style="@style/h6_headline"
					android:textColor="@color/BackgroundTextColor"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="@dimen/base_margin_eight"
					android:layout_marginEnd="@dimen/material_horizontal_margin"
					android:layout_marginStart="@dimen/material_horizontal_margin"
					tools:text="Your pickup order is preparing" />

				<loyaltyplantapp.droid.customviews.StyledTextView
					android:id="@+id/order_hint"
					style="@style/body_2"
					android:textColor="@color/SecondaryTextColor"
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:layout_marginTop="@dimen/base_margin_eight"
					android:layout_marginBottom="@dimen/base_margin_eight"
					android:layout_marginEnd="@dimen/material_horizontal_margin"
					android:layout_marginStart="@dimen/material_horizontal_margin"
					tools:text="покажи кассиру чтоль" />

			</LinearLayout>

		</FrameLayout>
	</androidx.cardview.widget.CardView>
</FrameLayout>