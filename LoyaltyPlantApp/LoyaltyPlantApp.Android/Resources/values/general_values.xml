<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="base_margin_four">4dp</dimen>
    <dimen name="base_margin_eight">8dp</dimen>
    <dimen name="base_margin_sixteen">16dp</dimen>
    <dimen name="base_margin_twenty_four">24dp</dimen>
    <dimen name="base_margin_fourteen">14dp</dimen>
    <dimen name="base_margin_forty_eight">48dp</dimen>

    <dimen name="navigation_drawer_width">240dp</dimen>

    <dimen name="bottom_control_button_elevation">@dimen/zero_dp</dimen>

    <dimen name="navigation_buttons_block_height_wide">64dp</dimen>
    <dimen name="navigation_buttons_block_height">48dp</dimen>

    <dimen name="zero_dp">0dp</dimen>

    <dimen name="bill_right_padding">60dp</dimen>

    <dimen name="app_bar_height">480dp</dimen>

    <dimen name="greeting_toolbar_height">160dp</dimen>

    <dimen name="bank_card_height">130dp</dimen>
    <dimen name="bank_card_width">200dp</dimen>

    <!--native forms-->
    <dimen name="bottom_button_height">48dp</dimen>
    <dimen name="vertical_padding_native_text_input">20dp</dimen>
    <dimen name="native_forms_item_height">80dp</dimen>
    <dimen name="vertical_padding_focus_on_field">50dp</dimen>

    <!--ADA compliance APP-3957 -->
    <dimen name="min_ada_height">48dp</dimen>
    <dimen name="min_ada_width">48dp</dimen>

    <dimen name="gradient_description_container_height">100dp</dimen>
    <dimen name="bottom_button_container_height">80dp</dimen>
    <dimen name="bottom_button_with_gradient">92dp</dimen>
    <dimen name="padding_above_bottom_button">84dp</dimen>
    <dimen name="do_item_position_description_max_height">80dp</dimen>

    <dimen name="default_card_text_height">52sp</dimen>
    <dimen name="do_card_text_height">78dp</dimen>

    <!--material design dimension-->
    <dimen name="material_horizontal_margin">16dp</dimen>
    <dimen name="drawable_left_padding_button">40dp</dimen>
    <dimen name="do_success_screen_horizontal_margin">24dp</dimen>

    <!--dialog chooser-->
    <dimen name="dialog_header_text_size">20dp</dimen>

    <!--opacity flash color accent-->
    <integer name="accent_flash_opacity">111</integer>

    <!--arrow for expandable section-->
    <dimen name="arrow_down_margin_top">18dp</dimen>
    <dimen name="arrow_size">25dp</dimen>

    <!--payment list-->
    <dimen name="payment_list_item_height">67dp</dimen>
    <dimen name="payment_list_item_image_size">40dp</dimen>
    <dimen name="payment_icon_padding">10dp</dimen>
    <dimen name="payment_card_icon">45dp</dimen>
    <dimen name="payment_card_icon_margin">2dp</dimen>

    <!--authorization screen-->
    <dimen name="auth_horizontal_margin">32dp</dimen>
    <dimen name="login_button_corner">6dp</dimen>

    <!--user profile-->
    <dimen name="profile_image_margin_top">76dp</dimen>
    <dimen name="trigger_title_height">128dp</dimen>
    <dimen name="trigger_username_height">120dp</dimen>

    <!-- user profile photo behavior -->
    <dimen name="image_behavior_finalXPadding">34dp</dimen>
    <dimen name="image_behavior_finalYPosition">10dp</dimen>
    <dimen name="image_behavior_finalHeight">38dp</dimen>

    <!--smaill slideshow circlePageIndicator-->
    <dimen name="radius_page_indicator_main_screen_slideshow">3.5dp</dimen>
    <dimen name="default_radius_circle">4dp</dimen>
    <dimen name="page_indicator_height">30dp</dimen>
    <dimen name="small_slide_image_margin">10dp</dimen>
    <dimen name="gradient_height">120dp</dimen>

    <!-- Order Tracking dimens -->
    <dimen name="ot_status_view_width">40dp</dimen>

    <!-- Sidebar dimens-->
    <dimen name="nav_drawer_padding">10dp</dimen>
</resources>