<resources>
	<color name="PrimaryGradientDownColor">@color/PrimaryColor</color>
	<color name="PrimaryGradientUpColor">@color/PrimaryColor</color>
	<color name="AccentGradientDownColor">@color/AccentColor</color>
	<color name="AccentGradientUpColor">@color/AccentColor</color>
	<color name="MainScreenAllButtonGradientDownColor">@color/MainScreenAllButtonColor</color>
	<color name="MainScreenAllButtonGradientUpColor">@color/MainScreenAllButtonColor</color>
<!--Default-->
<!--
	<color name="PrimaryColor">#FFD3D3D3</color>
	<color name="PrimaryTextColor">#FF4D5FAB</color>
	<color name="PrimaryPressedColor">#FFbababa</color>
	<color name="IphoneNavigationBarActionsColor">#FF4D5FAB</color>
	<color name="AccentColor">#FFFFA400</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FFe68b00</color>
	<color name="MessageboxAccentTextColor">#FFFFA400</color>
	<color name="MessageboxAccentTextDisabledColor">#43FFA400</color>
	<color name="MessageboxAccentTextPressedColor">#FFe68b00</color>
	<color name="BackgroundColor">#FFFFFFFF</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FFFFFFFF</color>
	<color name="DefaultTextColor">#DD000000</color>
	<color name="SecondaryTextColor">#8A000000</color>
	<color name="HintTextColor">#43000000</color>
	<color name="DividerTextColor">#1F000000</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#de000000</color>
	<color name="AccentCardColor">#ff4D5FAB</color>
	<color name="AccentCardTextColor">#ffFFFFFF</color>
	<color name="PriceLabelColor">#ff3D3D3D</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ff4D5FAB</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FFFFFFFF</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FFFFA400</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FF4D5FAB</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff00BFD6</color>
	<color name="MainScreenAllButtonColor">#FF00BFD6</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF00a6bd</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->

<!--GetFried-->
	<color name="PrimaryColor">#FF121212</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF000000</color>
	<color name="IphoneNavigationBarActionsColor">#FFF5AA01</color>
	<color name="AccentColor">#FFF5AAFF</color>
	<color name="AccentTextColor">#FF000000</color>
	<color name="AccentPressedColor">#FFdc9100</color>
	<color name="MessageboxAccentTextColor">#FFF5AA01</color>
	<color name="MessageboxAccentTextDisabledColor">#43F5AA01</color>
	<color name="MessageboxAccentTextPressedColor">#FFdc9100</color>
	<color name="BackgroundColor">#FF0E0E10</color>
	<color name="BackgroundTextColor">#deF5AA01</color>
	<color name="SecondaryBackgroundColor">#FF161616</color>
	<color name="DefaultTextColor">#DDFFFFFF</color>
	<color name="SecondaryTextColor">#8AFFFFFF</color>
	<color name="HintTextColor">#43FFFFFF</color>
	<color name="DividerTextColor">#1FFFFFFF</color>
	<color name="CardColor">#ff242424</color>
	<color name="CardTextColor">#deFFFFFF</color>
	<color name="AccentCardColor">#ff292929</color>
	<color name="AccentCardTextColor">#deFFFFFF</color>
	<color name="PriceLabelColor">#ffFFFFFF</color>
	<color name="PriceLabelTextColor">#ff161616</color>
	<color name="InfoLabelColor">#ffF5AA01</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FF242424</color>
	<color name="NavigationDrawerTextColor">#FFFFFFFF</color>
	<color name="NavigationDrawerAccentColor">#FFF5AA01</color>
	<color name="NavigationDrawerCopyrightTextColor">#77FFFFFF</color>
	<color name="MainScreenDelimitersColor">#FFFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersColor">#ff242424</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ffFFFFFF</color>
	<color name="MainScreenAllButtonColor">#FFF5AA01</color>
	<color name="MainScreenAllButtonTextColor">#FF161616</color>
	<color name="MainScreenAllButtonPressedColor">#FFdc9100</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>

	<!-- PJ Jordan-->
	<!-- 
	<color name="PrimaryColor">#FFFFFFFF</color>
	<color name="PrimaryTextColor">#FF000000</color>
	<color name="PrimaryPressedColor">#FFe6e6e6</color>
	<color name="IphoneNavigationBarActionsColor">#FF2D5D2A</color>
	<color name="AccentColor">#FF2D5D2A</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FF144411</color>
	<color name="MessageboxAccentTextColor">#FF2D5D2A</color>
	<color name="MessageboxAccentTextDisabledColor">#432D5D2A</color>
	<color name="MessageboxAccentTextPressedColor">#FF144411</color>
	<color name="BackgroundColor">#FFFFFFFF</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FFFFFFFF</color>
	<color name="DefaultTextColor">#DD626262</color>
	<color name="SecondaryTextColor">#8A626262</color>
	<color name="HintTextColor">#43626262</color>
	<color name="DividerTextColor">#1F626262</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#ff000000</color>
	<color name="AccentCardColor">#ff2D5D2A</color>
	<color name="AccentCardTextColor">#ffFFFFFF</color>
	<color name="PriceLabelColor">#ff2D5D2A</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ff2D5D2A</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FFFFFFFF</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FF2D5D2A</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FF000000</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff000000</color>
	<color name="MainScreenAllButtonColor">#FF2D5D2A</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF144411</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color> -->

	<!--balance grill-->
    <!--
        <color name="PrimaryColor">#FF2EACE2</color>
        <color name="PrimaryTextColor">#FFFFFFFF</color>
        <color name="PrimaryPressedColor">#FF1593c9</color>
        <color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
        <color name="AccentColor">#FFEF532A</color>
        <color name="AccentTextColor">#FFFFFFFF</color>
        <color name="AccentPressedColor">#FFd63a11</color>
        <color name="MessageboxAccentTextColor">#FFF2A71D</color>
        <color name="MessageboxAccentTextDisabledColor">#43F2A71D</color>
        <color name="MessageboxAccentTextPressedColor">#FFd98e04</color>
        <color name="BackgroundColor">#FFFFFFFF</color>
        <color name="BackgroundTextColor">#de252525</color>
        <color name="SecondaryBackgroundColor">#FFFFFFFF</color>
        <color name="DefaultTextColor">#DD252525</color>
        <color name="SecondaryTextColor">#8A252525</color>
        <color name="HintTextColor">#43252525</color>
        <color name="DividerTextColor">#1F252525</color>
        <color name="CardColor">#ffFFFFFF</color>
        <color name="CardTextColor">#ff252525</color>
        <color name="AccentCardColor">#ffF2A71D</color>
        <color name="AccentCardTextColor">#ffFFFFFF</color>
        <color name="PriceLabelColor">#ffF2A71D</color>
        <color name="PriceLabelTextColor">#ffFFFFFF</color>
        <color name="InfoLabelColor">#ffEF532A</color>
        <color name="InfoLabelTextColor">#ffFFFFFF</color>
        <color name="CoinColor">#00000000</color>
        <color name="NavigationDrawerColor">#FFFFFFFF</color>
        <color name="NavigationDrawerTextColor">#FF252525</color>
        <color name="NavigationDrawerAccentColor">#FFF2A71D</color>
        <color name="NavigationDrawerCopyrightTextColor">#77252525</color>
        <color name="MainScreenDelimitersColor">#FFEF532A</color>
        <color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
        <color name="MainScreenSecondaryDelimitersTextColor">#ff2EACE2</color>
        <color name="MainScreenAllButtonColor">#FF2EACE2</color>
        <color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
        <color name="MainScreenAllButtonPressedColor">#FF1593c9</color>
        <color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
        <color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
        <color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
        <color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
    -->
    
    
    <!-- Buzzed Bull Creamery" [ID=2332] -->
<!--

	<color name="PrimaryColor">#FF231F20</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF0a0607</color>
	<color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
	<color name="AccentColor">#FF231F20</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FF0a0607</color>
	<color name="MessageboxAccentTextColor">#FF231F20</color>
	<color name="MessageboxAccentTextDisabledColor">#43231F20</color>
	<color name="MessageboxAccentTextPressedColor">#FF0a0607</color>
	<color name="BackgroundColor">#FFF2F2F2</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FFF2F2F2</color>
	<color name="DefaultTextColor">#DD000000</color>
	<color name="SecondaryTextColor">#8A000000</color>
	<color name="HintTextColor">#43000000</color>
	<color name="DividerTextColor">#1F000000</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#de000000</color>
	<color name="AccentCardColor">#ffFFFFFF</color>
	<color name="AccentCardTextColor">#de231F20</color>
	<color name="PriceLabelColor">#ff6C6C6C</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ffFFFFFF</color>
	<color name="InfoLabelTextColor">#ff231F20</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FFFFFFFF</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FF6C6C6C</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FF231F20</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff231F20</color>
	<color name="MainScreenAllButtonColor">#FF231F20</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF0a0607</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->


    <!--SKY LINE CHILLY-->
<!--
	<color name="PrimaryColor">#FF003A7D</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF002164</color>
	<color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
	<color name="AccentColor">#FFFFC107</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FFe6a800</color>
	<color name="MessageboxAccentTextColor">#FFFFC107</color>
	<color name="MessageboxAccentTextDisabledColor">#43FFC107</color>
	<color name="MessageboxAccentTextPressedColor">#FFe6a800</color>
	<color name="BackgroundColor">#ffFFFFFF</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#ffFFFFFF</color>
	<color name="DefaultTextColor">#DD000000</color>
	<color name="SecondaryTextColor">#8A000000</color>
	<color name="HintTextColor">#43000000</color>
	<color name="DividerTextColor">#1F000000</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#de000000</color>
	<color name="AccentCardColor">#ffFFFFFF</color>
	<color name="AccentCardTextColor">#de000000</color>
	<color name="PriceLabelColor">#ff0073D0</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ff0073D0</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#FFFFFFFF</color>
	<color name="NavigationDrawerColor">#FFd1deef</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FF003A7D</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FF003A7D</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff003A7D</color>
	<color name="MainScreenAllButtonColor">#FF003A7D</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF002164</color>
	<color name="SecondaryBackgroundGradientUpColor">#ff7fa7d8</color>
	<color name="SecondaryBackgroundGradientDownColor">#ffFFFFFF</color>
	<color name="MainBackgroundGradientUpColor">#ff7fa7d8</color>
	<color name="MainBackgroundGradientDownColor">#ffFFFFFF</color>
-->

	<!--"Veleson Pub" 1733-->
<!--
	<color name="PrimaryColor">#FF283778</color>
	<color name="PrimaryTextColor">#FFA59753</color>
	<color name="PrimaryPressedColor">#FF0f1e5f</color>
	<color name="IphoneNavigationBarActionsColor">#FFDCC96E</color>
	<color name="AccentColor">#FFDCC96E</color>
	<color name="AccentTextColor">#FF283777</color>
	<color name="AccentPressedColor">#FFc3b055</color>
	<color name="MessageboxAccentTextColor">#FF283879</color>
	<color name="MessageboxAccentTextDisabledColor">#43283879</color>
	<color name="MessageboxAccentTextPressedColor">#FF0f1f60</color>
	<color name="BackgroundColor">#FFF8F8F8</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FF283777</color>
	<color name="DefaultTextColor">#DDFFFFFF</color>
	<color name="SecondaryTextColor">#8AFFFFFF</color>
	<color name="HintTextColor">#43FFFFFF</color>
	<color name="DividerTextColor">#1FFFFFFF</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#de000000</color>
	<color name="AccentCardColor">#ff283777</color>
	<color name="AccentCardTextColor">#deFFFFFF</color>
	<color name="PriceLabelColor">#ff283778</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ffDCC96E</color>
	<color name="InfoLabelTextColor">#ff283777</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FF283778</color>
	<color name="NavigationDrawerTextColor">#FFF8F8F8</color>
	<color name="NavigationDrawerAccentColor">#FFDCC96E</color>
	<color name="NavigationDrawerCopyrightTextColor">#77F8F8F8</color>
	<color name="MainScreenDelimitersColor">#FF192659</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff283777</color>
	<color name="MainScreenAllButtonColor">#FFDCC96E</color>
	<color name="MainScreenAllButtonTextColor">#FF283777</color>
	<color name="MainScreenAllButtonPressedColor">#FFc3b055</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->

    <!--KFC 1898-->
	<!--
	<color name="PrimaryColor">#FF89081E</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF700005</color>
	<color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
	<color name="AccentColor">#FFC41230</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FFab0017</color>
	<color name="MessageboxAccentTextColor">#FFC00A27</color>
	<color name="MessageboxAccentTextDisabledColor">#43C00A27</color>
	<color name="MessageboxAccentTextPressedColor">#FFa7000e</color>
	<color name="BackgroundColor">#FFFFFFFF</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FFFFFFFF</color>
	<color name="DefaultTextColor">#DD000000</color>
	<color name="SecondaryTextColor">#8A000000</color>
	<color name="HintTextColor">#43000000</color>
	<color name="DividerTextColor">#1F000000</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#de000000</color>
	<color name="AccentCardColor">#ffFFFFFF</color>
	<color name="AccentCardTextColor">#de231E0A</color>
	<color name="PriceLabelColor">#ff474342</color>
	<color name="PriceLabelTextColor">#ffFBF0E2</color>
	<color name="InfoLabelColor">#ffC41230</color>
	<color name="InfoLabelTextColor">#ffFBF0E2</color>
	<color name="CoinColor">#ffffcf2c</color>
	<color name="NavigationDrawerColor">#FFC72034</color>
	<color name="NavigationDrawerTextColor">#FFFFFFFF</color>
	<color name="NavigationDrawerAccentColor">#FFFFFFFF</color>
	<color name="NavigationDrawerCopyrightTextColor">#77FFFFFF</color>
	<color name="MainScreenDelimitersColor">#FF393635</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ffC41230</color>
	<color name="MainScreenAllButtonColor">#FF3D3D3B</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF242422</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->

	<!--7 Leaves 2732-->
	<!--
	<color name="PrimaryColor">#FFAE212A</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF950811</color>
	<color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
	<color name="AccentColor">#FFAE212A</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FF950811</color>
	<color name="MessageboxAccentTextColor">#FFAE212A</color>
	<color name="MessageboxAccentTextDisabledColor">#43AE212A</color>
	<color name="MessageboxAccentTextPressedColor">#FF950811</color>
	<color name="BackgroundColor">#FFFFFFFF</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FFFFFFFF</color>
	<color name="DefaultTextColor">#DD626262</color>
	<color name="SecondaryTextColor">#8A626262</color>
	<color name="HintTextColor">#43626262</color>
	<color name="DividerTextColor">#1F626262</color>
	<color name="CardColor">#ffFFFFFF</color>
	<color name="CardTextColor">#ff000000</color>
	<color name="AccentCardColor">#ffAE212A</color>
	<color name="AccentCardTextColor">#ffFFFFFF</color>
	<color name="PriceLabelColor">#ffAE212A</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ffAE212A</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FFFFFFFF</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FFAE212A</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FF50565A</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff000000</color>
	<color name="MainScreenAllButtonColor">#FFAE212A</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF950811</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
	-->
	<!--halfDay-->
<!--
	<color name="PrimaryColor">#FF3D2C20</color>
	<color name="PrimaryTextColor">#FFFFFFFF</color>
	<color name="PrimaryPressedColor">#FF241307</color>
	<color name="IphoneNavigationBarActionsColor">#FFFFFFFF</color>
	<color name="AccentColor">#FFE0281F</color>
	<color name="AccentTextColor">#FFFFFFFF</color>
	<color name="AccentPressedColor">#FFc70f06</color>
	<color name="MessageboxAccentTextColor">#FFE0281F</color>
	<color name="MessageboxAccentTextDisabledColor">#43E0281F</color>
	<color name="MessageboxAccentTextPressedColor">#FFc70f06</color>
	<color name="BackgroundColor">#FF241E19</color>
	<color name="BackgroundTextColor">#c2FFFFFF</color>
	<color name="SecondaryBackgroundColor">#FF241E19</color>
	<color name="DefaultTextColor">#DDFFFFFF</color>
	<color name="SecondaryTextColor">#8AFFFFFF</color>
	<color name="HintTextColor">#43FFFFFF</color>
	<color name="DividerTextColor">#1FFFFFFF</color>
	<color name="CardColor">#ff31231A</color>
	<color name="CardTextColor">#deFFFFFF</color>
	<color name="AccentCardColor">#ff31231A</color>
	<color name="AccentCardTextColor">#deFFFFFF</color>
	<color name="PriceLabelColor">#ffB3A333</color>
	<color name="PriceLabelTextColor">#ffFFFFFF</color>
	<color name="InfoLabelColor">#ff8E171A</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FF241E19</color>
	<color name="NavigationDrawerTextColor">#FFFFFFFF</color>
	<color name="NavigationDrawerAccentColor">#FFCBB836</color>
	<color name="NavigationDrawerCopyrightTextColor">#77FFFFFF</color>
	<color name="MainScreenDelimitersColor">#FFFFFFFF</color>
	<color name="MainScreenSecondaryDelimitersColor">#ff31231A</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ffFFFFFF</color>
	<color name="MainScreenAllButtonColor">#FFB77C4A</color>
	<color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
	<color name="MainScreenAllButtonPressedColor">#FF9e6331</color>
	<color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
	<color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
	<color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
	<color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->
	<!--smartCut-->
<!--
	<color name="PrimaryColor">#FFA6CE3A</color>
	<color name="PrimaryTextColor">#FF454545</color>
	<color name="PrimaryPressedColor">#FF8db521</color>
	<color name="IphoneNavigationBarActionsColor">#FF364C08</color>
	<color name="AccentColor">#FFD8EE08</color>
	<color name="AccentTextColor">#FF3D540E</color>
	<color name="AccentPressedColor">#FFbfd500</color>
	<color name="MessageboxAccentTextColor">#FF7D9F37</color>
	<color name="MessageboxAccentTextDisabledColor">#437D9F37</color>
	<color name="MessageboxAccentTextPressedColor">#FF64861e</color>
	<color name="BackgroundColor">#FF7D9F37</color>
	<color name="BackgroundTextColor">#de000000</color>
	<color name="SecondaryBackgroundColor">#FF7D9F37</color>
	<color name="DefaultTextColor">#DD000000</color>
	<color name="SecondaryTextColor">#8A000000</color>
	<color name="HintTextColor">#43000000</color>
	<color name="DividerTextColor">#1F000000</color>
	<color name="CardColor">#ffAEBF34</color>
	<color name="CardTextColor">#de243208</color>
	<color name="AccentCardColor">#ffBDCF3A</color>
	<color name="AccentCardTextColor">#de303408</color>
	<color name="PriceLabelColor">#ffFFFFFF</color>
	<color name="PriceLabelTextColor">#ff000000</color>
	<color name="InfoLabelColor">#ff556A21</color>
	<color name="InfoLabelTextColor">#ffFFFFFF</color>
	<color name="CoinColor">#00000000</color>
	<color name="NavigationDrawerColor">#FFBDCF3A</color>
	<color name="NavigationDrawerTextColor">#FF000000</color>
	<color name="NavigationDrawerAccentColor">#FF576F26</color>
	<color name="NavigationDrawerCopyrightTextColor">#77000000</color>
	<color name="MainScreenDelimitersColor">#FFE8FF10</color>
	<color name="MainScreenSecondaryDelimitersColor">#ffD8EE08</color>
	<color name="MainScreenSecondaryDelimitersTextColor">#ff000000</color>
	<color name="MainScreenAllButtonColor">#FFD8EE08</color>
	<color name="MainScreenAllButtonTextColor">#FF3D540E</color>
	<color name="MainScreenAllButtonPressedColor">#FFbfd500</color>
    <color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
    <color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
    <color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
    <color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>-->
    <!--SUSHI LOVE-->
<!--
    <color name="PrimaryColor">#FFFFFFFF</color>
    <color name="PrimaryTextColor">#FF454545</color>
    <color name="PrimaryPressedColor">#FFe6e6e6</color>
    <color name="IphoneNavigationBarActionsColor">#FFE46719</color>
    <color name="AccentColor">#FF7FBA00</color>
    <color name="AccentTextColor">#FFFFFFFF</color>
    <color name="AccentPressedColor">#FF66a100</color>
    <color name="MessageboxAccentTextColor">#FFE36119</color>
    <color name="MessageboxAccentTextDisabledColor">#43E36119</color>
    <color name="MessageboxAccentTextPressedColor">#FFca4800</color>
    <color name="BackgroundColor">#FFF8F8F8</color>
    <color name="BackgroundTextColor">#de000000</color>
    <color name="SecondaryBackgroundColor">#FFFFFFFF</color>
    <color name="DefaultTextColor">#DD000000</color>
    <color name="SecondaryTextColor">#8A000000</color>
    <color name="HintTextColor">#43000000</color>
    <color name="DividerTextColor">#1F000000</color>
    <color name="CardColor">#ffFFFFFF</color>
    <color name="CardTextColor">#de000000</color>
    <color name="AccentCardColor">#ffE36119</color>
    <color name="AccentCardTextColor">#deFFFFFF</color>
    <color name="PriceLabelColor">#ffE36119</color>
    <color name="PriceLabelTextColor">#ffFFFFFF</color>
    <color name="InfoLabelColor">#ffE36119</color>
    <color name="InfoLabelTextColor">#ffFFFFFF</color>
    <color name="CoinColor">#00000000</color>
    <color name="NavigationDrawerColor">#FFFFFFFF</color>
    <color name="NavigationDrawerTextColor">#FF000000</color>
    <color name="NavigationDrawerAccentColor">#FFE36119</color>
    <color name="NavigationDrawerCopyrightTextColor">#77000000</color>
    <color name="MainScreenDelimitersColor">#FFE04E1A</color>
    <color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
    <color name="MainScreenSecondaryDelimitersTextColor">#ffDE421B</color>
    <color name="MainScreenAllButtonColor">#FF7FBA00</color>
    <color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
    <color name="MainScreenAllButtonPressedColor">#FF66a100</color>
    <color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
    <color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
    <color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
    <color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
-->

    <!--PURE PROTEIN-->
	<!--
    <color name="PrimaryColor">#FFFFFFFF</color>
    <color name="PrimaryTextColor">#FF0D80AA</color>
    <color name="PrimaryPressedColor">#FFe6e6e6</color>
    <color name="IphoneNavigationBarActionsColor">#FF0D80AA</color>
    <color name="AccentColor">#FF0D80AA</color>
    <color name="AccentTextColor">#FFFFFFFF</color>
    <color name="AccentPressedColor">#FF006791</color>
    <color name="MessageboxAccentTextColor">#FF0D80AA</color>
    <color name="MessageboxAccentTextDisabledColor">#430D80AA</color>
    <color name="MessageboxAccentTextPressedColor">#FF006791</color>
    <color name="BackgroundColor">#FFF1F1F1</color>
    <color name="BackgroundTextColor">#de000000</color>
    <color name="SecondaryBackgroundColor">#FFF1F1F1</color>
    <color name="DefaultTextColor">#DD000000</color>
    <color name="SecondaryTextColor">#8A000000</color>
    <color name="HintTextColor">#43000000</color>
    <color name="DividerTextColor">#1F000000</color>
    <color name="CardColor">#ffFFFFFF</color>
    <color name="CardTextColor">#de121A1D</color>
    <color name="AccentCardColor">#ff0D80AA</color>
    <color name="AccentCardTextColor">#deFFFFFF</color>
    <color name="PriceLabelColor">#99000000</color>
    <color name="PriceLabelTextColor">#ffFFFFFF</color>
    <color name="InfoLabelColor">#ff0D80AA</color>
    <color name="InfoLabelTextColor">#ffFFFFFF</color>
    <color name="CoinColor">#00000000</color>
    <color name="NavigationDrawerColor">#FFFFFFFF</color>
    <color name="NavigationDrawerTextColor">#FF000000</color>
    <color name="NavigationDrawerAccentColor">#FF0D80AA</color>
    <color name="NavigationDrawerCopyrightTextColor">#77000000</color>
    <color name="MainScreenDelimitersColor">#FF0D80AA</color>
    <color name="MainScreenSecondaryDelimitersColor">#ffFFFFFF</color>
    <color name="MainScreenSecondaryDelimitersTextColor">#ff0D80AA</color>
    <color name="MainScreenAllButtonColor">#FF0D80AA</color>
    <color name="MainScreenAllButtonTextColor">#FFFFFFFF</color>
    <color name="MainScreenAllButtonPressedColor">#FF006791</color>
    <color name="SecondaryBackgroundGradientUpColor">@color/SecondaryBackgroundColor</color>
    <color name="SecondaryBackgroundGradientDownColor">@color/SecondaryBackgroundColor</color>
    <color name="MainBackgroundGradientUpColor">@color/BackgroundColor</color>
    <color name="MainBackgroundGradientDownColor">@color/BackgroundColor</color>
	-->

</resources>
