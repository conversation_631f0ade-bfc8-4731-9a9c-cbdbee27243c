<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--only 19+ sdk can show picker dialogs by webView-->
    <style name="WebViewAlertDialog" parent="@android:style/Theme.Holo.Light.Dialog.NoActionBar">
        <item name="colorAccent">@color/MessageboxAccentTextColor</item>
        <item name="android:textColor">@color/MessageboxAccentTextColor</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="LoyaltyPlantTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="LoyaltyPlantTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="LoyaltyPlantTheme.BaseNoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="LoyaltyPlantTheme.BaseNoActionBar.CheckoutScreen">

    </style>

    <style name="LPTransparentStatusBar" parent="LoyaltyPlantTheme.BaseNoActionBar">
        <item name="windowActionBarOverlay">false</item>
    </style>

</resources>