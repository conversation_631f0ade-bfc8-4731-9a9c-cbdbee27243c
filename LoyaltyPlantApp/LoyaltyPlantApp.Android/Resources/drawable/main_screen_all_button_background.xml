<?xml version="1.0" encoding="utf-8"?>

<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

  <!-- Drop Shadow Stack -->
  <item>
    <shape>
      <padding android:top="2dp" android:right="2dp" android:bottom="2dp" android:left="2dp" />
      <!--<solid android:color="@color/MainScreenAllButtonColor" />-->
      <gradient
       android:angle="90"
       android:startColor="@color/MainScreenAllButtonGradientDownColor"
       android:endColor="@color/MainScreenAllButtonGradientUpColor"
       android:type="linear" />
      <corners android:radius="2dp" />
    </shape>
  </item>
</layer-list>