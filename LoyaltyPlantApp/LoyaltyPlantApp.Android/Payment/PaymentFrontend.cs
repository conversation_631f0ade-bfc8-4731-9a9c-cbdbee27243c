using System.Threading.Tasks;
using Android.App;
using Android.Content;
using AndroidX.AppCompat.App;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Payment;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlantApp.Droid.Base;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Payment.WebView;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Payment
{
    public class PaymentFrontend : AppFrontend, IPaymentFrontend
	{
		public void UpdateCheckoutScreen()
		{
			var context = Context as Activity;
			context?.RunOnUiThread(() =>
			{
				var viewCheckout = context as IViewCheckout;
				_ = viewCheckout?.UpdateCheckoutScreenAsync();
			});
		}

		public void ShowPaymentMethodsProfile()
		{
			var intent = new Intent(Context, typeof(MethodsProfileActivity));
			Context.StartActivity(intent);
		}

		public void ShowPaymentMethodsScreen(PaymentMethod lastMethod)
		{
			var fragment = PaymentListDialogFragment.NewInstance();
			fragment.TryShowFragment(Context as AppCompatActivity);
		}

		public void CreateCvvHintMessage()
		{
			CVVHintFragment.NewInstance();
		}

		public void OpenPaymentWebPage(string url, Method httpMethod)
        {
	        OpenPaymentProcessingWebPage(null, url, null, httpMethod);
        }

        public void OpenPaymentWebPageHtml(string html, string url, The3DsLink the3DsLink, Method httpMethod)
        {
	        OpenPaymentProcessingWebPage(html, url, the3DsLink, httpMethod);
        }

		public void OpenPaymentProcessingWebPage(string html, string url, The3DsLink the3DsLink, Method httpMethod)
		{
			var resultUrl = string.IsNullOrWhiteSpace(the3DsLink?.Url) ? url : the3DsLink?.Url;

			var intent = new Intent(Context, typeof(PaymentProcessingWebActivity));
	        intent.PutExtra(PaymentProcessingWebActivity.PageContent, html);
			intent.PutExtra(PaymentProcessingWebActivity.Body, the3DsLink?.Body);
			intent.PutExtra(PaymentProcessingWebActivity.PageUrl, resultUrl);
			intent.PutExtra(PaymentProcessingWebActivity.HttpMethod, httpMethod.ToString());
			Context.StartActivity(intent);
        }

        public void OpenNativeCardInput()
        {
	        if (Context is BankCardInputInformationActivity)
	        {
		        LpLogger.LOG_W("Native Card input has already opened");
		        return;
	        }
	        var intent = new Intent(Context, typeof(BankCardInputInformationActivity));
	        Context.StartActivity(intent);
        }

        public void CloseNativeCardInput()
        {
	        if (Context is BankCardInputInformationActivity activity)
	        {
		        activity.OnBackPressed();
	        }
        }

        public void ShowEditTotalScreen(string screenDescription)
        {
            // TODO send screenDescription in PaymentSumInputFragment
            Context.StartIntentInRootActivity(activity =>
	        {
		        var editSum = PaymentSumInputFragment.NewInstance();
		        editSum.ShowInContainerActivity(activity, true);
	        });
        }

        public Task<CvvResult> ShowCvvDialog(BankCard bankCard)
        {
            var completionSource = new TaskCompletionSource<CvvResult>();
            var fragment = EnterCVCDialogFragment.NewInstance(bankCard.GetMask(), completionSource);
            fragment.TryShowFragment(Context as AppCompatActivity);
            return completionSource.Task;
        }
    }
}