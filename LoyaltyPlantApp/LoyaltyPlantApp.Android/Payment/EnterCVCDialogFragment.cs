using System;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Languages;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlantApp.Droid.Base.Utils;
using AlertDialog = AndroidX.AppCompat.App.AlertDialog;

namespace LoyaltyPlantApp.Droid.Payment
{
    public class EnterCVCDialogFragment : SafeDialogFragment
    {
        private const string CardMask = "card_mask";

        private readonly TaskCompletionSource<CvvResult> _completionSource;
        private EditText _inputCvv;
        private TextView _cvcInputError;
        private string _cardMask;

        public EnterCVCDialogFragment()
        {
        }

        public static EnterCVCDialogFragment NewInstance(string cardMask, TaskCompletionSource<CvvResult> completionSource)
        {
            var args = new Bundle();
            args.PutString(CardMask, cardMask);
            return new EnterCVCDialogFragment(completionSource) { Arguments = args };
        }

        private EnterCVCDialogFragment(TaskCompletionSource<CvvResult> completionSource)
        {
            _completionSource = completionSource;
        }

        public override Dialog OnCreateDialog(Bundle savedInstanceState)
        {
            _cardMask = Arguments.GetString(CardMask);
            var inflater = LayoutInflater.From(Context);
            var builder = new AlertDialog.Builder(Context, Resource.Style.LPDialogTheme);
            builder.SetView(InflateCvСView(inflater));
            builder.SetPositiveButton(I18N.PAY, OnOkClick);
            builder.SetNegativeButton(I18N.CANCEL, OnCancelClick);
            builder.SetTitle(I18N.ENTER_CVC_FOR_CARD_TITLE);

            return builder.Create();
        }

        private View InflateCvСView(LayoutInflater inflater)
        {
            var view = inflater.Inflate(Resource.Layout.dialog_cvv_enter_layout, null);
            var cardTitle = view.FindViewById<TextView>(Resource.Id.card);
            _inputCvv = view.FindViewById<EditText>(Resource.Id.input_cvc);
            _cvcInputError = view.FindViewById<TextView>(Resource.Id.error_cvc_text);
            cardTitle.Text = _cardMask;
            if (LanguagesModule.Instance.IsRtlSelected())
                cardTitle.Gravity = GravityFlags.End;
            return view;
        }

        private void OnOkClick(object sender, DialogClickEventArgs e)
        {
            //TODO если будет нужно проверка длинны cvv 
            //if(string.IsNullOrWhiteSpace(_inputCvv.Text) || _inputCvv.Text.Length < 3 )
            //{
            //    _cvvInputError.Text = "Error";
            //    _cvvInputError.Visibility = ViewStates.Visible;
            //    return;
            //}
            _cvcInputError.Visibility = ViewStates.Invisible;

            var result = new CvvResult { IsEntered = true, Cvv = _inputCvv.Text };
            _completionSource?.TrySetResult(result);
        }

        private void OnCancelClick(object sender, DialogClickEventArgs e)
        {
            _completionSource?.SetResult(new CvvResult { IsEntered = false });
        }

        public override void OnCancel(IDialogInterface dialog)
        {
            _completionSource?.SetResult(new CvvResult { IsEntered = false });
            base.OnCancel(dialog);
        }

        public override void OnDismiss(IDialogInterface dialog)
        {
            _completionSource?.TrySetResult(new CvvResult { IsEntered = false });
            base.OnDismiss(dialog);
        }
    }
}
