using System.Collections.Generic;
using System.Linq;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Views;
using Android.Widget;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.MakeCall;
using LoyaltyPlant.MakeCall.Model;
using LoyaltyPlant.View;
using LoyaltyPlantApp.Droid.Base.Screens;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.MakeCall
{
    [Activity(Theme = "@style/LoyaltyPlantTheme", ScreenOrientation = ScreenOrientation.Portrait)]
    public class MakeCallActivity : BaseActivity
    {
        public const string CardId = "CARD_ID";

        private LinearLayout _listContainer;
        private MakeCallCard _card;

        protected override void OnCreate(Bundle bundle)
        {
            base.OnCreate(bundle);

            SetContentView(Resource.Layout.activity_multibrand);

            _listContainer = FindViewById<LinearLayout>(Resource.Id.list_container);

            var cardId = Intent.GetIntExtra(CardId, 0);
            _card = MakeCallModule.Instance.GetCard(cardId);
            if (_card == null)
            {
                LpLogger.LOG_E($"MultiItemCard: {cardId} didn't find");
                Finish();
                return;
            }

            SupportActionBar.Title = _card.PreviewText;
            SupportActionBar.SetDisplayHomeAsUpEnabled(true);

            foreach (var buttonItem in _card.Buttons)
            {
                var inflater = (LayoutInflater)GetSystemService(Context.LayoutInflaterService);
                View view = inflater.Inflate(Resource.Layout.multibrand_list_item, null);

                view.FindViewById<TextView>(Resource.Id.connection_type_icon).SetIconFontSymbol(IconFontSymbols.Call);
                view.FindViewById<TextView>(Resource.Id.button_preview_text).Text = buttonItem.PreviewText;
                view.FindViewById<TextView>(Resource.Id.content).Text = buttonItem.Phone;

                view.Click += (sender, args) =>
                {
                    this.MakeCall(buttonItem.Phone);
                };

                _listContainer.AddView(view);
            }
        }

        public override ApplicationScreenType ScreenType => ApplicationScreenType.MakeCallCardScreen;

        public override bool OnOptionsItemSelected(IMenuItem item)
        {
            switch (item.ItemId)
            {
                // Respond to the action bar's Up/Home button
                case Android.Resource.Id.Home:
                    Finish();
                    return true;
            }
            return base.OnOptionsItemSelected(item);
        }

    }
}