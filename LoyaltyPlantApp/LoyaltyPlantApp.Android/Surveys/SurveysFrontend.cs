using LoyaltyPlant.Surveys;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlantApp.Droid.Base;
using LoyaltyPlantApp.Droid.Base.Screencontroller.ScreenAction;
using LoyaltyPlantApp.Droid.Utils.Androidextensions;

namespace LoyaltyPlantApp.Droid.Surveys
{
    public class SurveysFrontend : AppFrontend, ISurveysFrontend
    {
        public void ShowSurveyScreen(ShowSurveyScreenAction action)
        {
            var canGoBack = action.CanGoBack;
            Context.StartIntentInRootActivity((activity =>
            {
                var surveyFragment = SurveyFragment.NewInstance(action.ToBundle());
                surveyFragment.ShowInContainerActivity(activity, canGoBack);
            }));
        }
    }
}