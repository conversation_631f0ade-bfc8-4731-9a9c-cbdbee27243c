<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AdjustSdk.Xamarin.iOS" version="4.29.0" targetFramework="xamarinios10" />
  <package id="AppsFlyerXamarinBinding" version="6.5.4" targetFramework="xamarinios10" />
  <package id="AspectInjector" version="2.8.1" targetFramework="xamarinios10" />
  <package id="BouncyCastle.NetCore" version="1.8.5" targetFramework="xamarinios10" />
  <package id="Digifais.Xamarin.Facebook.CoreKit.iOS" version="16.1.2" targetFramework="xamarinios10" />
  <package id="Digifais.Xamarin.Facebook.GamingServicesKit.iOS" version="16.1.2" targetFramework="xamarinios10" />
  <package id="Digifais.Xamarin.Facebook.LoginKit.iOS" version="16.1.2" targetFramework="xamarinios10" />
  <package id="Digifais.Xamarin.Facebook.ShareKit.iOS" version="16.1.2" targetFramework="xamarinios10" />
  <package id="Digifais.Xamarin.Facebook.iOS" version="16.1.2" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter" version="3.2.1" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter.Analytics" version="3.2.1" targetFramework="xamarinios10" />
  <package id="Microsoft.AppCenter.Crashes" version="3.2.1" targetFramework="xamarinios10" />
  <package id="Microsoft.CSharp" version="4.3.0" targetFramework="xamarinios10" />
  <package id="Microsoft.NETCore.Platforms" version="3.1.0" targetFramework="xamarinios10" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="Naxam.BraintreeApplePay.iOS" version="4.37.0" targetFramework="xamarinios10" />
  <package id="Naxam.BrainTreeCore.iOS" version="4.37.0" targetFramework="xamarinios10" />
  <package id="NETStandard.Library" version="2.0.3" targetFramework="xamarinios10" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="xamarinios10" />
  <package id="RestSharp" version="106.1.0" targetFramework="xamarinios10" />
  <package id="System.ComponentModel.TypeConverter" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Serialization.Formatters" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Runtime.Serialization.Primitives" version="4.3.0" targetFramework="xamarinios10" />
  <package id="System.Xml.XmlDocument" version="4.3.0" targetFramework="xamarinios10" />
  <package id="WebP.Touch" version="1.0.8" targetFramework="xamarinios10" />
  <package id="Xamarin.Build.Download" version="0.11.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Essentials" version="1.6.0" targetFramework="xamarinios10" />
  <package id="Xamarin.FFImageLoading" version="2.4.11.982" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Analytics" version="8.10.0.3" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Core" version="8.10.0.3" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.Installations" version="8.10.0.3" targetFramework="xamarinios10" />
  <package id="Xamarin.Firebase.iOS.InstanceID" version="4.8.0" targetFramework="xamarinios10" />
  <package id="Xamarin.Google.iOS.Analytics" version="3.20.0.2" targetFramework="xamarinios10" />
  <package id="Xamarin.Google.iOS.SignIn" version="5.0.2.4" targetFramework="xamarinios10" />
  <package id="Xamarin.Google.iOS.TagManager" version="7.4.0.2" targetFramework="xamarinios10" />
  <package id="ZXing.Net.Mobile" version="2.4.1" targetFramework="xamarinios10" />
</packages>