{"images": [{"extent": "full-screen", "idiom": "iphone", "minimum-system-version": "8.0", "orientation": "landscape", "scale": "3x", "subtype": "736h"}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "iphone", "minimum-system-version": "7.0", "orientation": "portrait", "scale": "2x"}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "iphone", "minimum-system-version": "7.0", "orientation": "portrait", "scale": "2x", "subtype": "retina4"}, {"extent": "full-screen", "filename": "Default-Portrait~ipad.png", "idiom": "ipad", "minimum-system-version": "7.0", "orientation": "portrait", "scale": "1x"}, {"extent": "full-screen", "filename": "Default-Portrait@2x~ipad.png", "idiom": "ipad", "minimum-system-version": "7.0", "orientation": "portrait", "scale": "2x"}, {"extent": "full-screen", "filename": "Default-Portrait-834w-1112h@2x~ipad.png", "idiom": "ipad", "minimum-system-version": "9.0", "orientation": "portrait", "scale": "2x", "subtype": "834h"}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "ipad", "minimum-system-version": "9.0", "orientation": "portrait", "scale": "2x", "subtype": "1194h"}, {"extent": "full-screen", "filename": "Default-Portrait-1024w-1366h@2x~ipad.png", "idiom": "ipad", "minimum-system-version": "9.0", "orientation": "portrait", "scale": "2x", "subtype": "1366h"}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "iphone", "minimum-system-version": "11.0", "orientation": "portrait", "scale": "3x", "subtype": "2436h", "unassigned": true}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "iphone", "minimum-system-version": "8.0", "orientation": "portrait", "scale": "3x", "subtype": "736h", "unassigned": true}, {"extent": "full-screen", "filename": "<EMAIL>", "idiom": "iphone", "minimum-system-version": "8.0", "orientation": "portrait", "scale": "2x", "subtype": "667h", "unassigned": true}, {"extent": "full-screen", "filename": "Default-Portrait-744w-1133h@2x~ipad.png", "idiom": "ipad", "minimum-system-version": "9.0", "orientation": "portrait", "scale": "2x", "subtype": "1133h"}], "info": {"author": "xcode", "version": 1}}