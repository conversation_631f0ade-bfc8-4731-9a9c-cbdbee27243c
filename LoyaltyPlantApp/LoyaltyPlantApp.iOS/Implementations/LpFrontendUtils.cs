using System;
using System.Collections.Generic;
using UIKit;
using CoreGraphics;
using System.Text.RegularExpressions;

namespace LoyaltyPlantApp.UniversalFrontend
{
    public static class LpFrontendUtils
    {
		public static long Timestamp { get; set; }

		//mb works
		public static UIImage CreateResizableImageFromNinePatchImage(UIImage ninePatchImage) 
		{
			var rgbaImage = GetRgbAsFromImage(ninePatchImage, 0, 0, ninePatchImage.Size.Width * ninePatchImage.Size.Height);
			var topBarRgba = rgbaImage.GetRange(1, (int)ninePatchImage.Size.Width - 2);
			var leftBarRgba = new List<List<double>>();

			for (var i = 0; i < rgbaImage.Count; i += (int)ninePatchImage.Size.Width) 
			{
				leftBarRgba.Add(rgbaImage[i]);
			}

			int top = 30, left = -1, bottom = 30, right = -1;

			for (var i = 0; i < topBarRgba.Count; i++)
			{
				var aColor = topBarRgba[i];
				if ((int) aColor[3] == 1)
				{
					left = topBarRgba.IndexOf(aColor);
					break;
				}
			}

			for (var i = topBarRgba.Count - 1; i >= 0; i--)
			{
				var aColor = topBarRgba[i];
				if ((int) aColor[3] == 1)
				{
					right = topBarRgba.IndexOf(aColor);
					break;
				}
			}

			for (var i = 0; i < leftBarRgba.Count; i++)
			{
				var aColor = leftBarRgba[i];
				if ((int) aColor[3] == 1)
				{
					top = leftBarRgba.IndexOf(aColor);
					break;
				}
			}

			for (var i = leftBarRgba.Count - 1; i >= 0; i--)
			{
				var aColor = leftBarRgba[i];
				if ((int) aColor[3] == 1)
				{
					bottom = leftBarRgba.IndexOf(aColor);
					break;
				}
			}

			var cropImage = crop(ninePatchImage, new CGRect(1, 1, ninePatchImage.Size.Width - 2, ninePatchImage.Size.Height - 2));
			return cropImage.CreateResizableImage(new UIEdgeInsets(top, left, bottom, right)); 
		}

		private static UIImage crop(UIImage image, CGRect rect) 
		{
			if (image.CurrentScale > 1.0f)
			{
				rect = new CGRect(rect.X * image.CurrentScale,
					rect.Y * image.CurrentScale,
					rect.Size.Width * image.CurrentScale,
					rect.Size.Height * image.CurrentScale);
			}

			var imageRef = image.CGImage.WithImageInRect(rect); 
			return new UIImage(imageRef, image.CurrentScale, image.Orientation);
		}

		private static List<List<double>> GetRgbAsFromImage(UIImage image, int xx, int yy, nfloat count)
		{
			var result = new List<List<double>>();
			var imageRef = image.CGImage;
			var width = imageRef.Height;
			var height = imageRef.Width;
			var colorSpace = CGColorSpace.CreateDeviceRGB();
			var bytesPerRow = 4 * width;
			var rawData = new byte[height*width*4];

			CGBitmapContext context = new CGBitmapContext (rawData,
				imageRef.Width, imageRef.Height, imageRef.BitsPerComponent, imageRef.BytesPerRow,
				imageRef.ColorSpace, CGBitmapFlags.PremultipliedLast | CGBitmapFlags.ByteOrder32Big);

			context.DrawImage(new CGRect(0, 0, width, height), imageRef);
			var byteIndex = (bytesPerRow * yy) + xx * 4;

			for (var ii = 0; ii < count; ++ii) {
				var red = (rawData[byteIndex] * 1.0) / 255.0;
				var green = (rawData[byteIndex + 1] * 1.0) / 255.0;
				var blue = (rawData[byteIndex + 2] * 1.0) / 255.0;
				var alpha = (rawData[byteIndex + 3] * 1.0) / 255.0;
				byteIndex += 4;
				var aColor = new List<double>{red,green,blue,alpha};
				result.Add(aColor);
			}

			return result;
		}

		public static string FormatPhoneNumber(string phoneNumber)
		{

			if (String.IsNullOrEmpty(phoneNumber))
				return phoneNumber;

			Regex phoneParser = null;
			string format = "";

			switch (phoneNumber.Length)
			{

			case 5:
				phoneParser = new Regex(@"(\d{3})(\d{2})");
				format = "$1 $2";
				break;

			case 6:
				phoneParser = new Regex(@"(\d{3})(\d{3})");
				format = "$1 $2";
				break;

			case 7:
				phoneParser = new Regex(@"(\d{3})(\d{3})(\d{1})");
				format = "$1 $2 $3";
				break;

			case 8:
				phoneParser = new Regex(@"(\d{3})(\d{3})(\d{2})");
				format = "$1 $2 $3";
				break;

			case 9:
				phoneParser = new Regex(@"(\d{3})(\d{3})(\d{3})");
				format = "$1 $2 $3";
				break;

			case 10:
				phoneParser = new Regex(@"(\d{3})(\d{3})(\d{2})(\d{2})");
				format = "($1) $2-$3-$4";
				break;

			default:
				return phoneNumber;

			}//switch

			return phoneParser.Replace(phoneNumber, format);

		}
    }
}