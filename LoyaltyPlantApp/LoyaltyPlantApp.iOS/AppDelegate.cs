using System;
using System.Linq;
using System.Threading.Tasks;
using AppsFlyerXamarinBinding;
using AdjustBindingsiOS;
using CoreLocation;
using Facebook.CoreKit;
using Google.SignIn;
using Foundation;
using LoyaltyPlant.Analytics;
using LoyaltyPlant.App.iOS;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.AppCenterHelper;
using LoyaltyPlant.Backend;
using LoyaltyPlant.Backend.iOS;
using LoyaltyPlant.Cities;
using LoyaltyPlant.Cities.iOS;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.iOS;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.CoreImpl.Cache;
using LoyaltyPlant.CoreImpl.Web;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.DigitalOrdering.iOS;
using LoyaltyPlant.Donation;
using LoyaltyPlant.Donation.iOS;
using LoyaltyPlant.Feedback;
using LoyaltyPlant.Feedback.iOS;
using LoyaltyPlant.Geofencing;
using LoyaltyPlant.Geofencing.iOS;
using LoyaltyPlant.Hints;
using LoyaltyPlant.Images;
using LoyaltyPlant.InviteFriend;
using LoyaltyPlant.InviteFriend.iOS;
using LoyaltyPlant.Languages;
using LoyaltyPlant.Languages.iOS;
using LoyaltyPlant.Legal;
using LoyaltyPlant.Legal.iOS;
using LoyaltyPlant.Likes;
using LoyaltyPlant.Likes.iOS;
using LoyaltyPlant.Loyalty;
using LoyaltyPlant.Loyalty.iOS;
using LoyaltyPlant.MakeCall;
using LoyaltyPlant.MakeCall.iOS;
using LoyaltyPlant.Maps;
using LoyaltyPlant.Maps.iOS;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Messages.iOS;
using LoyaltyPlant.Offer;
using LoyaltyPlant.Offer.iOS;
using LoyaltyPlant.OpenUrl;
using LoyaltyPlant.OpenUrl.iOS;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Partner.iOS;
using LoyaltyPlant.Payment;
using LoyaltyPlant.Payment.iOS;
using LoyaltyPlant.PromoCode;
using LoyaltyPlant.PushNotifications;
using LoyaltyPlant.Shared;
using LoyaltyPlant.Support;
using LoyaltyPlant.Support.iOS;
using LoyaltyPlant.Surveys;
using LoyaltyPlant.Surveys.iOS;
using LoyaltyPlant.Texts;
using LoyaltyPlant.TieredLoyalty;
using LoyaltyPlant.User;
using LoyaltyPlant.User.iOS;
using LoyaltyPlantApp.iOS.Implementations;
using Microsoft.AppCenter;
using Microsoft.AppCenter.Analytics;
using Microsoft.AppCenter.Crashes;
using UIKit;
using System.Diagnostics;
using ObjCRuntime;
#if !VK_PATCH
using VKontakte;
#endif
using Configuration = LoyaltyPlant.Core.Configuration;

namespace LoyaltyPlantApp.iOS
{
    // The UIApplicationDelegate for the application. This class is responsible for launching the 
    // User Interface of the application, as well as listening (and optionally responding) to 
    // application events from iOS.
    [Register("AppDelegate")]
    public class AppDelegate : UIApplicationDelegate
    {
#if DEBUG
        private const string AppCenterKey = "af82f0a2-c72e-45d7-bc65-ec46b18e03b9";
#elif ACCEPTANCE_TESTING
        private const string AppCenterKey = "c1a2aa0a-72ee-4eb6-be1d-18b4ffe51650";
#else // RELEASE
        private const string AppCenterKey = "58e25a17-e86a-490a-975c-c92eb5a7ce5a";
#endif

        public static readonly string TrackingId = "UA-48577393-1";
        const string MapsApiKey = "AIzaSyAnT6QbSauyEEbUuINRLOi-gnHqAXKYmQw";

        public static nfloat ScreenWidth = UIScreen.MainScreen.Bounds.Width;
        public static nfloat ScreenHeight = UIScreen.MainScreen.Bounds.Height;

        public static CLLocationManager LocationManager;

        private new UIWindow _window;
        private UINavigationController _navigationController;
        //
        // This method is invoked when the application has loaded and is ready to run. In this 
        // method you should instantiate the window, load the UI into it and then make the window
        // visible.
        //
        // You have 17 seconds to return from this method, or iOS will terminate your application.
        //
        public override bool FinishedLaunching(UIApplication app, NSDictionary options)
        {
            var didFinishLaunchingStartTime = DateTime.Now;
            Debug.WriteLine($"DEBUG | {DateTime.Now.ToString("dd.MM.yy HH:mm:ss.fff")} | App loaded into memory, DidFinishLaunching() is called.");
            AppCenter.Start(AppCenterKey, typeof(Crashes), typeof(Analytics));

            PreventLinkerFromStrippingCommonLocalizationReferences();

            InitColorsFromPlist();

            LocationManager = new CLLocationManager();

            var dict = new NSDictionary("Info.plist");
            Facebook.CoreKit.Settings.SharedSettings.AppId = dict["FacebookAppID"]?.ToString();
            Facebook.CoreKit.Settings.SharedSettings.DisplayName = dict["FacebookDisplayName"]?.ToString();
#if ACCEPTANCE_TESTING
            Facebook.CoreKit.Settings.SharedSettings.IsCodelessDebugLogEnabled = true;
#endif
            Firebase.Core.App.Configure();
            var isGoogleConfigured = !string.IsNullOrEmpty(Firebase.Core.App.DefaultInstance?.Options.ClientId);
            if (isGoogleConfigured)
                SignIn.SharedInstance.ClientId = Firebase.Core.App.DefaultInstance.Options.ClientId;

#if !VK_PATCH
            var vkAppId = dict["VkAppID"]?.ToString();
            VKSdk.Initialize(vkAppId);
#endif
            AppsFlyerLib.Shared.AppleAppID = dict["AppsFlyerAppID"]?.ToString();
            AppsFlyerLib.Shared.AppsFlyerDevKey = dict["AppsFlyerDevKey"]?.ToString();

            string adjustAppToken = dict["AdjustAppToken"]?.ToString();
#if ACCEPTANCE_TESTING
            string adjustEnvironment = AdjustConfig.EnvironmentSandbox;
#else
            string adjustEnvironment = AdjustConfig.EnvironmentProduction;
#endif
            var config = ADJConfig.ConfigWithAppToken(adjustAppToken, adjustEnvironment);

            Adjust.AppDidLaunch(config);

            _window = new UIWindow(UIScreen.MainScreen.Bounds);
            _window.MakeKeyAndVisible();
            _window.BackgroundColor = Colors.BackgroundColor;
            var width = Dimensions.Width;

            var startController = new StartViewController();
            _navigationController = new UINavigationController(startController);
            _navigationController.View.BackgroundColor = UIColor.Black;
            _navigationController.NavigationBarHidden = true;
            _window.RootViewController = _navigationController;

            InitEngine(AppConfig.GetAppConfig());

            RemoveNotifications();

            if (options != null)
                TryParseUniversalLink(options);
            
            Task.Run(BackendModule.Instance.StartApplication);

            var didFinishLaunchingExecutionTime = DateTime.Now - didFinishLaunchingStartTime;
            LpLogger.LOG_I($"DidFinishLaunching() finished. Took {didFinishLaunchingExecutionTime.Seconds} sec {didFinishLaunchingExecutionTime.Milliseconds} msec to complete.");
            if (!isGoogleConfigured)
                LpLogger.LOG_E("ALARM! Google is Not Configured! See https://confluence.loyaltyplant.com/pages/viewpage.action?pageId=23595809");
            return ApplicationDelegate.SharedInstance.FinishedLaunching(app, options);
        }

        public void InitEngine(Configuration config)
        {
            Debug.WriteLine($"DEBUG | {DateTime.Now.ToString("dd.MM.yy HH:mm:ss.fff")} | InitEngine() called.");
            var initEngineStartTime = DateTime.Now;

            var platform = new PlatformiOS();
# if ACCEPTANCE_TESTING
            var webController = new ChaosWebController(new LpWebController());
#else
            var webController = new LpWebController();
#endif
            Debug.WriteLine($"DEBUG | {DateTime.Now.ToString("dd.MM.yy HH:mm:ss.fff")} | InitEngine(): Initializing webController.");
            Engine.Initialize(webController, new LpCacheController(platform.CacheDirectory), config,
                platform, new IosLoggerSerialize());

            LpLogger.LOG_I("InitEngine(): Starting adding modules.");
            var addingModulesStartTime = DateTime.Now;

            Engine.Instance.AddModule(new BackendModule());
            Engine.Instance.AddModule(new ImagesModule(new ImageSizeiOS()));
            Engine.Instance.AddModule(new TextsModule());
            Engine.Instance.AddModule(new CitiesModule());
            Engine.Instance.AddModule(new ContentModule());
            Engine.Instance.AddModule(new DonationModule());
            Engine.Instance.AddModule(new InviteFriendModule());
            Engine.Instance.AddModule(new PromoCodeModule());
            Engine.Instance.AddModule(new LanguagesModule());
            Engine.Instance.AddModule(new LegalModule());
            Engine.Instance.AddModule(new LoyaltyModule());
            Engine.Instance.AddModule(new LikesModule());
            Engine.Instance.AddModule(new MakeCallModule());
            Engine.Instance.AddModule(new MapsModule());
            Engine.Instance.AddModule(new OfferModule());
            Engine.Instance.AddModule(new OpenUrlModule());
            Engine.Instance.AddModule(new MessagesModule(new AppTrackingService()));
            Engine.Instance.AddModule(new PartnerModule());
            Engine.Instance.AddModule(new SupportModule());
            Engine.Instance.AddModule(new SurveysModule());
            Engine.Instance.AddModule(new UserModule());
            Engine.Instance.AddModule(new PushNotificationsModule());
            Engine.Instance.AddModule(new GeofencingModule(new GeofenceCallback()));
            Engine.Instance.AddModule(new HintsModule());
            Engine.Instance.AddModule(new DigitalOrderingModule(new JsonCacheController(Engine.Instance.CacheController.Directory)));
            Engine.Instance.AddModule(new FeedbackModule(DigitalOrderingModule.Instance));
            Engine.Instance.AddModule(new PaymentModule());
            Engine.Instance.AddModule(new TieredLoyaltyModule());
            Engine.Instance.AddModule(new DoMenuModule());

            var analyticsModule = new AnalyticsModule();
            analyticsModule.Trackers.Add(new AppCenterTracker());
            analyticsModule.Trackers.Add(new FirebaseTracker());
            analyticsModule.Trackers.Add(new LpLoggerTracker());
            analyticsModule.Trackers.Add(new FacebookTracker());
            analyticsModule.Trackers.Add(new AppsFlyerTracker());
            analyticsModule.Trackers.Add(new AdjustTracker());

            //TODO закомменчено пока не настроен кабинет Adjust и не добавлены Events
            //После этого добавить обработку ивентов для iOS и по подобию сделать на Android
            //analyticsModule.Trackers.Add(new AdjustTracker());

            Engine.Instance.AddModule(analyticsModule);

            var addingModulesExecutionTime = DateTime.Now - addingModulesStartTime;
            LpLogger.LOG_I($"InitEngine(): Finished adding modules. Took {(int)addingModulesExecutionTime.TotalMilliseconds} msec to complete.");

            var messageFr = new MessagesFrontend(_window, _navigationController);
            var backFrontend = new BackendFrontend(_window, _navigationController);

            LpLogger.LOG_I("InitEngine(): Starting setting controllers.");
            var settingControllersStartTime = DateTime.Now;

            BackendModule.Instance.SetController(new BackendController(backFrontend));
            MessagesModule.Instance.SetController(new MessagesController(messageFr));

            CitiesModule.Instance.SetController(new CitiesController(new CitiesFrontend(_window, _navigationController)));
            ContentModule.Instance.SetController(new ContentController(new ContentFrontend(_window, _navigationController)));
            DonationModule.Instance.SetController(new DonationController(new DonationFrontend(_window, _navigationController)));
            FeedbackModule.Instance.SetController(new FeedbackController(new FeedbackFrontend(_window, _navigationController)));
            var inviteFriendPromoCodeFront = new InviteFriendFrontend(_window, _navigationController);
            InviteFriendModule.Instance.SetController(new InviteFriendController(inviteFriendPromoCodeFront));
            PromoCodeModule.Instance.SetController(new PromoCodeController(inviteFriendPromoCodeFront));
            LanguagesModule.Instance.SetController(new LanguagesController(new LanguagesFrontend(_window, _navigationController)));
            LegalModule.Instance.SetController(new LegalController(new LegalFrontend(_window, _navigationController)));
            LoyaltyModule.Instance.SetController(new LoyaltyController(new LoyaltyFrontend(_window, _navigationController)));
            LikesModule.Instance.SetController(new LikesController(new LikesFrontend(_window, _navigationController)));
            MakeCallModule.Instance.SetController(new MakeCallController(new MakeCallFrontend(_window, _navigationController)));
            MapsModule.Instance.SetController(new MapsController(new MapsFrontend(_window, _navigationController)));
            OfferModule.Instance.SetController(new OfferController(new OfferFrontend(_window, _navigationController)));
            OpenUrlModule.Instance.SetController(new OpenUrlController(new OpenUrlFrontend(_window, _navigationController)));
            PartnerModule.Instance.SetController(new PartnerController(new PartnerFrontend(_window, _navigationController)));
            SupportModule.Instance.SetController(new SupportController(new SupportFrontend(_window, _navigationController)));
            SurveysModule.Instance.SetController(new SurveysController(new SurveysFrontend(_window, _navigationController)));
            UserModule.Instance.SetController(new UserController(new UserFrontend(_window, _navigationController)));
            DigitalOrderingModule.Instance.SetController(new DigitalOrderingController(new DigitalOrderingFrontend(_window, _navigationController)));
            PaymentModule.Instance.SetController(new PaymentController(new PaymentFrontend(_window, _navigationController)));

#if ACCEPTANCE_TESTING
            backFrontend.OnShownDebugScreen += () =>
            {
                AppFrontend.ShowViewController(new DebugViewController());
            };

            messageFr.OnCreateMessage += (obj) =>
            {
                var view = (BaseMessageView)obj;
                var plainMessage = obj.Message;
                var numberOfTapsRequired = 0;

                var gestureRecognize = new UITapGestureRecognizer(() =>
                {
                    numberOfTapsRequired += 1;

                    if (numberOfTapsRequired > 8)
                    {
                        numberOfTapsRequired = 0;

                        MessagesModule.Instance.Controller?.DismissMessage(plainMessage);
                        AppFrontend.GetNavigationController().PopToRootViewController(false);
                        BackendModule.Instance.Controller.ShowDebugScreen();
                    }
                });
                view.AddGestureRecognizer(gestureRecognize);
            };
#endif
            var settingControllersExecutionTime = DateTime.Now - settingControllersStartTime;
            var initEngineExecutionTime = DateTime.Now - initEngineStartTime;
            LpLogger.LOG_I($"InitEngine(): Finished setting controllers. Took {(int)settingControllersExecutionTime.TotalMilliseconds} msec to complete.");
            LpLogger.LOG_I($"InitEngine() execution finished. Took {(int)initEngineExecutionTime.TotalMilliseconds} msec to complete.");
        }

        public override void OnActivated(UIApplication application)
        {
            LpLogger.LOG_I("AppDelegate.OnActivated()");

            AppEvents.Shared.ActivateApp();

#if ACCEPTANCE_TESTING
            AppsFlyerLib.Shared.IsDebug = true;
#endif
            AppsFlyerLib.Shared.SetHost("appsflyersdk.com", "");
            AppsFlyerLib.Shared.Start();

            MessagesModule.Instance.ShowNextMessage();
        }

        public override void DidEnterBackground(UIApplication application)
        {
            LpLogger.LOG_I("AppDelegate.DidEnterBackground()");
            BackendModule.Instance.Controller.OnApplicationDeactivated();

            RemoveNotificationsMethod();
        }

        public override void WillEnterForeground(UIApplication application)
        {
            LpLogger.LOG_I("AppDelegate.WillEnterForeground()");

            var isPushAvailable = UIApplication.SharedApplication.IsRegisteredForRemoteNotifications;
            var currentSettings = UIApplication.SharedApplication.CurrentUserNotificationSettings;
            LpLogger.LOG_W("[PUSH_STATUS] Current push notification status is: " + currentSettings.Types + " AND isPushAvailable = " + isPushAvailable);

            if (UIApplication.SharedApplication.ApplicationIconBadgeNumber > 0)
            {
                Engine.Instance.ResetLastSyncTime();
            }

            Engine.Instance.UpdateOrdersAndMainScreenIfNeeded(true);

            BackendModule.Instance.Controller.OnApplicationResumed();

            RemoveNotificationsMethod();

            if (_navigationController?.VisibleViewController is PaymentProcessingWebPage)
                _ = PaymentModule.Instance.CheckIfSbpCardWasAdded();
        }

        private void RemoveNotificationsMethod()
        {
            try
            {
                RemoveNotifications();
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E("RemoveNotifications exeception ", ex);
            }
        }

        public override void RegisteredForRemoteNotifications(UIApplication application, NSData deviceToken)
        {
            LpLogger.LOG_I("Regisering for apple push notifications...");
            //var stringDeviceToken = deviceToken.Description.Replace("<", "").Replace(">", "").Replace(" ", "");

            byte[] bytes = deviceToken.ToArray<byte>();
            string[] hexArray = bytes.Select(b => b.ToString("x2")).ToArray();
            var stringDeviceToken = string.Join(string.Empty, hexArray);

            if (string.IsNullOrEmpty(stringDeviceToken))
            {
                LpLogger.LOG_E("Registered for apple push notifications, but device token is null or empty! Will not pass to model");
                return;
            }
            LpLogger.LOG_I("Regisering for apple push notifications... DONE! DeviceToken = " + stringDeviceToken);
            PushNotificationsModule.Instance.DeviceToken = stringDeviceToken;
        }

        public override void FailedToRegisterForRemoteNotifications(UIApplication application, NSError error)
        {
            const int errorCodeForPushOnEmulator = 3010;
            if (error.Code == errorCodeForPushOnEmulator)
            {
                //на эмуляторе нет пушей, поэтому всегда вылетает ошибка с кодом 3010, её можно не писать в лог
                return;
            }

            const int errorCodeForApsEnvironmentNotFound = 3000;
            var isDebugConfig = Engine.Instance.Configuration.Debug;
            if (isDebugConfig && error.Code == errorCodeForApsEnvironmentNotFound)
            {
                LpLogger.LOG_I("Got ApsEnvironmentNotFound error, but we're on debug config, " +
                    "so it's OK. Error message just in case: " + error.Description);
                return;
            }
            LpLogger.LOG_E("Failed registering for apple push notification, reason: " + error.Description);
        }

        public override void ReceivedRemoteNotification(UIApplication application, NSDictionary userInfo)
        {
            try
            {
                var apsDictionary = (NSDictionary)userInfo["aps"];
                var message = apsDictionary["alert"].ToString();

                if (userInfo.ContainsKey((NSString)"type") && userInfo.ContainsKey((NSString)"push-id"))
                {
                    var type = (NSString)userInfo["type"];
                    var pushId = Convert.ToInt64((NSString)userInfo["push-id"]);
                    var ttl = (userInfo.ContainsKey((NSString)"ttl"))
                        ? Convert.ToInt64((NSString)userInfo["ttl"])
                        : 0;
                    var cardId = (userInfo.ContainsKey((NSString)"card-id"))
                        ? Convert.ToInt32((NSString)userInfo["card-id"])
                        : 0;
                    var img = (userInfo.ContainsKey((NSString)"img"))
                        ? (NSString)userInfo["img"].ToString()
                        : "";

                    _ = PushNotificationsModule.Instance.OnDetailedPushMessageArrived(message, type.ToString(), pushId, ttl, cardId, img);

                    if (UIApplication.SharedApplication.ApplicationState == UIApplicationState.Active)
                    {
                        PushNotificationsModule.Instance.PushMessageReceivedWhileAppOpened(message);
                        application.ApplicationIconBadgeNumber = 0;
                    }
                }
                else
                {
                    _ = PushNotificationsModule.Instance.OnPushMessageArrived(message);

                    if (UIApplication.SharedApplication.ApplicationState == UIApplicationState.Active)
                    {
                        PushNotificationsModule.Instance.PushMessageReceivedWhileAppOpened(message);
                        application.ApplicationIconBadgeNumber = 0;
                    }
                }
            }
            catch (Exception exception)
            {
                LpLogger.LOG_E("Error during recieving push notification", exception);
            }
        }

        public override void DidReceiveRemoteNotification(UIApplication application, NSDictionary userInfo, Action<UIBackgroundFetchResult> completionHandler)
        {
            ReceivedRemoteNotification(application, userInfo);
        }

        public override bool OpenUrl(UIApplication application, NSUrl url, string sourceApplication, NSObject annotation)
        {
            LpLogger.LOG_I($"AppDelegate.OpenUrl(): received url={url.AbsoluteString}");

            if (string.IsNullOrWhiteSpace(sourceApplication))
            {
                sourceApplication = NSBundle.MainBundle.BundleIdentifier;
            }
#if !VK_PATCH
            if (VKSdk.ProcessOpenUrl(url, sourceApplication) || url.AbsoluteString.StartsWith("vk"))
            {
                return true;
            }
#endif
            if (url.AbsoluteString.StartsWith("fb"))
            {
                LpLogger.LOG_I("AppDelegate.OpenUrl(): got the FB url, proceeding with FBSDK delegate OpenUrl() method.");
                return ApplicationDelegate.SharedInstance.OpenUrl(application, url, sourceApplication, annotation);
            }

            if (url.AbsoluteString.StartsWith("com.googleusercontent"))
            {
                LpLogger.LOG_I("AppDelegate.OpenUrl(): got the Google/Firebase url, proceeding with Google SignIn HandleUrl() method.");
                return SignIn.SharedInstance.HandleUrl(url);
            }

            // TODO google auth
            /*
            else if (url.AbsoluteString.StartsWith("com.googleusercontent"))
            {
                LpLogger.LOG_I("[GOOGLESIGNIN] User tries to sign in! Time: " + LpDateTime.Now.ToString("HH:mm:ss"));
                return SignIn.SharedInstance.HandleUrl(url, sourceApplication, annotation);
            }*/
            else
            {
                return base.OpenUrl(application, url, sourceApplication, annotation);
            }
        }

        public void RemoveNotifications()
        {
            LpLogger.LOG_I("AppDelegate.RemoveNotifications(), ApplicationIconBadgeNumber ==> 0, also CancelAllLocalNotifications()");
            UIApplication.SharedApplication.ApplicationIconBadgeNumber = 0;
            UIApplication.SharedApplication.CancelAllLocalNotifications();
        }

        public override void ReceiveMemoryWarning(UIApplication application)
        {
            AppFrontend.GetTopViewController()?.DidReceiveMemoryWarning();
        }

        private void InitColorsFromPlist()
        {
            Colors.AccentCardColor = Colors.GetColor("AccentCardColor");
            Colors.AccentCardTextColor = Colors.GetColor("AccentCardTextColor");
            Colors.AccentColor = Colors.GetColor("AccentColor");
            Colors.AccentGradientDownColor = Colors.GetColor("AccentGradientDownColor") ?? Colors.AccentColor;
            Colors.AccentGradientUpColor = Colors.GetColor("AccentGradientUpColor") ?? Colors.AccentColor;
            Colors.AccentPressedColor = Colors.GetColor("AccentPressedColor");
            Colors.AccentTextColor = Colors.GetColor("AccentTextColor");
            Colors.BackgroundColor = Colors.GetColor("BackgroundColor");
            Colors.BackgroundTextColor = Colors.GetColor("BackgroundTextColor");
            Colors.CardColor = Colors.GetColor("CardColor");
            Colors.CoinColor = Colors.GetColor("CoinColor");
            Colors.CardTextColor = Colors.GetColor("CardTextColor");
            Colors.DefaultTextColor = Colors.GetColor("DefaultTextColor");
            Colors.DividerTextColor = Colors.GetColor("DividerTextColor");
            Colors.HintTextColor = Colors.GetColor("HintTextColor");
            Colors.InfoLabelColor = Colors.GetColor("InfoLabelColor");
            Colors.InfoLabelTextColor = Colors.GetColor("InfoLabelTextColor");
            Colors.IphoneNavigationBarActionsColor = Colors.GetColor("IphoneNavigationBarActionsColor");
            Colors.NavigationDrawerTextColor = Colors.GetColor("NavigationDrawerTextColor");
            Colors.NavigationDrawerAccentColor = Colors.GetColor("NavigationDrawerAccentColor");
            Colors.NavigationDrawerColor = Colors.GetColor("NavigationDrawerColor");
            Colors.NavigationDrawerCopyrightTextColor = Colors.GetColor("NavigationDrawerCopyrightTextColor");
            Colors.MainScreenAllButtonColor = Colors.GetColor("MainScreenAllButtonColor");
            Colors.MainScreenDelimitersColor = Colors.GetColor("MainScreenDelimitersColor");
            Colors.MessageboxAccentTextColor = Colors.GetColor("MessageboxAccentTextColor");
            Colors.MainScreenAllButtonTextColor = Colors.GetColor("MainScreenAllButtonTextColor");
            Colors.MainBackgroundGradientUpColor = Colors.GetColor("MainBackgroundGradientUpColor") ?? Colors.BackgroundColor;
            Colors.MainBackgroundGradientDownColor = Colors.GetColor("MainBackgroundGradientDownColor") ?? Colors.BackgroundColor;
            Colors.MainScreenAllButtonPressedColor = Colors.GetColor("MainScreenAllButtonPressedColor");
            Colors.MessageboxAccentTextDisabledColor = Colors.GetColor("MessageboxAccentTextDisabledColor");
            Colors.MainScreenAllButtonGradientUpColor = Colors.GetColor("MainScreenAllButtonGradientUpColor") ?? Colors.MainScreenAllButtonColor;
            Colors.MainScreenSecondaryDelimitersColor = Colors.GetColor("MainScreenSecondaryDelimitersColor");
            Colors.MainScreenAllButtonGradientDownColor = Colors.GetColor("MainScreenAllButtonGradientDownColor") ?? Colors.MainScreenAllButtonColor;
            Colors.MainScreenSecondaryDelimitersTextColor = Colors.GetColor("MainScreenSecondaryDelimitersTextColor");
            Colors.PrimaryColor = Colors.GetColor("PrimaryColor");
            Colors.PriceLabelColor = Colors.GetColor("PriceLabelColor");
            Colors.PrimaryTextColor = Colors.GetColor("PrimaryTextColor");
            Colors.PriceLabelTextColor = Colors.GetColor("PriceLabelTextColor");
            Colors.PrimaryPressedColor = Colors.GetColor("PrimaryPressedColor");
            Colors.PrimaryGradientUpColor = Colors.GetColor("PrimaryGradientUpColor") ?? Colors.PrimaryColor;
            Colors.PrimaryGradientDownColor = Colors.GetColor("PrimaryGradientDownColor") ?? Colors.PrimaryColor;
            Colors.SecondaryTextColor = Colors.GetColor("SecondaryTextColor");
            Colors.SecondaryBackgroundColor = Colors.GetColor("SecondaryBackgroundColor");
            Colors.SecondaryBackgroundGradientUpColor = Colors.GetColor("SecondaryBackgroundGradientUpColor") ?? Colors.SecondaryBackgroundColor;
            Colors.SecondaryBackgroundGradientDownColor = Colors.GetColor("SecondaryBackgroundGradientDownColor") ?? Colors.SecondaryBackgroundColor;
        }

        // Запретить Linker-у удалять Localization References
        // https://github.com/xamarin/Xamarin.Forms/issues/4037
        private static void PreventLinkerFromStrippingCommonLocalizationReferences()
        {
            new System.Globalization.ChineseLunisolarCalendar();
            new System.Globalization.HebrewCalendar();
            new System.Globalization.HijriCalendar();
            new System.Globalization.JapaneseCalendar();
            new System.Globalization.JapaneseLunisolarCalendar();
            new System.Globalization.KoreanCalendar();
            new System.Globalization.KoreanLunisolarCalendar();
            new System.Globalization.PersianCalendar();
            new System.Globalization.TaiwanCalendar();
            new System.Globalization.TaiwanLunisolarCalendar();
            new System.Globalization.ThaiBuddhistCalendar();
            new System.Globalization.UmAlQuraCalendar();
        }

        //iOS дает 5 сек. чтобы завершить всю работу
        //не дружит с async/await
        public override void WillTerminate(UIApplication application)
        {
            if (IsCheckoutOrPaymentProcessing())
            {
                var terminateTask = Task.Run(() => WillTerminateApp());
                terminateTask.Wait();
            }
        }

        private async Task WillTerminateApp()
        {
            try
            {
                var orderId = DigitalOrderingModule.Instance.CurrentOrder?.OrderId;
                var clientId = Engine.Instance.Credentials.PublicId;
                LpLogger.LOG_I($"WillTerminateApp(): Request to cancel order {orderId} by client {clientId} from app when it's being terminated.");

                if (_navigationController.VisibleViewController is PaymentProcessingWebPage)
                    LpLogger.LOG_I("CancelOrder reason: app was closed on 3ds WebView page");

                else if (_navigationController.VisibleViewController is PassKit.PKPaymentAuthorizationViewController)
                    LpLogger.LOG_I("CancelOrder reason: app was closed while PKPaymentAuthorizationViewController being presented");

                else
                    LpLogger.LOG_I("CancelOrder reason: app was closed, VisibleViewController = " + _navigationController.VisibleViewController?.Title);

                await DigitalOrderingModule.Instance.CancelCurrentOrder();
            }
            catch (Exception ex)
            {
                LpLogger.LOG_E("WillTerminateApp exception = " + ex.ToString());
            }
        }

        private bool IsCheckoutOrPaymentProcessing()
        {
            if (_navigationController == null)
                return false;

            return _navigationController.VisibleViewController is PassKit.PKPaymentAuthorizationViewController ||
                   _navigationController.VisibleViewController is PaymentProcessingWebPage;
        }

        public override bool ContinueUserActivity(UIApplication application, NSUserActivity userActivity, UIApplicationRestorationHandler completionHandler)
        {
            LpLogger.LOG_I("AppDelegate.ContinueUserActivity(): Received userActivity with " +
                           $"type \"{userActivity.ActivityType}\" and webpageUrl={userActivity.WebPageUrl}");
            try
            {
                if (userActivity.ActivityType == NSUserActivityType.BrowsingWeb)
                {
                    var url = userActivity.WebPageUrl;
                    if (url == null)
                        return false;

                    return ParseUniversalLinkUrl(url);
                }
                
                LpLogger.LOG_I("AppDelegate.ContinueUserActivity(): userActivity is not WebPageUrl. Not going further.");
                return false;
            }
            catch (Exception e)
            {
                LpLogger.LOG_I($"AppDelegate.ContinueUserActivity(): Exception raised: {e.Message}");
                return false;
            }
        }

        private void TryParseUniversalLink(NSDictionary launchOptions)
        {
            LpLogger.LOG_I($"AppDelegate.TryParseUniversalLink: received launchOptions: {launchOptions}");

            if (launchOptions.ContainsKey(UIApplication.LaunchOptionsUserActivityDictionaryKey)
                && launchOptions[UIApplication.LaunchOptionsUserActivityDictionaryKey] is NSDictionary activityDict
                && activityDict.ContainsKey(UIApplication.LaunchOptionsUserActivityTypeKey)
                && activityDict[UIApplication.LaunchOptionsUserActivityTypeKey] as NSString == NSUserActivityType.BrowsingWeb)
            {
                var userActivity = activityDict["UIApplicationLaunchOptionsUserActivityKey"] as NSUserActivity;

                if (userActivity?.ActivityType == NSUserActivityType.BrowsingWeb && userActivity.WebPageUrl != null)
                {
                    LpLogger.LOG_I("AppDelegate.TryParseUniversalLink: received universal link. Trying to parse url. ");
                    ParseUniversalLinkUrl(userActivity.WebPageUrl);
                }
            }
            else
                LpLogger.LOG_I("AppDelegate.TryParseUniversalLink: received activity is not an universal link.");
        }

        private bool ParseUniversalLinkUrl(NSUrl url)
        {
            LpLogger.LOG_I($"AppDelegate.ParseUniversalLinkUrl(): Universal link redirect caught! URL: {url}.");
            var components = new NSUrlComponents(url, true);
            
            if (string.IsNullOrEmpty(components.Path)) 
            { 
                LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): URL Path is empty, nothing to parse."); 
                return false; 
            }
            
            if (components.Path.Contains("/ul/")) 
            { 
                LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): found \"/ul/\" part in URL Path, " +
                               "proceeding to the new smart landings flow.");
                BackendModule.Instance.SendSmartLandingRequest(url.AbsoluteString);
            }
            else
            {
                LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): there is no \"/ul/\" part in URL Path, " +
                               "proceeding to the old smart landings flow.");
                
                if (components.QueryItems == null)
                {
                    LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): QueryItems are empty, nothing to parse.");
                    return false;
                }
                
                var queryItems = components.QueryItems.ToDictionary(item => item.Name, item => item.Value);
                
                if (string.IsNullOrEmpty(components.Path))
                {
                    LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): URL Path is empty, nothing to parse.");
                    return false;
                }
                
                AnalyticsModule.Instance.ParseUtmParametersIfNeeded(queryItems, false);

                if (components.Path.Contains("qr-mall-order"))
                {
                    LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): Got the `qr-mall-order` link.");
                    return DigitalOrderingModule.Instance.ParseInitialActionIfNeeded(components.Path, queryItems);
                }

                if (components.Path.Contains("promocode"))
                {
                    LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): Got the `promocode` link.");
                    return PromoCodeModule.Instance.ParseInitialActionIfNeeded(queryItems);
                }
                
                if (components.Path.Contains("lp/"))
                {
                    LpLogger.LOG_I("AppDelegate.ParseUniversalLinkUrl(): Got the `lp` link.");
                    return BackendModule.Instance.ParseInitialActionIfNeeded(queryItems);
                }
            }
            
            return true;
        }
    }
}
