<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.props" Condition="Exists('..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.props')" />
  <Import Project="..\..\LoyaltyPlant.Shared\LoyaltyPlant.Shared.projitems" Label="Shared" Condition="Exists('..\..\LoyaltyPlant.Shared\LoyaltyPlant.Shared.projitems')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">iPhoneSimulator</Platform>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{C298EE3C-C376-410B-AAD7-77E512369A71}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>LoyaltyPlantApp.iOS</RootNamespace>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
    <AssemblyName>LoyaltyPlantApp</AssemblyName>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <MtouchI18n>mideast</MtouchI18n>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhoneSimulator' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhoneSimulator\Debug</OutputPath>
    <DefineConstants>DEBUG;__UNIFIED__;__MOBILE__;__IOS__;IPHONE;ACCEPTANCE_TESTING;VK_PATCH</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <MtouchArch>x86_64</MtouchArch>
    <MtouchLink>Full</MtouchLink>
    <MtouchDebug>true</MtouchDebug>
    <MtouchProfiling>false</MtouchProfiling>
    <CodesignKey>iPhone Developer</CodesignKey>
    <CodesignProvision>
    </CodesignProvision>
    <CodesignEntitlements>
    </CodesignEntitlements>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhone' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhone\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchArch>ARM64</MtouchArch>
    <ConsolePause>false</ConsolePause>
    <CodesignKey>iPhone Distribution</CodesignKey>
    <DefineConstants>__UNIFIED__;__MOBILE__;__IOS__;IPHONE</DefineConstants>
    <BuildIpa>True</BuildIpa>
    <IpaMetadata>Entitlements.plist</IpaMetadata>
    <IpaPackageName>LoyaltyPlantApp.ipa</IpaPackageName>
    <MtouchLink>Full</MtouchLink>
    <MtouchUseLlvm>true</MtouchUseLlvm>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|iPhoneSimulator' ">
    <Optimize>true</Optimize>
    <OutputPath>bin\iPhoneSimulator\Release</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <MtouchArch>i386</MtouchArch>
    <ConsolePause>false</ConsolePause>
    <MtouchLink>None</MtouchLink>
    <DefineConstants>__UNIFIED__;__MOBILE__;__IOS__;IPHONE</DefineConstants>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <CodesignKey>iPhone Developer</CodesignKey>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|iPhone' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\iPhone\Debug</OutputPath>
    <DefineConstants>DEBUG;__UNIFIED__;__MOBILE__;__IOS__;IPHONE;ACCEPTANCE_TESTING</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <MtouchArch>ARM64</MtouchArch>
    <CodesignEntitlements>Entitlements.plist</CodesignEntitlements>
    <MtouchProfiling>false</MtouchProfiling>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>True</MtouchDebug>
    <MtouchLink>Full</MtouchLink>
    <MtouchOptimizePNGs>True</MtouchOptimizePNGs>
    <IpaMetadata>Entitlements.plist</IpaMetadata>
    <IpaPackageName>LoyaltyPlantApp.ipa</IpaPackageName>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
  </PropertyGroup>
  <Choose>
      <When Condition="$(DefineConstants.Contains('VK_PATCH'))" />
      <Otherwise>
          <ItemGroup>
              <Reference Include="Vkontakte.iOS">
                  <HintPath>Libraries\Vkontakte.iOS.dll</HintPath>
              </Reference>
          </ItemGroup>
      </Otherwise>
  </Choose>
  <ItemGroup>
    <Reference Include="AspectInjector.Broker, Version=*******, Culture=neutral, PublicKeyToken=a29e12442a3d3609, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AspectInjector.2.8.1\lib\netstandard2.0\AspectInjector.Broker.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\..\packages\BouncyCastle.NetCore.1.8.5\lib\xamarinios\BouncyCastle.Crypto.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Facebook.CoreKit, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Digifais.Xamarin.Facebook.CoreKit.iOS.16.1.2\lib\xamarinios10\Facebook.CoreKit.dll</HintPath>
    </Reference>
    <Reference Include="Facebook.LoginKit, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Digifais.Xamarin.Facebook.LoginKit.iOS.16.1.2\lib\xamarinios10\Facebook.LoginKit.dll</HintPath>
    </Reference>
    <Reference Include="Facebook.ShareKit, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Digifais.Xamarin.Facebook.ShareKit.iOS.16.1.2\lib\xamarinios10\Facebook.ShareKit.dll</HintPath>
    </Reference>
    <Reference Include="FFImageLoading, Version=2.4.11.982, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.2.4.11.982\lib\Xamarin.iOS10\FFImageLoading.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FFImageLoading.Platform, Version=2.4.11.982, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\..\packages\Xamarin.FFImageLoading.2.4.11.982\lib\Xamarin.iOS10\FFImageLoading.Platform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Firebase.Analytics, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.iOS.Analytics.8.10.0.3\lib\xamarinios10\Firebase.Analytics.dll</HintPath>
    </Reference>
    <Reference Include="Firebase.Installations, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Firebase.iOS.Installations.8.10.0.3\lib\xamarinios10\Firebase.Installations.dll</HintPath>
    </Reference>
    <Reference Include="Firebase.InstanceID, Version=*******, Culture=neutral, PublicKeyToken=null">
      <HintPath>..\..\packages\Xamarin.Firebase.iOS.InstanceID.4.8.0\lib\xamarinios10\Firebase.InstanceID.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.Analytics, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.iOS.Analytics.3.20.0.2\lib\xamarinios10\Google.Analytics.dll</HintPath>
    </Reference>
    <Reference Include="Google.TagManager, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Google.iOS.TagManager.7.4.0.2\lib\xamarinios10\Google.TagManager.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.3\lib\netstandard2.0\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OpenTK-1.0" />
    <Reference Include="System.Drawing.Common.dll" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors" />
    <Reference Include="Xamarin.Facebook.GamingServicesKit, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Digifais.Xamarin.Facebook.GamingServicesKit.iOS.16.1.2\lib\xamarinios10\Xamarin.Facebook.GamingServicesKit.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.iOS" />
    <Reference Include="Stripe">
      <HintPath>Libraries\Stripe.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="RestSharp">
      <HintPath>..\..\packages\RestSharp.106.1.0\lib\netstandard2.0\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Stripe">
      <HintPath>Libraries\Stripe.dll</HintPath>
    </Reference>
    <Reference Include="WebP.Touch">
      <HintPath>..\..\packages\WebP.Touch.1.0.8\lib\Xamarin.iOS10\WebP.Touch.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="Microsoft.AppCenter">
      <HintPath>..\..\packages\Microsoft.AppCenter.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.iOS.Bindings">
      <HintPath>..\..\packages\Microsoft.AppCenter.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.iOS.Bindings.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Crashes">
      <HintPath>..\..\packages\Microsoft.AppCenter.Crashes.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.Crashes.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Crashes.iOS.Bindings">
      <HintPath>..\..\packages\Microsoft.AppCenter.Crashes.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.Crashes.iOS.Bindings.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Analytics">
      <HintPath>..\..\packages\Microsoft.AppCenter.Analytics.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.Analytics.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AppCenter.Analytics.iOS.Bindings">
      <HintPath>..\..\packages\Microsoft.AppCenter.Analytics.3.2.1\lib\Xamarin.iOS10\Microsoft.AppCenter.Analytics.iOS.Bindings.dll</HintPath>
    </Reference>
    <Reference Include="Lottie.iOS">
      <HintPath>Libraries\Lottie.iOS.dll</HintPath>
    </Reference>
    <Reference Include="Xamarin.Essentials">
      <HintPath>..\..\packages\Xamarin.Essentials.1.6.0\lib\xamarinios10\Xamarin.Essentials.dll</HintPath>
    </Reference>
    <Reference Include="ZXing.Net.Mobile.Core">
      <HintPath>..\..\packages\ZXing.Net.Mobile.2.4.1\lib\Xamarin.iOS10\ZXing.Net.Mobile.Core.dll</HintPath>
    </Reference>
    <Reference Include="zxing.portable">
      <HintPath>..\..\packages\ZXing.Net.Mobile.2.4.1\lib\Xamarin.iOS10\zxing.portable.dll</HintPath>
    </Reference>
    <Reference Include="ZXingNetMobile">
      <HintPath>..\..\packages\ZXing.Net.Mobile.2.4.1\lib\Xamarin.iOS10\ZXingNetMobile.dll</HintPath>
    </Reference>
    <Reference Include="Naxam.BrainTreeCore.iOS">
      <HintPath>..\..\packages\Naxam.BrainTreeCore.iOS.4.37.0\lib\Xamarin.iOS10\Naxam.BrainTreeCore.iOS.dll</HintPath>
    </Reference>
    <Reference Include="Naxam.BraintreeApplePay.iOS">
      <HintPath>..\..\packages\Naxam.BraintreeApplePay.iOS.4.37.0\lib\Xamarin.iOS10\Naxam.BraintreeApplePay.iOS.dll</HintPath>
    </Reference>
	    <Reference Include="AppsFlyerXamarinBinding">
	      <HintPath>..\..\packages\AppsFlyerXamarinBinding.6.5.4\lib\xamarinios10\AppsFlyerXamarinBinding.dll</HintPath>
	    </Reference>
	    <Reference Include="Firebase.Core">
	      <HintPath>..\..\packages\Xamarin.Firebase.iOS.Core.8.10.0.3\lib\xamarinios10\Firebase.Core.dll</HintPath>
	    </Reference>
	    <Reference Include="Google.SignIn">
	      <HintPath>..\..\packages\Xamarin.Google.iOS.SignIn.5.0.2.4\lib\xamarinios10\Google.SignIn.dll</HintPath>
	    </Reference>
	    <Reference Include="AdjustSdk.Xamarin.iOS">
	      <HintPath>..\..\packages\AdjustSdk.Xamarin.iOS.4.29.0\lib\xamarinios10\AdjustSdk.Xamarin.iOS.dll</HintPath>
	    </Reference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Info.plist" />
    <None Include="packages.config" />
    <BundleResource Include="PrivacyInfo.xcprivacy" LogicalName="PrivacyInfo.xcprivacy" />
    <BundleResource Include="Resources\Animations\Achievements\yellow_ticket_animation.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2198\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2198\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2198\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2198\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2394\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2394\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2394\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2394\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2731\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2731\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2731\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2731\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2732\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2732\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2732\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\2732\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3249\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3249\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3249\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3249\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3299\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3299\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3299\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3299\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3307\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3307\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3307\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3307\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3330\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3330\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3330\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3330\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3334\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3334\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3334\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3334\silver_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3340\bronze_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3340\gold_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3340\platinum_upgrade.json" />
    <BundleResource Include="Resources\Animations\TieredLoyaltyV2\3340\silver_upgrade.json" />
    <BundleResource Include="Resources\Base.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\Base.lproj\Localizable.strings" />
    <BundleResource Include="Resources\lock_icon.png" />
    <BundleResource Include="Resources\navigation_font.ttf" />
    <BundleResource Include="Entitlements.plist" />
    <BundleResource Include="Resources\OrderStatuses\eat_in_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\eat_in_unfill.png" />
    <BundleResource Include="Resources\PaymentProcessing\androidpay.png" />
    <BundleResource Include="Resources\PaymentProcessing\card.png" />
    <BundleResource Include="Resources\PaymentProcessing\cash.png" />
    <BundleResource Include="Resources\PaymentProcessing\applepay.png" />
    <BundleResource Include="Resources\PaymentProcessing\plus.png" />
    <BundleResource Include="Resources\google_logo.png" />
    <BundleResource Include="Resources\PaymentProcessing\sbp_icon.png" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\1.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\2.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\3.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\4.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\5.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\6.jpg" />
    <BundleResource Include="Resources\Slideshow\DefaultSlideshow\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ar\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-az\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-bg\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-cs\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-de\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-en\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-es\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fi\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-fr\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-he\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hu\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-hy\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ka\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-kk\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ky\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lt\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-lv\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-mo\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-nl\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-pl\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-ru\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-sk\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uk\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-uz\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-vi\6_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\1.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\1_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\2.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\2_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\3.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\3_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\4.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\4_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\5.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\5_568.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\6.jpg" />
    <BundleResource Include="Resources\Slideshow\Slideshow-zh\6_568.jpg" />
    <BundleResource Include="Resources\Social\google.png" />
    <BundleResource Include="Resources\Animations\pointsAnimation.json" />
    <BundleResource Include="Resources\placeholder.jpg" />
    <BundleResource Include="Resources\Animations\spinnerTick.json" />
    <BundleResource Include="Resources\Animations\spinnerCross.json" />
    <BundleResource Include="Resources\Animations\spinner.json" />
    <BundleResource Include="Resources\Animations\feedbackStarAppear.json" />
    <BundleResource Include="Resources\Animations\feedbackStarFill.json" />
    <BundleResource Include="Resources\Animations\feedbackStarUnfill.json" />
    <BundleResource Include="Resources\Animations\searchingLoading.json" />
    <BundleResource Include="Resources\cross.png" />
    <BundleResource Include="Resources\cross%402x.png" />
    <BundleResource Include="Resources\Left.png" />
    <BundleResource Include="Resources\Left%402x.png" />
    <BundleResource Include="Resources\Location.png" />
    <BundleResource Include="Resources\Location%402x.png" />
    <BundleResource Include="Resources\map_marker.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2198\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2198\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2198\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2198\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2394\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2394\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2394\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2394\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2732\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2732\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2732\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\2732\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3249\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3249\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3249\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3249\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3299\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3299\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3299\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3299\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3307\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3307\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3307\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3307\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3330\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3330\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3330\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3330\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3334\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3334\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3334\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3334\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3340\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3340\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3340\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\3340\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\bronze.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\bronze_active.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\bronze_inactive.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\gold.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\gold_active.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\gold_inactive.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\platinum.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\platinum_active.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\platinum_inactive.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\silver.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\silver_active.png" />
    <BundleResource Include="Resources\TieredLoyaltyV2\silver_inactive.png" />
    <BundleResource Include="Resources\unchecked_checkbox.png" />
    <BundleResource Include="Resources\unchecked_checkbox%402x.png" />
    <BundleResource Include="Resources\checked_checkbox.png" />
    <BundleResource Include="Resources\checked_checkbox%402x.png" />
    <BundleResource Include="Resources\Animations\tieredLoyalty.json" />
    <BundleResource Include="Resources\subscription_icon.png" />
    <BundleResource Include="Resources\subscription_icon%402x.png" />
    <BundleResource Include="Resources\PaymentProcessing\mada.png" />
    <BundleResource Include="Resources\copy_icon%403x.png" />
    <BundleResource Include="Resources\copy_icon%402x.png" />
    <BundleResource Include="Resources\copy_icon.png" />
    <BundleResource Include="Resources\OrderStatuses\awaiting_delivery_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\awaiting_delivery_unfill.png" />
    <BundleResource Include="Resources\OrderStatuses\completed_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\completed_unfill.png" />
    <BundleResource Include="Resources\OrderStatuses\cooking_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\cooking_unfill.png" />
    <BundleResource Include="Resources\OrderStatuses\unconfirmed_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\awaiting_pickup_fill.png" />
    <BundleResource Include="Resources\OrderStatuses\awaiting_pickup_unfill.png" />
    <BundleResource Include="GoogleService-Info.plist" />
    <BundleResource Include="Resources\PaymentProcessing\meeza.png" />
    <BundleResource Include="Resources\Authorization\120_google.png" />
    <BundleResource Include="Resources\Authorization\120_email.png" />
    <BundleResource Include="Resources\Authorization\120_apple.png" />
    <BundleResource Include="Resources\Authorization\120_keep.png" />
    <BundleResource Include="Resources\Authorization\120_facebook.png" />
    <BundleResource Include="Resources\Authorization\120_get_rewards.png" />
    <BundleResource Include="Resources\Authorization\120_vk.png" />
    <BundleResource Include="Resources\Authorization\120_phone.png" />
    <BundleResource Include="Resources\PaymentProcessing\online_payment.png" />
  </ItemGroup>
  <ItemGroup Label="Multibrand_markers">
    <InterfaceDefinition Include="Resources\LaunchScreen.storyboard" />
  </ItemGroup>
  <ItemGroup Label="Resources">
    <BundleResource Include="Resources\uk.lproj\Localizable.strings" />
    <BundleResource Include="Resources\uk.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\pl.lproj\Localizable.strings" />
    <BundleResource Include="Resources\pl.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\ru.lproj\Localizable.strings" />
    <BundleResource Include="Resources\ru.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\bg.lproj\Localizable.strings" />
    <BundleResource Include="Resources\bg.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\de.lproj\Localizable.strings" />
    <BundleResource Include="Resources\de.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\en.lproj\Localizable.strings" />
    <BundleResource Include="Resources\en.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\fi.lproj\Localizable.strings" />
    <BundleResource Include="Resources\fi.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\fr.lproj\Localizable.strings" />
    <BundleResource Include="Resources\fr.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\hu.lproj\Localizable.strings" />
    <BundleResource Include="Resources\hu.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\ka.lproj\Localizable.strings" />
    <BundleResource Include="Resources\ka.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\kk.lproj\Localizable.strings" />
    <BundleResource Include="Resources\kk.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\ky.lproj\Localizable.strings" />
    <BundleResource Include="Resources\ky.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\lt.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\lt.lproj\Localizable.strings" />
    <BundleResource Include="Resources\lv.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\lv.lproj\Localizable.strings" />
    <BundleResource Include="Resources\ar.lproj\Localizable.strings" />
    <BundleResource Include="Resources\ar.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\az.lproj\Localizable.strings" />
    <BundleResource Include="Resources\az.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\uz.lproj\Localizable.strings" />
    <BundleResource Include="Resources\uz.lproj\InfoPlist.strings" />
    <BundleResource Include="Resources\zh.lproj\Localizable.strings" />
    <BundleResource Include="Resources\zh.lproj\InfoPlist.strings" />
  </ItemGroup>
  <ItemGroup>
    <LinkDescription Include="LinkerSetup.xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DebugViewController.cs" />
    <Compile Include="Implementations\IosLoggerSerialize.cs" />
    <Compile Include="Implementations\LpFrontendUtils.cs" />
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <Compile Include="PlatformiOS.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Implementations\ImageSizeiOS.cs" />
    <Compile Include="Implementations\FirebaseTracker.cs" />
    <Compile Include="Implementations\TrackingService.cs" />
    <Compile Include="Implementations\FacebookTracker.cs" />
    <Compile Include="Extensions\IosExtension.cs" />
    <Compile Include="Implementations\AppsFlyerTracker.cs" />
    <Compile Include="Implementations\AdjustTracker.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\maestro.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\mastercard.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\pattern.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\pcidss.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\pic_cvv.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\visa.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\Colors.plist" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\external_registration_header.jpg" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\featured_graphic.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\header.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\marker.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\marker%402x.png" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
  <ItemGroup>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-568h%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-667h%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Contents.json">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-60%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-Small.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-Small%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-Small-40%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-Small%403x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-60%403x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\Icon-Small-40%403x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-736h%403x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-812h%403x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait-744w-1133h%402x~ipad.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait-834w-1112h%402x~ipad.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait-1024w-1366h%402x~ipad.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait-1194h%402x.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait%402x~ipad.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\LaunchImage.launchimage\Default-Portrait~ipad.png">
      <Visible>false</Visible>
    </ImageAsset>
    <ImageAsset Include="Resources\Images.xcassets\AppIcons.appiconset\iTunesArtwork1024.png">
      <Visible>false</Visible>
    </ImageAsset>
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\uatp.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\investpayment.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\discover.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\dinnerclub.png" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\PaymentProcessing\americanexpress.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.AppCenterHelper\LoyaltyPlant.AppCenterHelper.csproj">
      <Project>{969d1901-bf97-4cbe-acdf-be75719a1032}</Project>
      <Name>LoyaltyPlant.AppCenterHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Backend\LoyaltyPlant.Backend\LoyaltyPlant.Backend.csproj">
      <Project>{02A93C6B-4927-426C-8692-82EEE871BFA7}</Project>
      <Name>LoyaltyPlant.Backend</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.Analytics\LoyaltyPlant.Analytics.csproj">
      <Project>{0F5C1967-C374-4636-ABD0-B3F22C245DD6}</Project>
      <Name>LoyaltyPlant.Analytics</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Base\LoyaltyPlant.CoreImpl\LoyaltyPlant.CoreImpl.csproj">
      <Project>{29615200-19ef-467a-9f88-9445ed9725c2}</Project>
      <Name>LoyaltyPlant.CoreImpl</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Cities\LoyaltyPlant.Cities\LoyaltyPlant.Cities.csproj">
      <Project>{846C0D99-7037-4E02-9A33-D54361927F97}</Project>
      <Name>LoyaltyPlant.Cities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Content\LoyaltyPlant.Content\LoyaltyPlant.Content.csproj">
      <Project>{0ADCB3E5-86B7-4248-978F-3CD4092ABF87}</Project>
      <Name>LoyaltyPlant.Content</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Donation\LoyaltyPlant.Donation\LoyaltyPlant.Donation.csproj">
      <Project>{D400D214-9520-4F79-8868-A33848303E6B}</Project>
      <Name>LoyaltyPlant.Donation</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Feedback\LoyaltyPlant.Feedback\LoyaltyPlant.Feedback.csproj">
      <Project>{750F86F0-2D0C-4686-ADA8-14F079CAB1BB}</Project>
      <Name>LoyaltyPlant.Feedback</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Geofencing\LoyaltyPlant.Geofencing\LoyaltyPlant.Geofencing.csproj">
      <Project>{E871D4CF-974E-49AD-ACA4-5CD94E74D217}</Project>
      <Name>LoyaltyPlant.Geofencing</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Images\LoyaltyPlant.Images.csproj">
      <Project>{D96B4A6A-B10C-4C24-A491-78D63B7CE5BF}</Project>
      <Name>LoyaltyPlant.Images</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\InviteFriend\LoyaltyPlant.InviteFriend\LoyaltyPlant.InviteFriend.csproj">
      <Project>{375876EE-F618-4F0E-8FD7-1B7747B78E0D}</Project>
      <Name>LoyaltyPlant.InviteFriend</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Languages\LoyaltyPlant.Languages\LoyaltyPlant.Languages.csproj">
      <Project>{A684CD2F-2EB6-4497-ADFF-4FEC041F4427}</Project>
      <Name>LoyaltyPlant.Languages</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Legal\LoyaltyPlant.Legal\LoyaltyPlant.Legal.csproj">
      <Project>{8D615F48-C1E2-402F-AD1A-071F65BCAC5A}</Project>
      <Name>LoyaltyPlant.Legal</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Likes\LoyaltyPlant.Likes\LoyaltyPlant.Likes.csproj">
      <Project>{CFC1628E-4B1F-4CBB-9366-12D2DF4ADAAA}</Project>
      <Name>LoyaltyPlant.Likes</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Loyalty\LoyaltyPlant.Loyalty\LoyaltyPlant.Loyalty.csproj">
      <Project>{31BDA297-7F3E-4346-9543-3E595EDA825D}</Project>
      <Name>LoyaltyPlant.Loyalty</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\MakeCall\LoyaltyPlant.MakeCall\LoyaltyPlant.MakeCall.csproj">
      <Project>{5ADA34B3-0172-431B-97D4-F95E959BD181}</Project>
      <Name>LoyaltyPlant.MakeCall</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Maps\LoyaltyPlant.Maps\LoyaltyPlant.Maps.csproj">
      <Project>{02DA20A9-9A65-48C6-BD5F-A57E235B3043}</Project>
      <Name>LoyaltyPlant.Maps</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Messages\LoyaltyPlant.Messages\LoyaltyPlant.Messages.csproj">
      <Project>{2910A413-6F31-49FE-B86F-F5D0C002EFCD}</Project>
      <Name>LoyaltyPlant.Messages</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Offer\LoyaltyPlant.Offer\LoyaltyPlant.Offer.csproj">
      <Project>{DA2869D3-DD30-419E-AF07-AADD86032416}</Project>
      <Name>LoyaltyPlant.Offer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\OpenUrl\LoyaltyPlant.OpenUrl\LoyaltyPlant.OpenUrl.csproj">
      <Project>{B5C27391-92D8-45FA-BF2B-AEE8C65E679D}</Project>
      <Name>LoyaltyPlant.OpenUrl</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Partner\LoyaltyPlant.Partner\LoyaltyPlant.Partner.csproj">
      <Project>{707FFD41-EFFE-4215-92C5-21CE81F4BF0C}</Project>
      <Name>LoyaltyPlant.Partner</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Payment\LoyaltyPlant.Payment\LoyaltyPlant.Payment.csproj">
      <Project>{0E25585D-4803-4343-A187-85F90B07B43B}</Project>
      <Name>LoyaltyPlant.Payment</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\PromoCode\LoyaltyPlant.PromoCode\LoyaltyPlant.PromoCode.csproj">
      <Project>{9997117e-8e3d-4491-b7ba-5e4a13d29623}</Project>
      <Name>LoyaltyPlant.PromoCode</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\PushNotifications\LoyaltyPlant.PushNotifications\LoyaltyPlant.PushNotifications.csproj">
      <Project>{870323D1-1076-46BE-9001-0DCDF9601FF1}</Project>
      <Name>LoyaltyPlant.PushNotifications</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Support\LoyaltyPlant.Support\LoyaltyPlant.Support.csproj">
      <Project>{362D4263-8E1E-407B-A3E6-4F186312F4A3}</Project>
      <Name>LoyaltyPlant.Support</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Surveys\LoyaltyPlant.Surveys\LoyaltyPlant.Surveys.csproj">
      <Project>{48A5794D-EDEB-4539-BB94-D9829CB4754F}</Project>
      <Name>LoyaltyPlant.Surveys</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Texts\LoyaltyPlant.Texts.csproj">
      <Project>{2E749BDD-98E9-4B1E-BC7F-CBF006C8A453}</Project>
      <Name>LoyaltyPlant.Texts</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\User\LoyaltyPlant.User\LoyaltyPlant.User.csproj">
      <Project>{0C639F1F-0CD1-42E7-8C0A-BFFD880D07CD}</Project>
      <Name>LoyaltyPlant.User</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.View\LoyaltyPlant.View.csproj">
      <Project>{05E6F7F8-997D-47A7-8C14-D3583384B80B}</Project>
      <Name>LoyaltyPlant.View</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Backend\LoyaltyPlant.App.iOS\LoyaltyPlant.App.iOS.csproj">
      <Project>{E0FBDB2B-2A29-4997-8348-7E57B906F605}</Project>
      <Name>LoyaltyPlant.App.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Backend\LoyaltyPlant.Backend.iOS\LoyaltyPlant.Backend.iOS.csproj">
      <Project>{D28056F8-7123-43FF-B50E-F63D1514EF33}</Project>
      <Name>LoyaltyPlant.Backend.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Cities\LoyaltyPlant.Cities.iOS\LoyaltyPlant.Cities.iOS.csproj">
      <Project>{5A0EA738-F937-4412-83AE-040FEC2DC0EF}</Project>
      <Name>LoyaltyPlant.Cities.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\DigitalOrdering\LoyaltyPlant.DigitalOrdering\LoyaltyPlant.DigitalOrdering.csproj">
      <Project>{40A59D30-0733-4D2E-AD0E-1EB7005AC4D9}</Project>
      <Name>LoyaltyPlant.DigitalOrdering</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Donation\LoyaltyPlant.Donation.iOS\LoyaltyPlant.Donation.iOS.csproj">
      <Project>{F6CC371B-7D2E-4AC5-A5C3-FD5833B93570}</Project>
      <Name>LoyaltyPlant.Donation.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Feedback\LoyaltyPlant.Feedback.iOS\LoyaltyPlant.Feedback.iOS.csproj">
      <Project>{84B21557-6589-4F74-B5AB-06EA041EA512}</Project>
      <Name>LoyaltyPlant.Feedback.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\InviteFriend\LoyaltyPlant.InviteFriend.iOS\LoyaltyPlant.InviteFriend.iOS.csproj">
      <Project>{8F81CE6A-5B26-4CF6-84B1-FC3557DC91C0}</Project>
      <Name>LoyaltyPlant.InviteFriend.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Languages\LoyaltyPlant.Languages.iOS\LoyaltyPlant.Languages.iOS.csproj">
      <Project>{C4F61991-4633-496F-8775-60EB293CB6C2}</Project>
      <Name>LoyaltyPlant.Languages.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Legal\LoyaltyPlant.Legal.iOS\LoyaltyPlant.Legal.iOS.csproj">
      <Project>{F35641B6-1266-475C-853F-E183023A2FF2}</Project>
      <Name>LoyaltyPlant.Legal.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Likes\LoyaltyPlant.Likes.iOS\LoyaltyPlant.Likes.iOS.csproj">
      <Project>{23396969-33DA-43A5-A59A-6F8984345718}</Project>
      <Name>LoyaltyPlant.Likes.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Loyalty\LoyaltyPlant.Loyalty.iOS\LoyaltyPlant.Loyalty.iOS.csproj">
      <Project>{9B649EBC-A640-4A4F-8D58-F8BB97A26ABF}</Project>
      <Name>LoyaltyPlant.Loyalty.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\MakeCall\LoyaltyPlant.MakeCall.iOS\LoyaltyPlant.MakeCall.iOS.csproj">
      <Project>{6AEA3F7C-552E-47BD-B1FE-5C55F96F14C8}</Project>
      <Name>LoyaltyPlant.MakeCall.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Maps\LoyaltyPlant.Maps.iOS\LoyaltyPlant.Maps.iOS.csproj">
      <Project>{7BB870DD-697A-4700-875E-B1B30D37647E}</Project>
      <Name>LoyaltyPlant.Maps.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Messages\LoyaltyPlant.Messages.iOS\LoyaltyPlant.Messages.iOS.csproj">
      <Project>{CC224659-F866-40BB-AEE3-4B7E9904C043}</Project>
      <Name>LoyaltyPlant.Messages.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Offer\LoyaltyPlant.Offer.iOS\LoyaltyPlant.Offer.iOS.csproj">
      <Project>{BCA91BB4-F525-4898-A1B4-18499E22A2AD}</Project>
      <Name>LoyaltyPlant.Offer.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\OpenUrl\LoyaltyPlant.OpenUrl.iOS\LoyaltyPlant.OpenUrl.iOS.csproj">
      <Project>{C72EB761-DBA4-44DD-BBFF-B3299699131A}</Project>
      <Name>LoyaltyPlant.OpenUrl.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Partner\LoyaltyPlant.Partner.iOS\LoyaltyPlant.Partner.iOS.csproj">
      <Project>{4EF22F1F-1815-458C-8978-93CC1D628EEB}</Project>
      <Name>LoyaltyPlant.Partner.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Surveys\LoyaltyPlant.Surveys.iOS\LoyaltyPlant.Surveys.iOS.csproj">
      <Project>{A9A215AF-C3EC-47B4-8F80-A4DAEC3BE6FF}</Project>
      <Name>LoyaltyPlant.Surveys.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Support\LoyaltyPlant.Support.iOS\LoyaltyPlant.Support.iOS.csproj">
      <Project>{A6A33CB4-FECD-4E62-89FC-B2D0294F924E}</Project>
      <Name>LoyaltyPlant.Support.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\User\LoyaltyPlant.User.iOS\LoyaltyPlant.User.iOS.csproj">
      <Project>{68E5EFC3-4EF2-4ED8-B240-772E3DB940A1}</Project>
      <Name>LoyaltyPlant.User.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Core\LoyaltyPlant.Core.csproj">
      <Project>{8227D169-A02F-4C18-8563-CAC13B7B33F5}</Project>
      <Name>LoyaltyPlant.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Hints\LoyaltyPlant.Hints\LoyaltyPlant.Hints.csproj">
      <Project>{38D373CC-E139-4DD7-B8DC-EC62A8DBEAEE}</Project>
      <Name>LoyaltyPlant.Hints</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DigitalOrdering\LoyaltyPlant.DigitalOrdering.iOS\LoyaltyPlant.DigitalOrdering.iOS.csproj">
      <Project>{751AF381-38A6-4F1C-99F3-CFA42410B0E3}</Project>
      <Name>LoyaltyPlant.DigitalOrdering.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Payment\LoyaltyPlant.Payment.iOS\LoyaltyPlant.Payment.iOS.csproj">
      <Project>{9C3EE74C-C346-47E9-BF1D-D160F256D529}</Project>
      <Name>LoyaltyPlant.Payment.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\TieredLoyalty\LoyaltyPlant.TieredLoyalty\LoyaltyPlant.TieredLoyalty.csproj">
      <Project>{********-9C7E-47A8-8256-211DCFEEB577}</Project>
      <Name>LoyaltyPlant.TieredLoyalty</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Geofencing\LoyaltyPlant.Geofencing.iOS\LoyaltyPlant.Geofencing.iOS.csproj">
      <Project>{28D27E3F-4320-4DA8-B9B8-166F011FF244}</Project>
      <Name>LoyaltyPlant.Geofencing.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Content\LoyaltyPlant.Content.iOS\LoyaltyPlant.Content.iOS.csproj">
      <Project>{B5EB0C13-E320-4890-AB9D-5A0E5DBAA2DB}</Project>
      <Name>LoyaltyPlant.Content.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.FirebaseHelper\LoyaltyPlant.FirebaseHelper.csproj">
      <Project>{43F8F9EF-9032-484C-A6F9-91ECA37534D2}</Project>
      <Name>LoyaltyPlant.FirebaseHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.FacebookHelper\LoyaltyPlant.FacebookHelper.csproj">
      <Project>{29758E55-93D9-438E-81BC-8DE0CE6AA7B2}</Project>
      <Name>LoyaltyPlant.FacebookHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.AppsFlyerHelper\LoyaltyPlant.AppsFlyerHelper.csproj">
      <Project>{A21AFF81-BD1F-4923-AA54-62D747F02462}</Project>
      <Name>LoyaltyPlant.AppsFlyerHelper</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Analytics\LoyaltyPlant.AdjustHelper\LoyaltyPlant.AdjustHelper.csproj">
      <Name>LoyaltyPlant.AdjustHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Extensions\" />
    <Folder Include="Resources\Authorization\" />
    <Folder Include="Resources\Animations\Achievements\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\..\packages\AspectInjector.2.8.1\analyzers\dotnet\cs\AspectInjector.Analyzer.dll" />
    <Analyzer Include="..\..\packages\AspectInjector.2.8.1\analyzers\dotnet\cs\AspectInjector.Broker.dll" />
    <Analyzer Include="..\..\packages\AspectInjector.2.8.1\analyzers\dotnet\cs\AspectInjector.Rules.dll" />
    <Analyzer Include="..\..\packages\AspectInjector.2.8.1\analyzers\dotnet\cs\FluentIL.Common.dll" />
  </ItemGroup>
  <ItemGroup>
    <NativeReference Include="libAdjustSigSdk.iOS.a">
      <Kind>Static</Kind>
      <SmartLink>False</SmartLink>
      <ForceLoad>True</ForceLoad>
      <LinkerFlags>-L"$(ProjectDir)"</LinkerFlags>
    </NativeReference>
  </ItemGroup>
  <ItemGroup Condition="$(DefineConstants.Contains('GOOGLE_TAG_MANAGER'))">
    <Content Include="container\GTM-5JDXQ46H.json" />
  </ItemGroup>
  <Import Project="..\..\packages\AspectInjector.2.2.5\build\netstandard2.0\AspectInjector.targets" Condition="Exists('..\..\packages\AspectInjector.2.2.5\build\netstandard2.0\AspectInjector.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105.The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.props'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.iOS.Installations.8.10.0.3\build\Xamarin.Firebase.iOS.Installations.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.iOS.Installations.8.10.0.3\build\Xamarin.Firebase.iOS.Installations.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Firebase.iOS.Analytics.8.10.0.3\build\Xamarin.Firebase.iOS.Analytics.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Firebase.iOS.Analytics.8.10.0.3\build\Xamarin.Firebase.iOS.Analytics.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Google.iOS.Analytics.3.20.0.2\build\Xamarin.Google.iOS.Analytics.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Google.iOS.Analytics.3.20.0.2\build\Xamarin.Google.iOS.Analytics.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Google.iOS.TagManager.7.4.0.2\build\Xamarin.Google.iOS.TagManager.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Google.iOS.TagManager.7.4.0.2\build\Xamarin.Google.iOS.TagManager.targets'))" />
    <Error Condition="!Exists('..\..\packages\AspectInjector.2.8.1\build\netstandard2.0\AspectInjector.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\AspectInjector.2.8.1\build\netstandard2.0\AspectInjector.targets'))" />
  </Target>
  <Import Project="..\..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.targets" Condition="Exists('..\..\packages\Xamarin.Build.Download.0.11.0\build\Xamarin.Build.Download.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.iOS.Core.8.10.0.3\build\Xamarin.Firebase.iOS.Core.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.iOS.Core.8.10.0.3\build\Xamarin.Firebase.iOS.Core.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.iOS.SignIn.5.0.2.4\build\Xamarin.Google.iOS.SignIn.targets" Condition="Exists('..\..\packages\Xamarin.Google.iOS.SignIn.5.0.2.4\build\Xamarin.Google.iOS.SignIn.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.iOS.Installations.8.10.0.3\build\Xamarin.Firebase.iOS.Installations.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.iOS.Installations.8.10.0.3\build\Xamarin.Firebase.iOS.Installations.targets')" />
  <Import Project="..\..\packages\Xamarin.Firebase.iOS.Analytics.8.10.0.3\build\Xamarin.Firebase.iOS.Analytics.targets" Condition="Exists('..\..\packages\Xamarin.Firebase.iOS.Analytics.8.10.0.3\build\Xamarin.Firebase.iOS.Analytics.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.iOS.Analytics.3.20.0.2\build\Xamarin.Google.iOS.Analytics.targets" Condition="Exists('..\..\packages\Xamarin.Google.iOS.Analytics.3.20.0.2\build\Xamarin.Google.iOS.Analytics.targets')" />
  <Import Project="..\..\packages\Xamarin.Google.iOS.TagManager.7.4.0.2\build\Xamarin.Google.iOS.TagManager.targets" Condition="Exists('..\..\packages\Xamarin.Google.iOS.TagManager.7.4.0.2\build\Xamarin.Google.iOS.TagManager.targets')" />
  <Import Project="..\..\packages\AspectInjector.2.8.1\build\netstandard2.0\AspectInjector.targets" Condition="Exists('..\..\packages\AspectInjector.2.8.1\build\netstandard2.0\AspectInjector.targets')" />
</Project>