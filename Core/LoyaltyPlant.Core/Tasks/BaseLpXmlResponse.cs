using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using LoyaltyPlant.Core.Exceptions;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.Core.Tasks
{
    public abstract class BaseLpXmlResponse : BaseLpWebResponse
    {
        private XElement _element;

        protected BaseLpXmlResponse()
        {
        }

        public override void Parse()
        {
            if (string.IsNullOrEmpty(Content))
            {
                return;
            }

            const string regEx = "[\x00-\x08\x0B\x0C\x0E-\x1F]";
            _element = XDocument.Load(new StringReader(Regex.Replace(Content, regEx, "", RegexOptions.None)), LoadOptions.None).Root;
            var errorType = GetErrorType();

            if (!string.IsNullOrWhiteSpace(errorType))
            {
                switch (errorType)
                {
                    case "BAD_REQUEST":
                    case "NOT_REGISTERED":
                    case "NOT_FOUND":
                    case "INTERNAL_ERROR":
                        ErrorException = new XmlApiException(errorType);
                        break;

                    case "FORCE_UPDATE":
                        var updateElement = _element.GetElement("update");
                        var updateInfo = new UpdateInfo(UpdateType.Force, updateElement.GetAttributeValue("version"),
                                                        Convert.ToInt32(updateElement.GetAttributeValue("build")),
                                                        updateElement.GetAttributeValue("url"));
                        ErrorException = new ForceUpdateException(updateInfo);
                        break;

                    case "WIPE":
                        var wipe = SyncResponse.ParseWipeElement(_element);
                        ErrorException = new WipeException(wipe);
                        break;
                }

                return;
            }

            Parse(_element);
        }

        private string GetErrorType()
        {
            if (_element.GetAttributeValue("type") == "error")
            {
                var errorType = _element.GetAttributeValue("error-type");
                return errorType;
            }

            return null;
        }

        public abstract void Parse(XElement rootElement);

    }
}
