using System;
using System.Net;
using System.Threading.Tasks;
using LoyaltyPlant.Core.Analytics;
using LoyaltyPlant.Core.Exceptions;
using LoyaltyPlant.Core.Logger;

namespace LoyaltyPlant.Core.Tasks
{
    public class LpTask<T> : WebTask<T>
        where T : BaseLpWebResponse, new()
    {
        public bool IsSilentLogs { get; set; }

        public LpTask()
        {
        }

        public async Task<T> ExecuteAsync(BaseLpWebRequest request)
        {
            using (await Engine.Instance.WebRequestLocker.LockAsync(request.GetType().FullName))
            {
                var startRequestTime = Environment.TickCount;
                if (!IsSilentLogs)
                {
                    LpLogger.LOG_D($"Preparing to send {request}");
                }

                // check internet
                if (Engine.Instance.Platform.NoInternetConnection)
                {
                    if (!IsSilentLogs)
                    {
                        LpLogger.LOG_I(request + "\nno internet");
                    }
                    Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                    return new T
                    {
                        ErrorException = new NoInternetException(),
                    };
                }

                // check is allowed request
                if (!request.IsAllowed())
                {
                    if (!IsSilentLogs)
                    {
                        LpLogger.LOG_I(request + "\nRequest not allowed!");
                    }
                    Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                    return new T
                    {
                        ErrorException = new NotAllowedException(),
                    };
                }
                
                if (!Engine.Instance.WasLastNetworkRequestSuccessfull)
                {
                    LpLogger.LOG_I(request.GetType().Name + ": previous network request wasn't successfull. Checking if gstatic is reachable before configuring request.");
                    var isGstaticReachable = await Engine.Instance.Platform.IsGstaticReachable();

                    if (!isGstaticReachable)
                    {
                        LpLogger.LOG_I($"Request {request.GetType().Name}. Gstatic didn't respond. Throwing NoInternetException.");
                        Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                        return new T
                        {
                            ErrorException = new NoInternetException(),
                        };
                    }
                }

                // generate request
                try
                {
                    request.Setup();
                }
                catch (Exception e)
                {
                    if (!IsSilentLogs)
                    {
                        LpLogger.LOG_E(request + "\nError when setup request", e);
                    }
                    Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                    return new T
                    {
                        ErrorException = new RequestSetupException(),
                    };
                }

                // get response 
                T response;

                try
                {
                    var startRequestDTime = DateTime.Now;
                    response = await Engine.Instance.WebController.GetResponseAsync<T>(request).ConfigureAwait(false);
                    var delta = DateTime.Now - startRequestDTime;
                    
                    if (request.GetType().Name == "MenuRequest")
                        LpLogger.LOG_I($"(MenuAnalys)MenuRequest, Sending: Timing (msec): {delta.TotalMilliseconds}");
                    
                    if (response == null)
                    {
                        Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                        throw new LpTaskException("response is null");
                    }
                }
                catch (Exception e)
                {
                    if (!IsSilentLogs)
                        LpLogger.LOG_E(request + "\nError during receive response", e);
                    
                    Engine.Instance.WasLastNetworkRequestSuccessfull = false;

                    LpLogger.LOG_I($"Request {request} failed with exception: {e}. Didn't get response. Checking if gstatic is reachable.");
                    var isGstaticReachable = await Engine.Instance.Platform.IsGstaticReachable();

                    if (!isGstaticReachable)
                    {
                        LpLogger.LOG_I($"Request {request}. Gstatic didn't respond. Throwing NoInternetException.");
                        return new T
                        {
                            ErrorException = new NoInternetException(),
                        };
                    }

                    if (e is not WipeException && e is not ForceUpdateException)
                        LpLogger.LOG_I($"[APP_ERROR_101_LP_server_not_responding_but_we_have_internet] Request {request}. " +
                            "Got response from Gstatic. There are problems with LP server response. Showing message if it's a blocking request."
                            + LpLogger.PartnerAndClientIdString);

                    return new T
                    {
                        ErrorException = e,
                    };
                }

                if (response.ErrorException != null)
                {
                    if (!IsSilentLogs)
                        LpLogger.LOG_E(request + "\nError in WebController", response.ErrorException);

                    Engine.Instance.WasLastNetworkRequestSuccessfull = false;
                    
                    LpLogger.LOG_I($"Request {request} returned response with error exception: {response.ErrorException}. " +
                        $"Response status code: {response.StatusCode}. Checking if gstatic is reachable.");

                    var isGstaticReachable = await Engine.Instance.Platform.IsGstaticReachable();

                    if (!isGstaticReachable)
                    {
                        LpLogger.LOG_I($"Request {request}. Gstatic didn't respond. Throwing NoInternetException.");
                        return new T
                        {
                            ErrorException = new NoInternetException(),
                        };
                    }

                    if (response.ErrorException is not WipeException && response.ErrorException is not ForceUpdateException)
                        LpLogger.LOG_I($"[APP_ERROR_101_LP_server_not_responding_but_we_have_internet] Request {request}. " +
                            $"Got response from Gstatic. There are problems with LP server response. Showing message if it's a blocking request."
                            + LpLogger.PartnerAndClientIdString);

                    return response;
                }
                
                Engine.Instance.WasLastNetworkRequestSuccessfull = true;

                // decrypt and parse response
                try
                {
                    if (request.Decrypted)
                    {
                        response.Decrypt();
                    }
                    response.Parse();
                }
                catch (Exception e)
                {
                    if (!IsSilentLogs)
                    {
                        LpLogger.LOG_E(request + "\nError during parsing\n" + response.Content, e);
                    }
                    response.ErrorException = e;
                    return response;
                }

                var requestTime = Environment.TickCount - startRequestTime;
                if (!response.Ok)
                {
                    LpLogger.LOG_E(request + $"\nServer sent incorrect response. [{response.StatusCode}], request_time: {requestTime}" +
                                   $"{response.ErrorException?.Message}", response.ErrorException);
                }

                LpAnalytics.TrackRequest(request, response.StatusCode, requestTime);

                return response;
            }
        }

        public async Task<T> ExecuteAsync(BaseLpWebRequest request, int repeatCount, int delayBetweenRequests = 0)
        {
            T resultTask = null;

            for (int i = 0; i < repeatCount; i++)
            {
                resultTask = await ExecuteAsync(request);

                if (resultTask.Ok)
                {
                    return resultTask;
                }

                // https://jira.loyaltyplant.com/browse/APP-4426
                if (resultTask.is502ErrorReturned)
                {
                    if (i < repeatCount - 1)
                        LpLogger.LOG_I(request + $"\nServer sent 502 Error. Waiting {delayBetweenRequests} seconds before next request.");

                    await Task.Delay(delayBetweenRequests * 1000);
                }
            }
            return resultTask;
        }
    }
}
