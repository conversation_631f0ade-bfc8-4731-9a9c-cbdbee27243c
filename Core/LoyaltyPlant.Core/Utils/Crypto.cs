using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Org.BouncyCastle.Crypto.Digests;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.Crypto.Modes;
using Org.BouncyCastle.Crypto.Paddings;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Crypto.Prng;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.Utilities.Encoders;

namespace LoyaltyPlant.Core.Utils
{
    public static class Crypto
    {
        private static readonly SecureRandom secureRandom = new SecureRandom(new DigestRandomGenerator(new Sha1Digest()));
        private static readonly byte[] KEY_A = UTF8Encoding.UTF8.GetBytes("FEDCBA9876543210");
        private static readonly byte[] KEY = new byte[KEY_A.Length];
        private const int IV_SIZE = 16;

        static Crypto()
        {
            byte[] keys = { 53, 18, 45, 13, 51, 34, 66, 28, 15 };
            var key = keys[KEY[0] % keys.Length];
            for (var i = 0; i < KEY.Length; i++)
            {
                KEY[i] = KEY_A[i];
                KEY[i] ^= key;
            }
        }

        public static string Encrypt(string src)
        {
            // Generating IV
            var iv = new byte[IV_SIZE];
            secureRandom.NextBytes(iv);

            var cipher = new PaddedBufferedBlockCipher(new CbcBlockCipher(new AesEngine()), new Pkcs7Padding());
            cipher.Init(true, new ParametersWithIV(new KeyParameter(KEY), iv));

            byte[] inputData = UTF8Encoding.UTF8.GetBytes(src);
            var outBlockSize = cipher.GetOutputSize(inputData.Length);
            var outputData = new byte[outBlockSize];

            var outLength = cipher.ProcessBytes(inputData, 0, inputData.Length, outputData, 0);
            outLength += cipher.DoFinal(outputData, outLength);

            // Packing IV with cipher data
            var output = new byte[IV_SIZE + outLength];
            Array.Copy(iv, 0, output, 0, IV_SIZE);
            Array.Copy(outputData, 0, output, IV_SIZE, outLength);

            return Convert.ToBase64String(output);
        }

        public static byte[] Decrypt(byte[] src)
        {
            var bytes = Base64.Decode(src);

            // Getting IV from first 2 bytes
            var iv = new byte[IV_SIZE];
            Array.Copy(bytes, 0, iv, 0, IV_SIZE);

            // Creating AES/CBC/PKCS7Padding cipher with specified Secret Key and Initial Vector
            var cipher = new PaddedBufferedBlockCipher(new CbcBlockCipher(new AesEngine()), new Pkcs7Padding());
            cipher.Init(false, new ParametersWithIV(new KeyParameter(KEY), iv));

            var dataSize = bytes.Length - IV_SIZE;
            var outBlockSize = cipher.GetOutputSize(dataSize);
            var outputData = new byte[outBlockSize];

            int outLength = cipher.ProcessBytes(bytes, IV_SIZE, dataSize, outputData, 0);
            outLength += cipher.DoFinal(outputData, outLength);
            return outputData;
        }

        public static string Sha1(byte[] sig)
        {
            using (var sha1Managed = new SHA1Managed())
            {
                byte[] hashBytes = sha1Managed.ComputeHash(sig);
                var hashText = BitConverter.ToString(hashBytes);
                return hashText.Replace("-", ":").ToLower();
            }
        }

        public static string Sha256Hash(string value)
        {
            var sb = new StringBuilder();

            using (var hash = SHA256.Create())
            {
                Encoding enc = Encoding.UTF8;
                Byte[] result = hash.ComputeHash(enc.GetBytes(value));

                foreach (byte b in result)
                    sb.Append(b.ToString("x2"));
            }

            return sb.ToString();
        }

    }
}
