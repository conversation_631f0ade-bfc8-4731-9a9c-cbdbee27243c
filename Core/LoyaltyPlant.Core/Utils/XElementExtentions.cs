using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using LoyaltyPlant.Core.Logger;

namespace LoyaltyPlant.Core.Tasks
{
    public static class XElementExtentions
    {
        public static bool Contains(this XElement element, string value)
        {
            return element.Descendants(value).Any();
        }

        public static int GetIntValue(this XElement element, int defaultValue)
        {
            if (!string.IsNullOrWhiteSpace(element?.Value))
            {
                int.TryParse(element.Value, out defaultValue);
            }
            return defaultValue;
        }
        
        public static bool GetChild(this XElement element, string value, out XElement child)
        {
            child = element.Element(value);
            return child != null;
        }

        public static string GetCData(this XElement element, string value)
        {
            var list = element.Descendants(value);
            return list.Any() ? list.First().Value : "";
        }

        public static string GetAttributeValueTag(this XElement element, string value)
        {
            var attributeList = element.Descendants(value);
            if (attributeList?.Any() ?? false)
            {
                var attribute = attributeList.First().Attribute("value");
                return attribute?.Value;
            }
            else
            {
                LpLogger.LOG_W($"Group of elements parsing failed. Recieved Value = {value}");
                return null;
            }
        }

        public static string GetAttributeValue(this XElement element, string value)
        {
            var attribute = element.Attribute(value);
            return attribute?.Value;
        }

        public static XElement GetElement(this XElement element, string value)
        {
            return element.Descendants(value).FirstOrDefault();
        }

        public static IEnumerable<XElement> GetElementsList(this XElement element, string rootElement,
            string valueElement)
        {
            var list = element.Descendants(rootElement);
            return list.Any() ? list.First().Descendants(valueElement) : new List<XElement>();
        }
    }
}
