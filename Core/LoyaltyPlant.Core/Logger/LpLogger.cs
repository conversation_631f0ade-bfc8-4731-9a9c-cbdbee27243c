using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.Utils;
using Xamarin.Essentials;

namespace LoyaltyPlant.Core.Logger
{
    public class LpLogger : BaseModule<LpLogger, LoggerModel>, ISyncHandler
    {
        private const int ANALYSIS_SEND_LOGS_RESPONSE_TIME_LIMIT = 10;

        public const int MinLogsInOnePack = 10;
        public const int MaxLogsInOnePack = 50;
        public const int CriticalCountOfLogs = 2000;
        private static TimeSpan minTimeBetweenSeriesOfLogPacksSendings = new TimeSpan(0, 0, 15);
        private static TimeSpan minTimeForLogsSinceStartOfTheApp = new TimeSpan(0, 0, 60);
        private static TimeSpan _timeLimitForSuccessfulLogsSending = TimeSpan.FromMinutes(5);

        private const string EmailTo = "<EMAIL>";
        private const string EmptyLogsText = "Empty logs";

        private readonly ILoggerWriter _loggerWriter;
        private readonly SemaphoreSlim _semaphore;
        public string LogServer => Model.LogServer;
        private bool _weAreSendingLogsNow;

        private static int _partnerId => Engine.Instance.Configuration.PartnerId;
        private static string _buildVersion => Engine.Instance.Platform.AppVersion;
        private static int _clientId => Engine.Instance.Credentials.PublicId;

        public static string PartnerAndClientIdString => $"[PID={_partnerId}] [CID={_clientId}]";

        public LpLogger(ILoggerWriter loggerWriter)
        {
            _loggerWriter = loggerWriter;
            _semaphore = new SemaphoreSlim(1);
        }

        public static void LOG_D(string log, Exception exception = null)
        {
            Instance.AddLog(new Log(LogLevel.Debug, log), exception);
        }

        public static void LOG_I(string log, Exception exception = null)
        {
            Instance.AddLog(new Log(LogLevel.Info, log), exception);
        }

        public static void LOG_W(string log, Exception exception = null)
        {
            Instance.AddLog(new Log(LogLevel.Warn, log), exception);
        }

        public static void LOG_E(string log, Exception exception = null)
        {
            log += $" [partnerId={_partnerId}] [build={_buildVersion}]";
            Instance.AddLog(new Log(LogLevel.Error, log), exception);
        }

        private void AddLog(Log log, Exception ex)
        {
            log.Exception = ex;
            _loggerWriter.ConsoleWrite(log);

            //TODO: maybe sometimes we should be also storing and sending DEBUG to the server. For now - only to console!
            if (log.LogLevel >= LogLevel.Info)
            {
                Task.Run(async () =>
                {
                    await _semaphore.WaitAsync();

                    try
                    {
                        await Task.Run(async () =>
                        {
                            if (log.LogLevel >= LogLevel.Error)
                                _loggerWriter.TrackError(ex, log.Description);

                            _loggerWriter.AddLog(log);

                            bool needToForceLogsSending = (log.LogLevel >= LogLevel.Error);
                            var timeSinceLastLogsSendingSeriesAttempt = LpDateTime.Now - Model.LastSendingLogs;
                            var timeSinceStartOfTheApp = LpDateTime.Now - Engine.Instance.LastAppLaunchStartDateTime;

                            if (_weAreSendingLogsNow)
                            {
                                //If we are already sending a log packs series, no need to start another one...
                                return;
                            }

                            if (needToForceLogsSending)
                            {
                                //This case means some error happened when Exception was catched. We probably need to send logs ASAP.
                                await SendLogsInPacksUntilDeletedOrFailed(true);
                                return;
                            }

                            if (timeSinceLastLogsSendingSeriesAttempt < minTimeBetweenSeriesOfLogPacksSendings)
                            {
                                //If we are not sending a log pack series, we we were sending one recently, don't start a new series. wait a bit...
                                return;
                            }

                            if (timeSinceStartOfTheApp > minTimeForLogsSinceStartOfTheApp)
                            {
                                //the app has already launched, we can safely send logs and don't interfere with the app loading
                                //These are normal logs. Only really send a pack if there are enough of the logs
                                await SendLogsInPacksUntilDeletedOrFailed(false);
                                return;
                            }
                        });
                    }
                    finally
                    {
                        _semaphore.Release();
                    }
                });
            }
        }

        public async Task SendLogsInPacksUntilDeletedOrFailed(bool force)
        {
            await SendLogsInPacksUntilDeletedOrFailedAsync(force).ConfigureAwait(false);
        }

        // useful https://github.com/serilog/serilog-sinks-seq ?
        public async Task SendLogsInPacksUntilDeletedOrFailedAsync(bool force)
        {
            LOG_D($"Sending logs - start()");

            if (_weAreSendingLogsNow)
            {
                LOG_D($"Sending logs - end() Can't send these logs because we're sending another pack of logs at the moment.");
                return;
            }

            Model.LastSendingLogs = LpDateTime.Now;

            //TODO: replace this with a less costly method which will only get the total number of items  
            var allLogsStored = _loggerWriter.GetLogs();
            var logsToSend = _loggerWriter.GetLogs(MaxLogsInOnePack);

            LOG_I($"Sending logs - New logs pack! Total logs in the database now: {allLogsStored.Count}, we've extracted a pack of {logsToSend.Count} logs for sending");

            if (allLogsStored.Count > CriticalCountOfLogs)
            {
                _loggerWriter.DeleteAllLogs();
                LOG_W($"Sending logs - Too many logs! ({allLogsStored.Count}) All logs were deleted!");
            }

            if (logsToSend.Count > MinLogsInOnePack || force)
            {
                LOG_D($"Sending logs - Sending a pack of {logsToSend.Count} logs to server!");

                _weAreSendingLogsNow = true;
                var request = new LogRequest(logsToSend)
                {
                    TimeoutSeconds = 10
                };

                DateTime timeBeforeSendLogsRequest = DateTime.Now;

                var result = await new LpTask<LogResponse>
                {
                    IsSilentLogs = true,
                }.ExecuteAsync(request);

                if (result.Ok)
                {
                    Model.LastSuccessfulLogsSentDateTime = LpDateTime.Now;
                    TimeSpan serverSendLogsResponseTime = DateTime.Now.Subtract(timeBeforeSendLogsRequest);

                    if (serverSendLogsResponseTime.TotalSeconds <= ANALYSIS_SEND_LOGS_RESPONSE_TIME_LIMIT)
                    {
                        LOG_I($"Sending logs - Sent successfully a pack of {logsToSend.Count} logs! ANALYSIS_SEND_LOGS_RESPONSE_TIME = {serverSendLogsResponseTime.ToString(@"ss\:ff")} seconds");
                    }
                    else
                    {
                        LOG_W($"Sending logs - That was too long... ANALYSIS_SEND_LOGS_RESPONSE_TIME_TOO_LONG = {serverSendLogsResponseTime.ToString(@"ss\:ff")} seconds");
                    }

                    _loggerWriter.DeleteLogs(logsToSend);
                    Model.LastSendingLogs = LpDateTime.Now;

                    await SaveAsync();

                    _weAreSendingLogsNow = false;

                    LOG_I($"Sending logs - Proceeding to the next pack of logs!");
                    SendLogsInPacksUntilDeletedOrFailed(false);
                }
                else
                {
                    var exceptionString = result.ErrorException != null ? $" Exception: {result.ErrorException}. " : "";
                    LOG_W($"Sending logs - Failed to send a pack of {logsToSend.Count} logs. Response status code: {result.StatusCode}.{exceptionString}" +
                        $"Stopping the current series of packs and halting the 'normal' (non-forced) logs sending for at least {minTimeBetweenSeriesOfLogPacksSendings.Seconds} seconds...");
                    _weAreSendingLogsNow = false;
                }
            }
            else
            {
                LOG_I($"Sending logs - Not enough logs to send ({logsToSend.Count}<{MinLogsInOnePack}) and we are NOT forced to send. Stopping the current series of packs and halting the 'normal' (non-forced) logs sending for at least {minTimeBetweenSeriesOfLogPacksSendings.Seconds} seconds...");
            }

            LOG_D($"Sending logs - end()");
        }

        /// <summary>
        /// Checking if logs were not successfully sent to server for a long period of time.
        /// APP-4760, by default the  time limit is set to 5 minutes.
        /// </summary>
        /// <returns>Has it been too long cince last successful logs were sent.</returns>
        public bool IsLogsNotSentForTooLong()
        {
            if (_timeLimitForSuccessfulLogsSending != null)
                return DateTime.Now - Model.LastSuccessfulLogsSentDateTime >= _timeLimitForSuccessfulLogsSending;
            else
                return true;
        }

        public void DeleteDatabase()
        {
            _loggerWriter.Delete();
        }

        public void ProceedWithSyncRequest(XmlWriter writer, SyncType syncType)
        {
            // nothing
        }

        public void ProceedWithSyncResponse(XElement element)
        {
            if (element.Contains("log-server"))
            {
                var logServer = element.Element("log-server").GetAttributeValue("url");
                if (Model.LogServer != logServer)
                {
                    Model.LogServer = logServer;
                    Save();
                }
            }
        }

        [MainThread]
        // TODO: APP-2822 если захочешь выполнить эту задачку, то заиспользуй этот метод
        public async Task SendErrorLogsViaEmail(string attentionHeader)
        {
            try
            {
                var body = GetEmailBody(attentionHeader);
                var emailsTo = new List<string> { EmailTo };
                var subject = $"Logs {GetPublicId()} - {Engine.Instance.Configuration.PartnerId} - {Engine.Instance.Platform.PlatformNumber}";

                var emailMessage = new EmailMessage
                {
                    To = emailsTo,
                    Subject = subject,
                    Body = body,
                };
                await Email.ComposeAsync(emailMessage);
            }
            catch (Exception ex)
            {
                LOG_D("Can't send logs via email: ", ex);
            }
        }

        private string GetEmailBody(string attentionHeader)
        {
            var logsBase64 = GetAllLogsBase64();
            if (string.IsNullOrWhiteSpace(logsBase64))
                logsBase64 = EmptyLogsText;

            if (string.IsNullOrWhiteSpace(attentionHeader))
                attentionHeader = string.Empty;

            return $"{attentionHeader}\n\n{logsBase64}";
        }

        private string GetAllLogsBase64()
        {
            var logs = _loggerWriter.GetLogs();
            if (logs == null)
                return string.Empty;

            var sb = new StringBuilder();

            foreach (var log in logs)
                sb.AppendLine(log.LevelString.ToUpper() + " | " + log.Data + " | " + log.Timestamp);

            if (sb.Length == 0)
                return string.Empty;

            return sb.ToString().EncryptString();
        }
    }
}