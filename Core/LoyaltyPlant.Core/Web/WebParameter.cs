namespace LoyaltyPlant.Core.Web
{
    public class WebParameter
    {
        public enum WebParameterType
        {
            <PERSON><PERSON>,
            GetOrPost,
            UrlSegment,
            RequestBody,
            QueryString,
            HttpHeader,
        }

        public string Name { get; set; }
        public object Value { get; set; }
        public WebParameterType Type { get; set; }

        public override string ToString()
        {
            return string.Format("{0}={1}", (object)this.Name, this.Value);
        }
    }
}
