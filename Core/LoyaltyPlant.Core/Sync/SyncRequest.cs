using System;
using System.Xml;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Core.Utils;

namespace LoyaltyPlant.Core.Sync
{
    public class SyncRequest : BaseLpXmlRequest
    {
        private readonly SyncType _syncType = SyncType.Default;

        private const string PartnerInfoTag = "partner-info";
        private const string PointsTag = "points";
        private const string CouponsTag = "coupons";
        private const string SectionsTag = "sections";
        private const string FeaturesTag = "features";

        private const string UseTag = "use";

        public const string SkipSyncTag = "skip";
        public const string SkipCardsSyncAttr = "cards-sync";
        public const string SkipSectionsSyncAttr = "sections-sync";

        public const string NeedPersonalInfoTag = "need-personal-info";
        public const string SkipPersonalInfoAttr = "personal-info-sync";

        public SyncRequest(SyncType syncType = SyncType.AllowSkipSync | SyncType.AllowSkipCards | SyncType.AllowSkipSections) 
            : base(syncType.HasFlag(SyncType.Initial) ? "initial-sync" : "sync")
        {
            _syncType = syncType;
        }

        protected override void WriteXml(XmlWriter writer)
        {
            writer.WriteStartElement("request");
            writer.WriteAttributeString("type", "sync");
            writer.WriteAttributeString("protocol-version", "9");
            writer.WriteAttributeString("client-id", Engine.Instance.Credentials.ClientId);
            writer.WriteAttributeString("public-id", Engine.Instance.Credentials.PublicId.ToString());

            writer.WriteStartElement("application");
            writer.WriteAttributeString("version", Engine.Instance.Platform.AppVersion);
            writer.WriteAttributeString("build", Engine.Instance.Platform.AppBuild.ToString());
            writer.WriteEndElement();

            WriteDeviceIdentifiersElement(writer);
            Engine.Instance.Platform.WriteExtraXmlData(writer, false);

            writer.WriteStartElement("date");
            writer.WriteAttributeString("timestamp", LpDateTime.UtcNow.DateTimeTotalMillis().ToString());
            writer.WriteEndElement();

            var allModules = Engine.Instance.GetModules<ISyncHandler>();

            foreach (var handler in allModules)
            {
                try
                {
                    handler.ProceedWithSyncRequest(writer, _syncType);
                }
                catch (Exception e)
                {
                    LpLogger.LOG_E("SyncRequest module: " + handler.GetType().Name, e);
                }
            }

            writer.WriteEndElement();
        }

        protected override void ValidateXml()
        {
            var partnerInfoNodes = XmlDocument.GetElementsByTagName(PartnerInfoTag);
            if (partnerInfoNodes.Count == 0)
            {
                var partnerInfo = XmlDocument.CreateElement(PartnerInfoTag);
                partnerInfo.SetAttribute("version", LpDateTime.UtcNow.DateTimeTotalMillis().ToString());
                XmlDocument.DocumentElement.AppendChild(partnerInfo);
            }

            var pointNodes = XmlDocument.GetElementsByTagName(PointsTag);
            if (pointNodes.Count == 0)
            {
                var element = XmlDocument.CreateElement(PointsTag);
                element.SetAttribute("amount", "0");
                XmlDocument.DocumentElement.AppendChild(element);
            }

            var couponNodes = XmlDocument.GetElementsByTagName(CouponsTag);
            if (couponNodes.Count == 0)
            {
                var element = XmlDocument.CreateElement(CouponsTag);
                element.AppendChild(XmlDocument.CreateElement("hidden"));
                element.AppendChild(XmlDocument.CreateElement("shown"));
                XmlDocument.DocumentElement.AppendChild(element);
            }

            var sectionNodes = XmlDocument.GetElementsByTagName(SectionsTag);
            if (sectionNodes.Count == 0)
            {
                var element = XmlDocument.CreateElement(SectionsTag);
                element.SetAttribute("wp-fixed-section-id", "0");
                XmlDocument.DocumentElement.AppendChild(element);
            }

            var featureNodes = XmlDocument.GetElementsByTagName(FeaturesTag);
            if (featureNodes.Count == 0)
            {
                var element = XmlDocument.CreateElement(FeaturesTag);
                XmlDocument.DocumentElement.AppendChild(element);
            }

            var skipElement = XmlDocument.CreateElement(SkipSyncTag);

            if (!(_syncType.HasFlag(SyncType.Initial) ||
                  _syncType.HasFlag(SyncType.NeedPersonalInfo)))
            {
                SkipPersonalInfoSync(skipElement);
            }

            SkipCardAndSectionSync(skipElement);

            XmlDocument.DocumentElement.AppendChild(skipElement);
        }

        private void SkipCardAndSectionSync(XmlElement skipElement)
        {
            if (!_syncType.HasFlag(SyncType.AllowSkipSync))
                return;

            var allowSkipCardsSync = AllowSkipCardsSync();
            var allowSkipSyncSection = _syncType.HasFlag(SyncType.AllowSkipSections);

            if (allowSkipCardsSync || allowSkipSyncSection)
            {
                if (allowSkipSyncSection)
                    skipElement.SetAttribute(SkipSectionsSyncAttr, true.ToString());

                if (allowSkipCardsSync)
                    skipElement.SetAttribute(SkipCardsSyncAttr, true.ToString());
            }
        }

        private void SkipPersonalInfoSync(XmlElement skipElement)
        {
            skipElement.SetAttribute(SkipPersonalInfoAttr, true.ToString());
        }

        private bool AllowSkipCardsSync()
        {
            var useNodes = XmlDocument.GetElementsByTagName(UseTag);
            if (useNodes.Count > 0)
                return false;

            return _syncType.HasFlag(SyncType.AllowSkipCards);
        }
    }
}