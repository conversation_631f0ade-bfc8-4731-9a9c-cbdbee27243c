using System;
using System.Linq;
using System.Xml.Linq;
using LoyaltyPlant.Core.Tasks;

namespace LoyaltyPlant.Core.Auth
{
    public class RegisterResponse : BaseLpXmlResponse
    {
        public long CurrentServerTimestamp { get; private set; }
        public bool Accepted { get; private set; }
        public string RejectedReason { get; private set; }
        public string DeviceIdKeychain { get; private set; }

        public override void Parse(XElement rootElement)
        {
            CurrentServerTimestamp = Convert.ToInt64(rootElement.GetAttributeValue("current-server-timestamp"));
            Accepted = Convert.ToBoolean(rootElement.GetAttributeValue("accepted"));

            if (!Accepted)
            {
                // rootElement.GetCData(security-alarm");
                if (rootElement.Contains("reject-reason"))
                    RejectedReason = rootElement.GetCData("reject-reason");
                return;
            }

            Engine.Instance.Model.IsFirstInstallFromServer = Convert.ToBoolean(rootElement.GetAttributeValue("first-install"));
            Engine.Instance.Model.FirstInstallTimestampFromServer = Convert.ToInt64(rootElement.GetAttributeValue("first-install-timestamp"));

            Engine.Instance.Credentials = new Credentials()
            {
                PublicId = Convert.ToInt32(rootElement.GetAttributeValue("public-id")),
                ClientId = rootElement.GetAttributeValue("client-id")
            };

            if (rootElement.Contains("write-device-id-keychain"))
                DeviceIdKeychain = rootElement.GetAttributeValueTag("write-device-id-keychain");


            foreach (var regHandler in Engine.Instance.GetModules<IRegisterHandler>())
                regHandler.ProceedWithRegisterResponse(rootElement);
        }
    }
}
