using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Modules;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Texts.Model;

namespace LoyaltyPlant.Texts
{
    public class TextsModule : BaseModule<TextsModule, TextsModel>, ISyncHandler
    {
        public TextsModule() 
        {
        }

        public async Task<bool> UpdateTexts()
        {
            var result = await new LpTask<UpdateTextsResponse>().ExecuteAsync(new UpdateTextsRequest());
            await SaveAsync();
            return result.Ok;
        }

        public void ProceedWithSyncRequest(XmlWriter writer, SyncType syncType)
        {
            if (syncType.HasFlag(SyncType.PersonalInfo))
                return;

            writer.WriteStartElement("texts");
            writer.WriteAttributeString("version", Model.Version.ToString());
            writer.WriteEndElement();
        }

        public void ProceedWithSyncResponse(XElement element)
        {
            var needToUpdateTexts = element.Contains("update-texts-i18n");
            if (needToUpdateTexts)
                UpdateTexts().ConfigureAwait(false);
        }

        public async Task UpdateTextsIfNeeded()
        {
            if (Model.Version <= 0)
            {
                LpLogger.LOG_I("UpdateTextsIfNeeded(): Texts will be updated");
                await UpdateTexts();
            }
        }

        public void ShouldTextsUpdate()
        {
            Model.Version = 0;
        }
    }
}
