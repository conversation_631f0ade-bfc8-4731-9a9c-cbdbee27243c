using System;
using System.Net.Http;
using System.Xml;
using LoyaltyPlant.Base;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Tasks;

namespace LoyaltyPlant.Texts.Model
{
    public class UpdateTextsRequest : BaseLpXmlRequest
    {
        internal UpdateTextsRequest() : base("get-i18n")
        {
            
        }

        protected override void WriteXml(XmlWriter writer)
        {
            writer.WriteStartElement("request");
            writer.WriteAttributeString("type", Name);
            writer.WriteAttributeString("protocol-version", "9");
            writer.WriteAttributeString("client-id", Engine.Instance.Credentials.ClientId);
            writer.WriteEndElement();
        }
    }
}
