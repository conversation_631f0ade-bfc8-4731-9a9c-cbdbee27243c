using System.Linq;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Content;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core;
using LoyaltyPlant.Offer.Model;
using Xunit;

namespace LoyaltyPlant.Offer.Tests
{
    public class SmartSortTest
    {
        private const int OfferSectionId = 12;
        
        public SmartSortTest()
        {
            Engine.Initialize(new MockWebController(), new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());
            
            Engine.Instance.AddModule(new OfferModule{});
            Engine.Instance.AddModule(new ContentModule());

            var offerModel = new OfferModel
            {
                CustomSortSectionId = OfferSectionId,
            };
            for (int i = 0; i < 10; i++)
            {
                var offerCard = new OfferCard(i, 10000, "previewText", OfferSectionId, i * 100, "", "");
                offerModel.OfferCards.Add(offerCard);
            }
            
            OfferModule.Instance.SetModel(offerModel);
            
            ContentModule.Instance.SetModel(new ContentModel
            {
                Sections =
                {
                    new ParentSection(OfferSectionId, 100, "", 1, "", 4),
                }
            });
        }

        [Fact]
        public void CardMessage_SomeOfferTo_FirstPosition()
        {
            var someOffer = OfferModule.Instance.Offers[4];
            someOffer.WasUsedInMessageWithCard = true;

            var section = ContentModule.Instance.GetSection(OfferSectionId);
            var cards = ContentModule.Instance.GetSectionCards(section);

            var firstCard = cards.FirstOrDefault();
            Assert.Equal(someOffer, firstCard);
        }
    }
}