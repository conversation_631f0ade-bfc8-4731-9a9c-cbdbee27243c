<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{BCA91BB4-F525-4898-A1B4-18499E22A2AD}</ProjectGuid>
    <ProjectTypeGuids>{FEACFBD2-3405-455C-9665-78FE426C6842};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>LoyaltyPlant.Offer.iOS</RootNamespace>
    <AssemblyName>LoyaltyPlant.Offer.iOS</AssemblyName>
    <IPhoneResourcePrefix>Resources</IPhoneResourcePrefix>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>portable</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug</OutputPath>
    <DefineConstants>DEBUG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchDebug>true</MtouchDebug>
    <MtouchNoSymbolStrip>true</MtouchNoSymbolStrip>
    <MtouchFastDev>true</MtouchFastDev>
    <IOSDebuggerPort>33308</IOSDebuggerPort>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <DeviceSpecificBuild>false</DeviceSpecificBuild>
    <MtouchVerbosity>
    </MtouchVerbosity>
    <MtouchLink>
    </MtouchLink>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodesignKey>iPhone Developer</CodesignKey>
    <MtouchNoSymbolStrip>true</MtouchNoSymbolStrip>
    <MtouchLink>SdkOnly</MtouchLink>
    <MtouchHttpClientHandler>NSUrlSessionHandler</MtouchHttpClientHandler>
    <MtouchVerbosity>
    </MtouchVerbosity>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="Xamarin.iOS" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="OfferFrontend.cs" />
    <Compile Include="OfferViewController.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Backend\LoyaltyPlant.App.iOS\LoyaltyPlant.App.iOS.csproj">
      <Project>{E0FBDB2B-2A29-4997-8348-7E57B906F605}</Project>
      <Name>LoyaltyPlant.App.iOS</Name>
      <IsAppExtension>false</IsAppExtension>
      <IsWatchApp>false</IsWatchApp>
    </ProjectReference>
    <ProjectReference Include="..\..\Content\LoyaltyPlant.Content\LoyaltyPlant.Content.csproj">
      <Project>{0ADCB3E5-86B7-4248-978F-3CD4092ABF87}</Project>
      <Name>LoyaltyPlant.Content</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Texts\LoyaltyPlant.Texts.csproj">
      <Project>{2e749bdd-98e9-4b1e-bc7f-cbf006c8a453}</Project>
      <Name>LoyaltyPlant.Texts</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\TieredLoyalty\LoyaltyPlant.TieredLoyalty\LoyaltyPlant.TieredLoyalty.csproj">
      <Project>{b5260943-9c7e-47a8-8256-211dcfeeb577}</Project>
      <Name>LoyaltyPlant.TieredLoyalty</Name>
    </ProjectReference>
    <ProjectReference Include="..\LoyaltyPlant.Offer\LoyaltyPlant.Offer.csproj">
      <Project>{DA2869D3-DD30-419E-AF07-AADD86032416}</Project>
      <Name>LoyaltyPlant.Offer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.View\LoyaltyPlant.View.csproj">
      <Project>{05E6F7F8-997D-47A7-8C14-D3583384B80B}</Project>
      <Name>LoyaltyPlant.View</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Images\LoyaltyPlant.Images.csproj">
      <Project>{D96B4A6A-B10C-4C24-A491-78D63B7CE5BF}</Project>
      <Name>LoyaltyPlant.Images</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\LoyaltyPlant.Core\LoyaltyPlant.Core.csproj">
      <Project>{8227D169-A02F-4C18-8563-CAC13B7B33F5}</Project>
      <Name>LoyaltyPlant.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\iOS\Xamarin.iOS.CSharp.targets" />
</Project>