using System;
using LoyaltyPlant.Content.Model;

namespace LoyaltyPlant.Offer
{
    public static class SmartSortExtension
    {
        public static int GetMainScreenRank(this Card card)
        {
            var rank = OfferModule.Instance.GetCardRank(card);
            return rank.MainScreenRank;
        }
    }

    [Serializable]
    public class CardRankWrapper
    {
        public int MainScreenRank { get; set; }
        public long ShownOnMainScreenCounterLastIncrementTimestamp { get; set; }
        public int ShownOnMainScreenCounter { get; set; }
    }
}