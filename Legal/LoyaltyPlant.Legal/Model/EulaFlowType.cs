using System.Runtime.Serialization;
using FFImageLoading;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace LoyaltyPlant.Legal.Model
{
    [Preserve(AllMembers = true)]
    [JsonConverter(typeof(StringEnumConverter))]
    public enum EulaFlowType
    {
        [EnumMember(Value = "RUS")]
        Rus,
        [EnumMember(Value = "USA")]
        Usa,
        [EnumMember(Value = "GDPR")]
        Gdpr,
    }
}