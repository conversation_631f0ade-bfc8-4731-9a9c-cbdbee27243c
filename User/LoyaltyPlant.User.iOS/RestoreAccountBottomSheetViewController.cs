using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Sync;
using LoyaltyPlant.Messages;
using LoyaltyPlant.Surveys.Fields;
using LoyaltyPlant.Surveys.iOS;
using LoyaltyPlant.Surveys.Model;
using LoyaltyPlant.Texts;
using LoyaltyPlant.Backend.Utils;
using UIKit;
using LoyaltyPlant.Partner;

namespace LoyaltyPlant.User.iOS
{
    public class RestoreAccountBottomSheetViewController : BottomSheetBaseViewController
    {
        private UILabel _titleLabel;
        private UILabel _descriptionLabel;
        private UIView _continueButton;
        private UIView _containerView;
        private StringField _emailField;
        private StringFieldView _emailFieldView;

        public RestoreAccountBottomSheetViewController() : base()
        {
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            SetupContentView();
            SetupConstraints();
            UpdateContentViewWith(_containerView);
        }

        private void SetupContentView()
        {
            _containerView = new UIView();

            _titleLabel = new UILabel()
            {
                TextColor = Colors.BackgroundTextColor,
                Text = I18N.EMAIL_VERIFICATION_EMAIL_HEADER,
                Lines = 0,
                TextAlignment = UITextAlignment.Center
            };
            _titleLabel.SetFont(Dimensions.NewSubheaderFont, UniversalUtils.FontType.Bold);

            _descriptionLabel = new UILabel()
            {
                TextColor = Colors.BackgroundTextColor,
                Text = string.Format(I18N.EMAIL_VERIFICATION_EMAIL_TEXT, PartnerModule.Instance.Model.ApplicationName),
                Lines = 0,
                TextAlignment = UITextAlignment.Center
            };
            _descriptionLabel.SetFont(Dimensions.DefaultFont, UniversalUtils.FontType.Normal);

            _emailField = new EmailField(-1, -1, false, String.Empty, Meta.Email);
            var emailFieldWidth = UIScreen.MainScreen.Bounds.Width - 2 * Dimensions.PaddingBig;
            _emailFieldView = new StringFieldView(_emailField, emailFieldWidth);

            _continueButton = new UiBottomButton(I18N.CONTINUE, GetContinueButtonAction(), _containerView, false);

            _containerView.AddSubview(_titleLabel);
            _containerView.AddSubview(_descriptionLabel);
            _containerView.AddSubview(_emailFieldView);
            _containerView.AddSubview(_continueButton);
        }

        private Action GetContinueButtonAction()
        {
            return async () =>
            {
                _emailFieldView.EndEditing(true);
                var answer = _emailFieldView.TextField.Text;
                if (string.IsNullOrEmpty(answer))
                    _emailFieldView.ShowError(string.Format(I18N.SURVEY_ERROR_INCORRECT_BIO, _emailField.Placeholder.ToLower()));
                else
                {
                    var isValidEmail = _emailField.CheckAndApplyAnswer(answer, out var error);

                    if (isValidEmail)
                    {
                        await Backend.BackendModule.Instance.RestoreAccount(answer, () =>
                        {
                            AnimateDismissView();
                        });
                    }

                    if (!string.IsNullOrEmpty(error))
                        _emailFieldView.ShowError(error);
                }
            };
        }

        private void SetupConstraints()
        {
            _titleLabel.TranslatesAutoresizingMaskIntoConstraints = false;
            _descriptionLabel.TranslatesAutoresizingMaskIntoConstraints = false;
            _emailFieldView.TranslatesAutoresizingMaskIntoConstraints = false;
            _continueButton.TranslatesAutoresizingMaskIntoConstraints = false;

            NSLayoutConstraint.ActivateConstraints(new NSLayoutConstraint[]
            {
                _titleLabel.CenterXAnchor.ConstraintEqualTo(_containerView.CenterXAnchor),
                _titleLabel.TopAnchor.ConstraintEqualTo(_containerView.TopAnchor),
                _titleLabel.LeadingAnchor.ConstraintEqualTo(_containerView.LeadingAnchor, Dimensions.PaddingBig),
                _titleLabel.TrailingAnchor.ConstraintEqualTo(_containerView.TrailingAnchor, -Dimensions.PaddingBig),

                _descriptionLabel.CenterXAnchor.ConstraintEqualTo(_containerView.CenterXAnchor),
                _descriptionLabel.LeadingAnchor.ConstraintEqualTo(_titleLabel.LeadingAnchor),
                _descriptionLabel.TrailingAnchor.ConstraintEqualTo(_titleLabel.TrailingAnchor),
                _descriptionLabel.TopAnchor.ConstraintEqualTo(_titleLabel.BottomAnchor, Dimensions.Padding),

                _emailFieldView.CenterXAnchor.ConstraintEqualTo(_containerView.CenterXAnchor),
                _emailFieldView.TopAnchor.ConstraintEqualTo(_descriptionLabel.BottomAnchor, Dimensions.Padding),
                _emailFieldView.LeadingAnchor.ConstraintEqualTo(_titleLabel.LeadingAnchor),
                _emailFieldView.TrailingAnchor.ConstraintEqualTo(_titleLabel.TrailingAnchor),
                _emailFieldView.HeightAnchor.ConstraintEqualTo(_emailFieldView.Bounds.Height),

                _continueButton.TopAnchor.ConstraintEqualTo(_emailFieldView.BottomAnchor, Dimensions.Padding),
                _continueButton.TrailingAnchor.ConstraintEqualTo(_containerView.TrailingAnchor),
                _continueButton.LeadingAnchor.ConstraintEqualTo(_containerView.LeadingAnchor),
                _continueButton.HeightAnchor.ConstraintEqualTo(_continueButton.Bounds.Height),
                _continueButton.BottomAnchor.ConstraintEqualTo(_containerView.BottomAnchor, Dimensions.PaddingBig)
            });
        }
    }
}
