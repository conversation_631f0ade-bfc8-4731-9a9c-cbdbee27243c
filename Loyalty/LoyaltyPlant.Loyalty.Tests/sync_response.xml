<?xml version="1.0" encoding="utf-8"?>
<response protocol-version="9" type="sync" current-server-timestamp="1532367682781">

    <points amount="15"/>

    <user-validated-present value="true"/>
    <user-validated-bonuscard value="true"/>
    
    <start-present start-present-id="140611" can-use-now="true"/>
    <birthday-present birthday-present-id="142396"/>
    <discounts>
        <discount type="amount" id="1" value="5.00" taxed="false"/>
    </discounts>
    <coupons>
        <hidden>
            <add>
                <coupon card-id="142396" type="present" image-url-part="md5=6e5d8374d3195f4046c39b4b3c2574b8&from=_client_20271507" version="1526999297000" client-coupon-id="*********" server-id="1" generation-timestamp="1533908011171" section-id="12659">
                    <move-to-hidden-after-use value="true"/>
                    <short-description>
                        <![CDATA[Happy birthday, stop by for something sweet on us!]]>
                    </short-description>
                    <priority value="130105"/>
                    <coupon-price value="0" visible="true"/>
                    <previewtext>
                        <![CDATA[H&H Cookies for your Birthday]]>
                    </previewtext>
                    <digital-ordering-linked-items>
                        <item id="7879" ordering-type="all"/>
                    </digital-ordering-linked-items>
                </coupon>
            </add>
            <remove/>
            <update/>
            <from-hidden-to-shown/>
        </hidden>
        <shown>
            <add>
                <coupon card-id="140611" type="present" image-url-part="md5=63de8e4e2ecf2098d799a6915179516f&from=_client_20271507" version="1524829658000" client-coupon-id="379944342" server-id="1" generation-timestamp="1533908011180" section-id="12661">
                    <move-to-hidden-after-use value="true"/>
                    <short-description>
                        <![CDATA[Thanks for using our app! Show this coupon to get $5 off for your order!]]>
                    </short-description>
                    <priority value="5000"/>
                    <coupon-price value="0" visible="true"/>
                    <previewtext>
                        <![CDATA[$5 Off for your next order]]>
                    </previewtext>
                    <digital-ordering-linked-items>
                        <item ordering-type="all"/>
                    </digital-ordering-linked-items>
                    <digital-ordering-linked-discount id="1"/>
                </coupon>
                <coupon card-id="139798" type="present" image-url-part="md5=d755e36b4e86d00f6b2e4a333cfbbe07&from=_client_20271507" version="1525359985000" client-coupon-id="379944343" server-id="1" generation-timestamp="1533908011188" section-id="12659">
                    <move-to-hidden-after-use value="false"/>
                    <short-description>
                        <![CDATA[Collect points and get this reward!]]>
                    </short-description>
                    <offer-details>
                        <![CDATA[
                        <p>Served on choice of Rye, Whole Wheat, White Bread, Kaiser Roll, or a Bagel. Includes Lettuce, Tomato, Onion, and your choice of Condiments. All are served with a Kosher Dill Pickle and Colesaw.
                        </p>]]>
                    </offer-details>
                    <priority value="25000"/>
                    <coupon-price value="2390" visible="true"/>
                    <previewtext>
                        <![CDATA[Roasted Turkey]]>
                    </previewtext>
                    <digital-ordering-linked-items>
                        <item id="7871" ordering-type="all"/>
                    </digital-ordering-linked-items>
                </coupon>


            </add>
            <remove/>
            <update/>
            <from-shown-to-hidden/>
        </shown>
    </coupons>
    <sections/>
    
    <bonuscard card-id="128296" version="1525679305000" image-url-part="md5=c73e0906cee7ff6cfc0379e3b03f81d8&from=_client_20271507" section-id="12657">
        <text-to-show-on-validation-screen>
            <![CDATA[Loyalty card]]>
        </text-to-show-on-validation-screen>
        <previewtext>
            <![CDATA[Use your Loyalty Card to earn points]]>
        </previewtext>
        <purchase min="1" max="300000"/>
        <multiplier value="100"/>
        <validation-codes>
            <validation client-coupon-id="379944378" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944377" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944376" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944375" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944374" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944373" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944372" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944371" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944370" server-id="1" generation-timestamp="1533908785473"/>
            <validation client-coupon-id="379944369" server-id="1" generation-timestamp="1533908785473"/>
        </validation-codes>
        <priority value="100"/>
    </bonuscard>

</response>