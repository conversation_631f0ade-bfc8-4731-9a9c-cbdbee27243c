using System;
using System.Collections.Generic;
using System.Linq;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;

namespace LoyaltyPlant.Loyalty.Model
{
    [Serializable]
    public abstract class LoyaltyCard : Card
    {
        public List<ValidationInformation> ValidationInfos = new List<ValidationInformation>();
        public readonly string TextToShowOnValidationScreen;
        public string DigitalCodeForCurrentQrCode;

        protected LoyaltyCard(int id, long version, string previewText, string textToShowOnValidationScreen,
            int sectionId, int priority, string imageUrl)
            : base(id, version, previewText, sectionId, priority, imageUrl, null)
        {
            TextToShowOnValidationScreen = textToShowOnValidationScreen;
        }

        public ValidationInformation GetLastValidationInfo()
        {
            return ValidationInfos.Count > 0 ? ValidationInfos[0] : null;
        }

        public ValidationInformation[] GetAllValidationInfos()
        {
            return ValidationInfos.ToArray();
        }

        public void AddValidationInfo(ValidationInformation validationInformation)
        {
            ValidationInfos.Insert(0, validationInformation);
        }

        public void RemoveLastValidationInfo()
        {
            if (ValidationInfos == null || ValidationInfos.Count == 0)
            {
                LpLogger.LOG_E("Cant remove last validation info because no validation infos were found");
                return;
            }
            ValidationInfos.RemoveAt(0);
        }

        public int ValidationInfoCount()
        {
            return GetAllValidationInfos() != null ? ValidationInfos.Count : 0;
        }

        public bool HasValidationCodes()
        {
            return ValidationInfos.Count > 0;
		}

        public override bool IsSpecial => true;

        public override void WriteInRequest(System.Xml.XmlWriter writer)
        {
            return;
        }

        public void RemoveUsedInPendingsValidationCodes()
        {
            var pendings = new List<Pending>(LoyaltyModule.Instance.Model.Pendings);
            var valInfos = new List<ValidationInformation> (ValidationInfos);

            foreach (var validationInformation in valInfos)
            {
                if (pendings.Any(pending => validationInformation.Equals(pending.ValidationInfo)))
                {
                    ValidationInfos.Remove(validationInformation);
                }
            }
        }
    }
}