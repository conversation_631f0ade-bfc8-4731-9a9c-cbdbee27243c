using System;
using System.Collections.Generic;
using System.Text;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Loyalty.Utils
{
    public class QRCodeGeneratorV5 : QRCodeGenerator
    {
        private const int VERSION = 5;


        /// <summary>
        /// ���������� qr-���
        /// </summary>
        /// <param name="uses">-������ � ������������� ����������, ������ ��������� ������� 1 ��������</param>
        /// <param name="multiplier">-multiplier ������� ��������</param>
        /// <param name="timestamp">-����� ��������� qr-����</param>
        /// <returns>qr-���</returns>
        public static string GenerateQrCode(List<Pending> uses, int multiplier, long timestamp)
        {
            string qrCodeDataFields = GenerateQrCodeDataFields(uses, multiplier, timestamp);
            try
            {
                string encryptedQrCode = QRCodeEncrypterV2.Encrypt(qrCodeDataFields);
                return LoyaltyModule.Instance.Model.QrCodePrefix + VERSION + encryptedQrCode;
            }
            catch (Exception e)
            {
                LpLogger.LOG_E("Can't encrypt QRcode: " + e);
                return VERSION + qrCodeDataFields;
            }
        }

        /// <summary>
        /// ���������� ��������������� qr-���
        /// </summary>
        /// <param name="uses">-������ � ������������� ����������, ������ ��������� ������� 1 ��������</param>
        /// <param name="multiplier">-multiplier ������� ��������</param>
        /// <param name="timestamp">-����� ��������� qr-����</param>
        /// <returns>��������������� qr-���</returns>
        private static string GenerateQrCodeDataFields(List<Pending> uses, int multiplier, long timestamp)
        {
            StringBuilder sb = new StringBuilder();
            //[6] PartnerID, int
            sb.Append(ToFixedLengthString(Engine.Instance.Configuration.PartnerId, 6));

            //[1] ServerID - id ������� ������� ������ ��� (��������, �������� ��� ��� �����-��, �������� �������� ��������), int.
            // ������������ server-id �� ������ ������������ ��������, ���������� ��� ��������� ��������.
            Pending firstUse = uses[0];
            sb.Append(ToFixedLengthString(firstUse.ValidationInfo.ServerId, 1));

            //[9] pid - �� �� PublicID �������, int
            sb.Append(ToFixedLengthString(Engine.Instance.Credentials.PublicId, 9));

            //[1] multiplierId �� partner-info, ����� ��� ���� ����. �������� 2 ��������:
            //1 �������� ��� multiplier = 1, int
            //2 �������� ��� multiplier = 100, int
            sb.Append(ToFixedLengthString(GetMultiplierId(multiplier), 1));

            //[13] ������� ����� � �������������� �� ������� ������� � ������� timestamp � ��������������.
            sb.Append(ToFixedLengthString(timestamp.ToString(), 13));

            //[2] ���������� ������������ ���������.
            sb.Append(ToFixedLengthString(uses.Count, 2));

            //����� ������� ����� ������������ ���������, ������ ���� ���� �� ������.
            //������ ���� ���������� � ���� ���� ���� ��������� � CardID.
            foreach (Pending use in uses)
            {
                //[1] ��� ���� ���������
                int cardType = GetCardType(use);
                sb.Append(ToFixedLengthString(cardType, 1));

                //[1] ����� ���� CardID (0-9), int
                //[����� ���� CardID, ��. ����] CardID, int
                AppendDynamicLengthValue(sb, use.CardId);

                //����� ��������� ����� ������������ �� ���� �����
                switch (cardType)
                {
                    case BONUS_CARD:
                        //[1] ����� ���� amount (0-9), int
                        //[����� ���� amount] ������ ����� � ���������, ������� ���� ������
                        AppendDynamicLengthValue(sb, use.Amount);
                        break;
                    case DISCOUNT_CARD:
                        //[1] ����� ���� amount (0-9), int
                        //[����� ���� amount] ������ ����� � ���������, ������� ���� ������
                        AppendDynamicLengthValue(sb, use.Amount);
                        //[1] ����� ���� discount
                        //[����� ���� discount] - ������� ������, int
                        AppendDynamicLengthValue(sb, use.Discount);
                        break;
                    case PRESENT:
                        //��� �������������� �����
                        break;
                    case PAY_WITH_POINTS:
                        //[1] ����� ���� amount (0-9), int
                        //[����� ���� amount] ������ ����� � ���������, ������� ���� ������
                        AppendDynamicLengthValue(sb, use.Amount);
                        //[1] ����� ���� points to spent
                        //[����� ���� points to spent] - ���������� ������ ��� �������� (Points_to_spend), int
                        AppendDynamicLengthValue(sb, use.PointsSpent);
                        break;
                }
            }
            return sb.ToString();
        }
    }
}