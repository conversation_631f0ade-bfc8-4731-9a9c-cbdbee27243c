using LoyaltyPlant.Loyalty.Model;

namespace LoyaltyPlant.Loyalty
{
    public class Validation
    {
        public Present Present { get; }
        public string QrCodeString { get; }
        public string QrCodeHint { get; }
        public QrCodeType QrCodeType { get; }
        public string DigitalValidationCode { get; }

        public Validation(Present present, string qrCodeString, string qrCodeHint, QrCodeType qrCodeType, 
            string digitalValidationCode)
        {
            Present = present;
            QrCodeString = qrCodeString;
            QrCodeHint = qrCodeHint;
            QrCodeType = qrCodeType;
            DigitalValidationCode = digitalValidationCode;
        }
    }
}