using System;
using System.Threading.Tasks;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Content.Model;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.Loyalty.iOS.Utils;
using LoyaltyPlant.Loyalty.Model;
using LoyaltyPlant.Texts;
using UIKit;

namespace LoyaltyPlant.Loyalty.iOS
{
    public class LoyaltyValidationViewController : BaseViewController, IDigitalCodeShower
	{
        private nfloat _oldBrightness = 0.0f;

        private readonly LoyaltyCard _loyaltyCard;
        private readonly string _qrString;
        private readonly string _sumString;
        private QrCodeFragment _qrView;

        private UIView _bottomButton;
        private readonly string _qrHintText;
        private string _currentCode = "";

        public LoyaltyValidationViewController(Card card, string qrString, string sumString, string qrHintText)
		{
            _qrHintText = qrHintText;
            _sumString = sumString;
			_qrString = qrString;
			_loyaltyCard = card as LoyaltyCard;
		}

        protected override ApplicationScreenType GetApplicationScreenType()
        {
            switch (_loyaltyCard)
            {
	            case BonusCard _:
		            return ApplicationScreenType.BonusCardValidationScreen;
	            case DiscountCard _:
		            return ApplicationScreenType.DiscountCardValidationScreen;
	            case PayWithPointsCard _:
		            return ApplicationScreenType.PayWithPointsCardValidationScreen;
	            default:
		            return ApplicationScreenType.Default;
            }
        }

		public override UIColor NavigationBarColor => UIColor.Clear;
		public override UIColor NavigationBarActionButtonsColor => UIColor.White;
		public override UIColor NavigationBarTitleColor => UIColor.White;

		public override void ViewWillAppear(bool animated)
		{
			base.ViewWillAppear(animated);
			NavigationItem.HidesBackButton = true;
			NavigationController.NavigationBarHidden = false;
			NavigationController.NavigationBar.Translucent = true;
		}

		public override void ViewDidLoad()
		{
			base.ViewDidLoad();

            SetGradientForBackground(View, Colors.SecondaryBackgroundGradientUpColor, Colors.SecondaryBackgroundGradientDownColor,
			                         Colors.SecondaryBackgroundColor);
			NavigationItem.HidesBackButton = true;

			_bottomButton = CreateBottomButton(I18N.DONE, () =>
			{
				DoneButtonTouched();
			});
			_bottomButton.AccessibilityIdentifier = "doneButton";
			View.AddSubview(_bottomButton);
            _bottomButton.SetBottom(View.Frame.Height);

            // hide logo on iPhone 4/4s/5/5c/5s/SE/6/7/8/6,7,8 Plus
            if (UIScreen.MainScreen.Bounds.Height > 736)
            {
                var logo = new UIImageView(new CGRect(0, 0, Width, Width / 2));
                logo.ContentMode = UIViewContentMode.ScaleAspectFill;
                logo.SetHeight(Width * 0.488f);
                logo.AccessibilityIdentifier = "logo";
                logo.Image = UIImage.FromFile("featured_graphic.png");
                View.AddSubview(logo);
            }

            ShowQrCode(_loyaltyCard, _qrString, _qrHintText, _loyaltyCard.DigitalCodeForCurrentQrCode ?? _currentCode,
			           LoyaltyModule.Instance.NeedToShowDigitalCodeForQrOnLoyaltyCardScreen() 
			           ? QrCodeType.QrCodeWithDigits : QrCodeType.QrCode);

			if (!string.IsNullOrWhiteSpace(_sumString))
			{
				var sumLabel = new UILabel();
				sumLabel.AccessibilityIdentifier = "sumText";
				sumLabel.Text = _sumString;
				sumLabel.TextColor = Colors.AccentColor;
                sumLabel.SetFont(UniversalUtils.FontStyle.Bigheader, UniversalUtils.FontType.Bold);
				sumLabel.SizeToFit();
				sumLabel.SetInCenterHorizontal(View);
                sumLabel.SetBottom(_qrView.Frame.Top - Dimensions.Padding);
				View.AddSubview(sumLabel);
			}
        }

		private void ShowQrCode(Card card, string qrCodeString, string hint, string digitalValidationCode, QrCodeType qrCodeType)
		{
			_qrView = new QrCodeFragment(card as LoyaltyCard, qrCodeString, hint, digitalValidationCode, qrCodeType);
            _qrView.SetBottom(_bottomButton.Frame.Top - Dimensions.PaddingBig);
			View.AddSubview(_qrView);

			if (string.IsNullOrWhiteSpace(_sumString))
			{
				_qrView.SetTop((_bottomButton.Frame.Top - Width * 0.488f)/2 + Width * 0.488f - _qrView.Frame.Height/2);
			}
        }

		private void DoneButtonTouched()
		{
			switch (_loyaltyCard)
			{
				case BonusCard _:
					LoyaltyModule.Instance.Controller.OnClickedDoneButtonOnBonusCardValidationScreen();
					break;
				case DiscountCard _:
					LoyaltyModule.Instance.Controller.OnClickedDoneButtonOnDiscountCardValidationScreen();
					break;
				case PayWithPointsCard _:
					LoyaltyModule.Instance.Controller.OnClickedDoneButtonOnPayWithPointsCardValidationScreen();
					break;
			}
		}

		public override void ViewDidAppear(bool animated)
		{
            base.ViewDidAppear(animated);
            _oldBrightness = UIScreen.MainScreen.Brightness;
            _ = ChangeBrightness(UIScreen.MainScreen.Brightness, 1.0f);
        }

		public override void ViewDidDisappear(bool animated)
		{
            _ = ChangeBrightness(UIScreen.MainScreen.Brightness, _oldBrightness);

            base.ViewDidDisappear(animated);

            LoyaltyModule.Instance.Controller.OnValidationScreenClosed(null);
		}

        public void ShowDigitalCode(string code)
        {
            if (_qrView != null)
				_qrView.UpdateDigitalCode(code);
            else
                _currentCode = code;
        }

        private async Task ChangeBrightness(nfloat toValue, nfloat fromValue)
        {
            var differenceBrightness = (fromValue - toValue) >= 0;

            while (Math.Round(UIScreen.MainScreen.Brightness, 2) != Math.Round(fromValue, 2))
            {
                UIScreen.MainScreen.Brightness += (differenceBrightness) ? 0.01f : -0.01f;

                await Task.Delay(5);
            }
        }
    }
}