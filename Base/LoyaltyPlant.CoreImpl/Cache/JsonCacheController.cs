using System;
using System.IO;
using System.Threading.Tasks;
using LoyaltyPlant.Base.Utils;
using LoyaltyPlant.Core.Cache;
using LoyaltyPlant.Core.Exceptions;
using Newtonsoft.Json;

namespace LoyaltyPlant.CoreImpl.Cache
{
    public class JsonCacheController : ICacheController
    {
        private readonly AsyncDuplicateLock _fileLock = new AsyncDuplicateLock();
        private readonly JsonSerializer _jsonSerializer;

        public JsonCacheController(string rootPath) : this(rootPath, new JsonSerializer
        {
            TypeNameHandling = TypeNameHandling.Auto,
            ContractResolver = new FieldsContractResolver(),
            NullValueHandling = NullValueHandling.Ignore,
            ReferenceLoopHandling = ReferenceLoopHandling.Serialize,
            PreserveReferencesHandling = PreserveReferencesHandling.Objects,
        })
        {
        }

        public JsonCacheController(string rootPath, JsonSerializer jsonSerializer)
        {
            if (!System.IO.Directory.Exists(rootPath))
            {
                try
                {
                    System.IO.Directory.CreateDirectory(rootPath);
                }
                catch
                {
                    //
                }
            }

            Directory = rootPath;
            _jsonSerializer = jsonSerializer;
        }

        public string Directory { get; }


        public bool IsFileExists(string filename)
        {
            return File.Exists(this.GetAbsPath(filename));
        }

        public async Task<T> LoadAsync<T>(string filename)
        {
            var absFilePath = this.GetAbsPath(filename);
            using (await _fileLock.LockAsync(absFilePath))
            {
                try
                {
                    if (!IsFileExists(filename))
                        return default(T);

                    return await Task.Run(() =>
                    {
                        var filePath = this.GetAbsPath(filename);
                        using (var file = File.OpenText(filePath))
                        {
                            return (T) _jsonSerializer.Deserialize(file, typeof(T));
                        }
                    });
                }
                catch (Exception e)
                {
                    throw new CacheException($"Exception during json loading: {typeof(T)}, file: {filename}", e);
                }
            }
        }

        public async Task SaveAsync<T>(string filename, T data)
        {
            var absFilePath = this.GetAbsPath(filename);
            using (await _fileLock.LockAsync(absFilePath))
            {
                try
                {
                    await Task.Run(() =>
                    {
                        using (StreamWriter sw = new StreamWriter(absFilePath))
                        {
                            using (JsonWriter writer = new JsonTextWriter(sw))
                            {
                                _jsonSerializer.Serialize(writer, data);
                            }
                        }
                    });
                }
                catch (IOException e)
                {
                    FullStorageException.ThrowIfStorageIsFull(e);
                    throw new CacheException($"IOException during json saving: {typeof(T)}, file: {filename}", e);
                }
                catch (Exception e)
                {
                    throw new CacheException($"Exception during json saving: {typeof(T)}, file: {filename}", e);
                }
            }
        }

        public byte[] Load(string filename)
        {
            // not implemented
            return new byte[0];
        }

        public void Save(string filename, byte[] data)
        {
            // not implemented
        }
    }
}