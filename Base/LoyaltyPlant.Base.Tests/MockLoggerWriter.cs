using System;
using System.Collections.Generic;
using LoyaltyPlant.Core.Logger;

namespace LoyaltyPlant.Base.Tests
{
	public class MockLoggerWriter : ILoggerWriter
    {
        public MockLoggerWriter(bool disableExceptionByErrorLogs = false)
        {
            DisableExceptionByErrorLogs = disableExceptionByErrorLogs;
        }
        
        public void TrackError(Exception ex, string description)
        {

        }

        public bool DisableExceptionByErrorLogs { get; set; }

        public void AddLog(Log log)
        {

        }

        public void ConsoleWrite(Log log)
        {
            if (!DisableExceptionByErrorLogs && log.LogLevel == LogLevel.Error)
            {
                throw new Exception(log.Data);
            }
        }

        public void DeleteAllLogs()
        {
        }

        public void Delete()
        {
        }

        public void DeleteLogs(List<Log> logs)
        {
        }

        public List<Log> GetLogs()
        {
            return new List<Log>();
        }

        public List<Log> GetLogs(int count)
        {
            return new List<Log>();
        }
    }
}
