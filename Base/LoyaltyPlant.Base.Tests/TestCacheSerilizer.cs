using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using LoyaltyPlant.Core.Cache;

namespace LoyaltyPlant.Base.Tests
{
    public class TestCacheSerilizer : ICacheController
    {
        public string Directory => ".";

        public async Task<T> LoadAsync<T>(string filename)
        {
            return default(T);
        }

        public async Task SaveAsync<T>(string filename, T data)
        {
            using (var memoryStream = new MemoryStream())
            {
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, data);
            }
        }

        public byte[] Load(string filename)
        {
            return null;
        }

        public void Save(string filename, byte[] data)
        {
            using (var memoryStream = new MemoryStream())
            {
                IFormatter formatter = new BinaryFormatter();
                formatter.Serialize(memoryStream, data);
            }
        }

        public bool IsFileExists(string filename)
        {
            return false;
        }
    }
}
