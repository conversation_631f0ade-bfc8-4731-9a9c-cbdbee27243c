using System;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Modules;

namespace LoyaltyPlant.Base.Tests
{
    public static class ModuleTestExtensions
    {
        public static void SetModel<TModule, TModel>(this BaseModule<TModule, TModel> module, TModel model)
            where TModel : IModuleModel, new() where TModule : IModule
        {
            var cacheController = Engine.Instance.CacheController as MockCacheController;
            if (cacheController == null)
            {
                throw new Exception("Use for engine MockCacheController");
            }
            cacheController.Add(model);
            module.LoadAsync().Wait();
        }  
        
        public static void SetModel(this Engine engine, EngineModel model)
        {
            var cacheController = engine.CacheController as MockCacheController;
            if (cacheController == null)
            {
                throw new Exception("Use for engine MockCacheController");
            }
            cacheController.Add(model);
            engine.LoadAsync().Wait();
        }
    }
}