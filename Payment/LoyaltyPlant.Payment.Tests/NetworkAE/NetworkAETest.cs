using System;
using System.IO;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.Partner;
using LoyaltyPlant.Payment.Tasks;
using LoyaltyPlant.Payment.Tests.Mocks;
using LoyaltyPlant.Texts;
using Xunit;

namespace LoyaltyPlant.Payment.Tests.NetworkAE
{
    public class NetworkAETest
    {
        [Fact]
        public async void Payment_GateSettings_NetworkAEParsing()
        {
            var wc = new MockWebController("NetworkAE/gate-settings-networkae.json");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());
            Engine.Instance.AddModule(new PaymentModule());
            PaymentModule.Instance.InitPaymentIntent(new MockPaymentObject());

            await PaymentModule.Instance.GetGateSettings();
            var gate = PaymentModule.Instance.PaymentIntent.Gate;
            Assert.IsType<Gates.NetworkAEGate.NetworkAE>(gate);
            Assert.Equal(100, gate.Timeout);
        }

        [Fact]
        public async void Payment_NetworkAE_Pay()
        {
            var wc = new MockWebController();

            wc.AddResponse("gate-settings", "NetworkAE/gate-settings-networkae.json");
            wc.AddResponse("add-bank-card", "add-bank-card.json");
            wc.AddResponse("get-bank-cards", "get-bank-cards.json");
            wc.AddResponse("make-payment", "make-payment-success.json");
            wc.AddResponse("get-enroll-settings", "NetworkAE/get-enroll-settings-networkae.json");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter(true));
            Engine.Instance.AddModule(new PaymentModule());
            Engine.Instance.AddModule(new PartnerModule());
            var partnerModel = new PartnerModel();
            PartnerModule.Instance.SetModel(partnerModel);
            var po = new MockPaymentObject();
            var paymentFrontend = new MockPaymentFrontend(true)
            {
                BrowserUrlResponses = { ("http://fallback-promo.test1.loyaltyplant.net/payment-result?stripeToken=123456", null) },
            };
            PaymentModule.Instance.SetController(new PaymentController(paymentFrontend));
            
            PaymentModule.Instance.InitPaymentIntent(po);
            PaymentModule.Instance.Controller.OnSelectPaymentMethod(new Model.PaymentMethod(Model.PaymentMethodType.NewCard));
            
            await PaymentModule.Instance.GetGateSettings();
            
            var paySuccessful = await PaymentModule.Instance.Controller.Pay();
            
            Assert.True(paySuccessful);
            Assert.True(po.MakePaymentCompleted);
        }

        [Fact]
        public async void GetEnrollSettingsRequest_AddContextData()
        {
            var mc = new MockWebController();
            
            Engine.Initialize(mc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter(true));
            Engine.Instance.AddModule(new PaymentModule());
            Engine.Instance.AddModule(new PartnerModule());
            var partnerModel = new PartnerModel();
            PartnerModule.Instance.SetModel(partnerModel);
            var po = new MockPaymentObject();
            var paymentIntent = PaymentModule.Instance.InitPaymentIntent(po);
            paymentIntent.Gate = new Gates.NetworkAEGate.NetworkAE
            {
               RedirectUrl = "http://fallback-promo.test1.loyaltyplant.net/payment-result",
            };
            var paymentFrontend = new MockPaymentFrontend(true)
            {
                BrowserUrlResponses =
                {
                    ("http://fallback-promo.test1.loyaltyplant.net/payment-result", null)
                },
            };
            PaymentModule.Instance.SetController(new PaymentController(paymentFrontend));
            
            PaymentModule.Instance.Controller.OnSelectPaymentMethod(new Model.PaymentMethod(Model.PaymentMethodType.NewCard));

            string salesOutletId = null;
            mc.OnResponse += request =>
            {
                if (request is GetEnrollSettingsRequest getEnrollSettingsRequest)
                {
                    salesOutletId = getEnrollSettingsRequest.JObject["salesOutletId"].ToObject<string>();
                }
            };

            var paySuccessful = await PaymentModule.Instance.Controller.Pay();
            
            Assert.True(paySuccessful);
            Assert.Equal("some id", salesOutletId);
        }
    }
}