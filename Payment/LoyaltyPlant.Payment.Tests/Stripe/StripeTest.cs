using System;
using System.IO;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.Payment.Tests.Mocks;
using LoyaltyPlant.Texts;
using Xunit;

namespace LoyaltyPlant.Payment.Tests.Stripe
{
    public class StripeTest
    {
        [Fact]
        public async void Payment_GateSettings_StripeParsing()
        {
            var wc = new MockWebController("Stripe/gate-settings-stripe.json");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());
            Engine.Instance.AddModule(new PaymentModule());
            PaymentModule.Instance.InitPaymentIntent(new MockPaymentObject());

            await PaymentModule.Instance.GetGateSettings();
            var gate = PaymentModule.Instance.PaymentIntent.Gate;

            Assert.IsType(typeof(Gates.StripeGate.Stripe), gate);
            Assert.Equal(100, gate.Timeout);
            Assert.Equal("https://test1.loyaltyplant.net/card-enrollment/stripe/?publicApiKey=pk_test_QS6tiGMwtjVdzz18yW3V80tQ&redirect=http%3A%2F%2Ffallback-promo.test1.loyaltyplant.net%2Fpayment-result", gate.Url);
            Assert.Equal("pk_test_QS6tiGMwtjVdzz18yW3V80tQ", (gate as Gates.StripeGate.Stripe).PublicApiKey);
            Assert.Equal("http://fallback-promo.test1.loyaltyplant.net/payment-result?stripeToken=123456", gate.RedirectUrl);
        }

        [Fact]
        public async void Payment_Stripe_AddCardCancel()
        {
            var wc = new MockWebController();

            wc.AddResponse("gate-settings", "Stripe/gate-settings-stripe.json");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());
            Engine.Instance.AddModule(new PaymentModule());
            PaymentModule.Instance.InitPaymentIntent(new MockPaymentObject());
            await PaymentModule.Instance.GetGateSettings();

            PaymentModule.Instance.SetController(new PaymentController(new MockPaymentFrontend(false)));
            await PaymentModule.Instance.Controller.OnAddCard();

            Assert.Equal(Model.PaymentMethodType.NotSelected, PaymentModule.Instance.PaymentIntent.PaymentMethod.PaymentMethodType);
            Assert.Equal(0, PaymentModule.Instance.Model.BankCards.Count);
        }

        [Fact]
        public async void Payment_Stripe_AddCardSuccess()
        {
            var wc = new MockWebController();

            wc.AddResponse("gate-settings", "Stripe/gate-settings-stripe.json");
            wc.AddResponse("add-bank-card", "add-bank-card.json");
            wc.AddResponse("get-bank-cards", "get-bank-cards.json");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());
            Engine.Instance.AddModule(new PaymentModule());
            var paymentFrontend = new MockPaymentFrontend(true)
            {
                BrowserUrlResponses =
                {
                    ("http://fallback-promo.test1.loyaltyplant.net/payment-result?stripeToken=123456", null)
                },
            };
            PaymentModule.Instance.SetController(new PaymentController(paymentFrontend));
            
            PaymentModule.Instance.InitPaymentIntent(new MockPaymentObject());
            await PaymentModule.Instance.GetGateSettings();

            Assert.Equal(Model.PaymentMethodType.NotSelected, PaymentModule.Instance.PaymentIntent.PaymentMethod.PaymentMethodType);
            await PaymentModule.Instance.Controller.OnAddCard();

            Assert.Equal(Model.PaymentMethodType.BankCardApp, PaymentModule.Instance.PaymentIntent.PaymentMethod.PaymentMethodType);
            Assert.Equal(81678, PaymentModule.Instance.PaymentIntent.PaymentMethod.CardId);
        }
    }
}