using System;
using System.Linq;
using CoreAnimation;
using CoreGraphics;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Core.Analytics;
using LoyaltyPlant.Core.UI;
using LoyaltyPlant.DigitalOrdering;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Texts;
using UIKit;

namespace LoyaltyPlant.Payment.iOS
{
    public class PaymentMethodsViewController : BaseViewController
    {
        private readonly PaymentMethod _lastMethod;
        private readonly bool _fromProfile;

        private UIScrollView _scrollView;
        private UIView _emptyView;
        private UIView _crossView;

        protected override ApplicationScreenType GetApplicationScreenType()
        {
            return ApplicationScreenType.PaymentMethodsScreen;
        }

        public PaymentMethodsViewController(PaymentMethod lastMethod, bool fromProfile)
        {
            _fromProfile = fromProfile;
            _lastMethod = lastMethod;
        }

        public override void ViewDidAppear(bool animated)
        {
            base.ViewDidAppear(animated);
            UpdatePaymentMethodsList();
        }

        public override void ViewDidLoad()
        {
            base.ViewDidLoad();
            SetGradientForBackground(View, Colors.SecondaryBackgroundGradientUpColor, Colors.SecondaryBackgroundGradientDownColor,
                                     Colors.SecondaryBackgroundColor);

            NavigationItem.Title = _fromProfile ? PaymentModule.Instance.SavedPaymentMethodsTitle : PaymentModule.Instance.PaymentMethodsTitle;

            _scrollView = new UIScrollView(View.Bounds);
            _scrollView.ShowsVerticalScrollIndicator = false;
            View.AddSubview(_scrollView);

            _emptyView = new UIView(_scrollView.Bounds)
            {
                BackgroundColor = Colors.SecondaryBackgroundColor,
                Hidden = true
            };
            View.AddSubview(_emptyView);

            var tmpView = new UIView(new CGRect(View.Frame.Width / 2 - 260 / 2, 0, 260, 0));
            _emptyView.AddSubview(tmpView);

            var emptyIcon = new UILabel(new CGRect(0, 0, tmpView.Frame.Width, 153f))
            {
                TextAlignment = UITextAlignment.Center,
                Text = "y",
                TextColor = Colors.HintTextColor,
                Font = UIFont.FromName("icon_font", 150f)
            };
            tmpView.AddSubview(emptyIcon);

            nfloat delta = 90f;
            var header = new UILabel(new CGRect(0, emptyIcon.Frame.Bottom + delta, tmpView.Frame.Width, 0f))
            {
                TextAlignment = UITextAlignment.Center,
                Text = I18N.EMPTY_CARDS_SCREEN_HEADER,
                Font = UniversalUtils.SystemFont(Dimensions.HeaderFont, UniversalUtils.FontType.Semibold),
                TextColor = Colors.DefaultTextColor
            };

            header.SizeHeightToFit();
            header.SetWidth(tmpView.Frame.Width);
            tmpView.AddSubview(header);

            var desc = new UILabel(new CGRect(0, header.Frame.Bottom + Dimensions.PaddingSmall, tmpView.Frame.Width, 0f))
            {
                TextAlignment = UITextAlignment.Center,
                Lines = 0,
                Text = I18N.EMPTY_CARDS_SCREEN_DESCRIPTION,
                Font = UniversalUtils.SystemFont(Dimensions.MiddleFont),
                TextColor = Colors.DefaultTextColor
            };

            desc.SetLineHeight(22f);
            desc.SizeHeightToFit();
            desc.SetWidth(tmpView.Frame.Width);
            tmpView.AddSubview(desc);

            tmpView.SetHeight(desc.Frame.Bottom);
            tmpView.SetTop(_emptyView.Frame.Height / 2 - tmpView.Frame.Height / 2);

            UpdatePaymentMethodsList();
        }

        internal void UpdatePaymentMethodsList()
        {
            var methods = _fromProfile ? PaymentModule.Instance.SavedPaymentMethods() : PaymentModule.Instance.GetAllPaymentMethods();

            foreach (var view in _scrollView.Subviews)
                view.RemoveFromSuperview();

            if (_fromProfile)
            {
                if (methods.Count <= 0)
                {
                    _emptyView.Hidden = false;
                    return;
                }
                else
                    _emptyView.Hidden = true;
            }
            else
                _emptyView.Hidden = true;

            var panel = new UIStackPanel()
            {
                Orientation = UIStackPanel.Orientations.Vertical,
                PaddingBetweenElements = 24
            };
            panel.AddPadding(Dimensions.PaddingSmall);
            panel.SetWidth(Width);

            var sbpMethods = methods.Where(m => m.PaymentMethodType == PaymentMethodType.Sbp);
            foreach (var method in sbpMethods)
                panel.AddSubview(CreateMethodView(method));

            var newSpbCardMethod = methods.FirstOrDefault(m => m.PaymentMethodType == PaymentMethodType.NewSbp);
            if (newSpbCardMethod != null && !_fromProfile)
            {
                var addSbpCardView = CreateMethodView(newSpbCardMethod);
                panel.AddSubview(addSbpCardView);
            }
            
            foreach (var method in methods.Where(m => !m.IsOffline && !m.IsSbpPayment))
                panel.AddSubview(CreateMethodView(method));

            if (!_fromProfile && PaymentModule.Instance.NeedToShowAddNewCardButton())
            {
                var addRegularCardView = CreateAddCardView();
                panel.AddSubview(addRegularCardView);
            }

            if (methods.Count(m => m.IsOffline) > 0)
                foreach (var method in methods.Where(m => m.IsOffline))
                    panel.AddSubview(CreateMethodView(method));

            _scrollView.AddSubview(panel);
        }

        private UIView CreateAddCardView()
        {
            var view = new UIView(new CGRect(0, 0, Width, 45))
            {
                IsAccessibilityElement = true,
                AccessibilityLabel = I18N.ADD_DEBIT_OR_CREDIT_CARD,
                AccessibilityTraits = UIAccessibilityTrait.Button
            };

            var iconView = new UIView(new CGRect(Dimensions.PaddingBig, 0, 32, 32))
            {
                BackgroundColor = UIColor.White,
                ClipsToBounds = true
            };
            iconView.Layer.BorderColor = Colors.DividerTextColor.CGColor;
            iconView.Layer.BorderWidth = 1f;
            iconView.Layer.CornerRadius = 3f;
            view.AddSubview(iconView);

            var icon = new UIImageView(new CGRect(2, 2, iconView.Frame.Width - 4, iconView.Frame.Height - 4))
            {
                ContentMode = UIViewContentMode.ScaleAspectFit,
                Image = UIImage.FromFile("PaymentProcessing/plus.png")
            };
            iconView.AddSubview(icon);

            var arrowLabel = new UILabel
            {
                Font = UIFont.FromName("navigation_font", Dimensions.BigFont),
                Text = Settings.IsLtrSelected ? "2" : "3",
                TextColor = Colors.DividerTextColor
            };
            arrowLabel.SizeToFit();
            view.AddSubview(arrowLabel);

            var headerView = new UILabel(new CGRect(
                iconView.Frame.Right + Dimensions.Padding,
                0,
                view.Frame.Width - iconView.Frame.Width - arrowLabel.Frame.Width - 2 * Dimensions.Padding - 2 * Dimensions.PaddingBig,
                iconView.Frame.Height))
            {
                TextColor = Colors.DefaultTextColor,
                Text = PaymentModule.Instance.GetTextForNewCard(),
                TextAlignment = Settings.IsLtrSelected ? UITextAlignment.Left : UITextAlignment.Right
            };

            headerView.SetFont(Dimensions.MiddleFont);
            view.AddSubview(headerView);
            arrowLabel.SetInCenterVertical(headerView);

            var warningMessageLabel = new UILabel(new CGRect(
                Dimensions.PaddingBig, iconView.Frame.Bottom,
                Width - iconView.Frame.Width - Dimensions.Padding - 2 * Dimensions.PaddingBig, 0))
            {
                TextColor = Colors.SecondaryTextColor,
                Text = I18N.WARNING_CHECK_CARD_WITH_CHARGE,
                Lines = 0,
                TextAlignment = Settings.IsLtrSelected ? UITextAlignment.Left : UITextAlignment.Right
            };

            warningMessageLabel.SetFont(Dimensions.SemiSmallFont);
            warningMessageLabel.SizeHeightToFit();
            view.AddSubview(warningMessageLabel);

            var currentHeight = headerView.Frame.Height + warningMessageLabel.Frame.Height + Dimensions.PaddingSmall;
            view.SetHeight(currentHeight);

            if (PaymentModule.Instance.IsNeedShowCartLogos())
            {
                var cardsView = new UIView(new CGRect(
                    Dimensions.PaddingBig,
                    warningMessageLabel.Frame.Bottom + 4,
                    view.Frame.Width - 2 * Dimensions.PaddingBig - iconView.Frame.Width - Dimensions.Padding,
                    40 * Dimensions.Multiplier));

                var madaView = GetImageViewByCard("PaymentProcessing/mada.png");
                var meezaView = GetImageViewByCard("PaymentProcessing/meeza.png");
                var amexView = GetImageViewByCard("PaymentProcessing/americanexpress.png");
                var visaView = GetImageViewByCard("PaymentProcessing/visa.png");
                var masterView = GetImageViewByCard("PaymentProcessing/mastercard.png");

                cardsView.AddSubviews(masterView, visaView, amexView, meezaView, madaView);

                if (Settings.IsLtrSelected)
                {
                    meezaView.SetLeft(madaView.Frame.Right + 4);
                    amexView.SetLeft(meezaView.Frame.Right + 4);
                    visaView.SetLeft(amexView.Frame.Right + 4);
                    masterView.SetLeft(visaView.Frame.Right + 4);

                    cardsView.SetLeft(Dimensions.PaddingBig + iconView.Frame.Width + Dimensions.Padding);
                }
                else
                {
                    madaView.SetRight(cardsView.Frame.Width);
                    meezaView.SetRight(madaView.Frame.Left - 4);
                    amexView.SetRight(meezaView.Frame.Left - 4);
                    visaView.SetRight(amexView.Frame.Left - 4);
                    masterView.SetRight(visaView.Frame.Left - 4);

                    cardsView.SetRight(Width - (Dimensions.PaddingBig + iconView.Frame.Width + Dimensions.Padding));
                }

                currentHeight += cardsView.Frame.Height;

                view.AddSubview(cardsView);
                view.SetHeight(currentHeight);
            }

            var line = new UILine(view.Frame.Width - Dimensions.PaddingBig - headerView.Frame.Left);
            line.SetLeft(headerView.Frame.Left);
            line.SetBottom(view.Frame.Height);
            view.AddSubview(line);

            line.SetWidth(headerView.Frame.Width);

            SetLtrPaddingsIfNeeded(headerView, line, iconView, warningMessageLabel, arrowLabel);

            view.SetMaterialSelection(async () =>
            {
                await DigitalOrderingModule.Instance.UpdateUserDataBeforePayment();
                _ = PaymentModule.Instance.Controller.OnAddCard();
                LpAnalytics.TrackSelectPaymentMethod("ADD_NEW_CARD");
                NavigationController.PopViewController(true);
            });
            return view;
        }

        private UIImageView GetImageViewByCard(string path)
        {
            var icon = new UIImageView(new CGRect(0, 0, 46, 40))
            {
                ContentMode = UIViewContentMode.ScaleAspectFit,
                Image = UIImage.FromFile(path)
            };
            return icon;
        }

        private UIView CreateMethodView(PaymentMethod method)
        {
            var isAddSbpCardButton = method.PaymentMethodType == PaymentMethodType.NewSbp;
            
            var view = new UIView(new CGRect(0, 0, Width, 45))
            {
                IsAccessibilityElement = true,
                AccessibilityLabel = method.Name,
                AccessibilityTraits = UIAccessibilityTrait.Button
            };

            var iconView = new UIView(new CGRect(0, 0, 32, 32))
            {
                BackgroundColor = UIColor.White,
                ClipsToBounds = true
            };
            iconView.Layer.BorderColor = Colors.DividerTextColor.CGColor;
            iconView.Layer.BorderWidth = 1f;
            iconView.Layer.CornerRadius = 3f;

            view.AddSubview(iconView);

            var icon = new UIImageView(new CGRect(2, 2, iconView.Frame.Width - 4, iconView.Frame.Height - 4));
            icon.ContentMode = UIViewContentMode.ScaleAspectFit;
            iconView.AddSubview(icon);

            if (method.GetCard() != null)
                switch (method.GetCard().CardType)
                {
                    case BankCardType.AmericanExpress:
                        icon.Image = UIImage.FromFile("PaymentProcessing/americanexpress.png");
                        break;
                    case BankCardType.DinersClub:
                        icon.Image = UIImage.FromFile("PaymentProcessing/dinnerclub.png");
                        break;
                    case BankCardType.DiscoverCard:
                        icon.Image = UIImage.FromFile("PaymentProcessing/discover.png");
                        break;
                    case BankCardType.Maestro:
                        icon.Image = UIImage.FromFile("PaymentProcessing/maestro.png");
                        break;
                    case BankCardType.Mada:
                        icon.Image = UIImage.FromFile("PaymentProcessing/mada.png");
                        break;
                    case BankCardType.Meeza:
                        icon.Image = UIImage.FromFile("PaymentProcessing/meeza.png");
                        break;
                    case BankCardType.MasterCard:
                        icon.Image = UIImage.FromFile("PaymentProcessing/mastercard.png");
                        break;
                    case BankCardType.Visa:
                        icon.Image = UIImage.FromFile("PaymentProcessing/visa.png");
                        break;
                    case BankCardType.Sbp:
                        icon.Image = UIImage.FromFile("PaymentProcessing/sbp_icon.png");
                        break;
                    default:
                        icon.Image = UIImage.FromFile("PaymentProcessing/card.png");
                        break;
                }
            else
                switch (method.PaymentMethodType)
                {
                    case PaymentMethodType.ApplePay:
                        icon.Frame = iconView.Frame;
                        icon.Image = UIImage.FromFile("PaymentProcessing/applepay.png");
                        iconView.Layer.BorderWidth = 0f;
                        iconView.BackgroundColor = UIColor.Clear;
                        break;
                    case PaymentMethodType.AndroidPay:
                        icon.Image = UIImage.FromFile("PaymentProcessing/androidpay.png");
                        break;
                    case PaymentMethodType.BankCardOffline:
                        icon.Image = UIImage.FromFile("PaymentProcessing/card.png");
                        break;
                    case PaymentMethodType.CashOffline:
                        icon.Image = UIImage.FromFile("PaymentProcessing/cash.png");
                        break;
                    case PaymentMethodType.NewCard when PaymentModule.Instance.PaymentIntent.Gate.Type is GateType.Pesapal 
                                                        || PaymentModule.Instance.PaymentIntent.Gate.Type is GateType.VivaWallet:
                        icon.Image = UIImage.FromFile("PaymentProcessing/online_payment.png");
                        break;
                    case PaymentMethodType.NewCard:
                        icon.Image = UIImage.FromFile("PaymentProcessing/card.png");
                        break;
                    case PaymentMethodType.Sbp:
                    case PaymentMethodType.NewSbp:
                        icon.Image = UIImage.FromFile("PaymentProcessing/sbp_icon.png");
                        break;
                    default:
                        icon.Image = UIImage.FromFile("PaymentProcessing/card.png");
                        break;
                }

            var headerView = new UILabel(new CGRect(0, 0, 0, iconView.Frame.Height))
            {
                TextColor = Colors.DefaultTextColor,
                Text = method.Name,
                TextAlignment = Settings.IsLtrSelected ? UITextAlignment.Left : UITextAlignment.Right
            };

            headerView.SetFont(Dimensions.MiddleFont);
            view.AddSubview(headerView);

            var line = new UILine(0);
            line.SetBottom(view.Frame.Height);
            view.AddSubview(line);

            if (method.Equals(_lastMethod) && !_fromProfile && !isAddSbpCardButton)
            {
                var group = new CGRect(0, 0, 30, 30);
                var bezierPath = new UIBezierPath();
                bezierPath.MoveTo(new CGPoint(group.GetMinX() + 0.27083 * group.Width, group.GetMinY() + 0.54167 * group.Height));
                bezierPath.AddLineTo(new CGPoint(group.GetMinX() + 0.41667 * group.Width, group.GetMinY() + 0.68750 * group.Height));
                bezierPath.AddLineTo(new CGPoint(group.GetMinX() + 0.75000 * group.Width, group.GetMinY() + 0.35417 * group.Height));
                bezierPath.LineCapStyle = CGLineCap.Square;
                bezierPath.Stroke();

                var lines = new CAShapeLayer()
                {
                    Path = bezierPath.CGPath,
                    Bounds = group,
                    StrokeColor = Colors.AccentColor.CGColor,
                    FillColor = UIColor.Clear.CGColor,
                    LineWidth = 2f,
                    Position = new CGPoint(group.Width / 2.0, group.Height / 2.0),
                    AnchorPoint = new CGPoint(.5, .5)
                };

                var view1 = new UIView(group);
                view.Add(view1);
                view1.Layer.AddSublayer(lines);
                view1.SetInCenterVertical(headerView);

                if (Settings.IsLtrSelected)
                    view1.SetRight(view.Frame.Width - Dimensions.PaddingBig);
                else
                    view1.SetLeft(Dimensions.PaddingBig);
            }

            if (_fromProfile && !isAddSbpCardButton && (method.PaymentMethodType == PaymentMethodType.BankCardApp 
                                                        || method.PaymentMethodType == PaymentMethodType.Sbp))
            {
                var cross = new UILabel(new CGRect(0, 0, view.Frame.Height, view.Frame.Height))
                {
                    TextAlignment = Settings.IsLtrSelected ? UITextAlignment.Right : UITextAlignment.Left,
                    TextColor = Colors.HintTextColor,
                    Text = "l",
                    Font = UIFont.FromName("icon_font", 15f)
                };

                _crossView = new UIView(cross.Bounds);
                _crossView.AddSubview(cross);
                _crossView.SetMaterialSelection(async () =>
                {
                    await PaymentModule.Instance.Controller.OnClickedDeleteBankCard(method.GetCard());
                    UpdatePaymentMethodsList();
                }, UIColor.Clear);

                if (Settings.IsLtrSelected)
                    _crossView.SetRight(Width - Dimensions.PaddingBig);
                else
                    _crossView.SetLeft(Dimensions.PaddingBig);

                view.AddSubview(_crossView);
            }

            var headerViewWidth = (_crossView != null) ? _crossView.Frame.Width : 0;
            headerView.SetWidth(Width - iconView.Frame.Width - headerViewWidth);

            line.SetWidth(Width);

            SetLtrPaddingsIfNeeded(headerView, line, iconView);

            if (_fromProfile)
            {
                return view;
            }
            
            if (isAddSbpCardButton)
            {
                view.SetMaterialSelection(async () =>
                {
                    await DigitalOrderingModule.Instance.UpdateUserDataBeforePayment();
                    _ = PaymentModule.Instance.Controller.OnAddCard(isSbpCard: true);
                    LpAnalytics.TrackSelectPaymentMethod("ADD_NEW_SBP_CARD");
                    NavigationController.PopViewController(true);
                });
            }
            else
            {
                view.SetMaterialSelection(() =>
                {
                    PaymentModule.Instance.Controller.OnSelectPaymentMethod(method);
                    DigitalOrderingModule.Instance.CurrentOrder.ResetUniqueOrderQuoteGuid();
                    LpAnalytics.TrackSelectPaymentMethod(method.Name);
                    NavigationController.PopViewController(true);
                });
            }
            return view;
        }

        private void SetLtrPaddingsIfNeeded(UILabel headerView, UILine line, UIView iconView, UILabel warningMessageView = null, UILabel arrowLabel = null)
        {
            var headerWidth = Width - iconView.Frame.Width - Dimensions.Padding - 2 * Dimensions.PaddingBig;

            if (_crossView != null)
                headerWidth -= _crossView.Frame.Width;

            if (arrowLabel != null)
                headerWidth -= arrowLabel.Frame.Width + Dimensions.Padding;

            headerView.SetWidth(headerWidth);
            line.SetWidth(Width - 2 * Dimensions.PaddingBig);

            if (Settings.IsLtrSelected)
            {
                var headerPadding = (_crossView != null) ? _crossView.Frame.Width : 0;

                headerPadding += (arrowLabel?.Frame.Width + Dimensions.Padding) ?? 0;
                headerView.SetRight(Width - headerPadding - Dimensions.PaddingBig);

                iconView.SetLeft(Dimensions.PaddingBig);
                line.SetLeft(Dimensions.PaddingBig);
                warningMessageView?.SetLeft(headerView.Frame.Left);
                arrowLabel?.SetRight(Width - Dimensions.PaddingBig);
            }
            else
            {
                var headerPadding = Dimensions.PaddingBig + ((_crossView != null) ? _crossView.Frame.Width : 0);

                headerPadding += (arrowLabel?.Frame.Width + Dimensions.Padding) ?? 0;
                headerView.SetLeft(headerPadding);

                iconView.SetRight(Width - Dimensions.PaddingBig);
                line.SetLeft(Dimensions.PaddingBig);
                warningMessageView?.SetRight(headerView.Frame.Right);
                arrowLabel?.SetLeft(Dimensions.PaddingBig);
            }
        }
    }
}
