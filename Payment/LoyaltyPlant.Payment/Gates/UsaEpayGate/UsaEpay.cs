using System;
using System.Threading.Tasks;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Tasks;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Payment.Gates.UsaEpayGate
{
	public class UsaEpay : BaseGate
    {
        public string SourceKey { get; internal set; }
        public string Pin { get; internal set; }
        string _num, _month, _year, _cvv;

        public UsaEpay() : base(GateType.UsaEpay)
        {
        }

        internal override Task OnCardInput(string num, string month, string year, string cvv, BankCardType type)
        {
            _num = num;
            _month = month;
            _year = year;
            _cvv = cvv;
            return base.OnCardInput(num, month, year, cvv, type);
        }

        protected override void ResetPaymentState()
        {
            _num = null;
            _month = null;
            _year = null;
            _cvv = null;
        }

        internal override async Task OnAddCard(bool isSbpCard)
        {
            if (PaymentModule.Instance.Controller == null)
            {
                throw new Exception("Cannot make payment for " + Type + " because no controller");
            }
            
            InitPayHandler();
            
            try
            {
                PaymentModule.Instance.Controller.OpenNativeCardInput();
                await GatePayHandler.WaitAsync();

                if (GatePayHandler.IsCanceled)
                    return;

                PaymentModule.Instance.Controller.ShowLoadingIndicator(I18N.PLEASE_WAIT);
                var result = await new WebTask<UsaEpayTransactionsResponse>().ExecuteAsync(new UsaEpayTransactionsRequest(this, _num, _month, _year, _cvv));

                if (!result.Ok)
                {
                    PaymentModule.Instance.Controller.CreateOkMessage(I18N.PAYMENT_PROCESS_CARD_WAS_NOT_ADDED);
                    LpLogger.LOG_W("UsaEpay did not save card" + result.StatusCode + " " + result.ErrorException);
                    return;
                }

                var token = result.GetToken();

                if (string.IsNullOrWhiteSpace(token))
                {
                    PaymentModule.Instance.Controller.CreateOkMessage(I18N.PAYMENT_PROCESS_CARD_WAS_NOT_ADDED);
                    LpLogger.LOG_D("No token recieved for UsaEpayTransactions");
                    return;
                }

                var bankCard = BankCard.GetBankCardByNumber(_num);
                bankCard.Token = token;
                bankCard.GateType = GateType.UsaEpay;
                
                var isSuccessAdded = await PaymentModule.Instance.AddAndSelectCard(bankCard);
                if (isSuccessAdded)
                {
                    PaymentModule.Instance.Controller.CloseNativeCardInput();
                }
            }
            catch (Exception e)
            {
                LpLogger.LOG_E($"{GetType().Name} OnAddCard", e);
            }
            finally
            {
                PaymentModule.Instance.Controller.HideLoadingIndicator();
                StopPay();
            }
        }
    }
}
