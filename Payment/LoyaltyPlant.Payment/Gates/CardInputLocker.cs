using System;
using System.Threading;
using System.Threading.Tasks;

namespace LoyaltyPlant.Payment.Model
{
    public sealed class CardInputLocker : IDisposable
    {
        private SemaphoreSlim _lock;
        private GatePaymentState _eventResult;
        
        public bool IsCanceled => _eventResult == GatePaymentState.Cancelation;
        
        private enum GatePaymentState
        {
            Default,
            Success,
            Cancelation,
        }

        public void Success()
        {
            _eventResult = GatePaymentState.Success;
            Release();
        }

        public void Cancel()
        {
            _eventResult = GatePaymentState.Cancelation;
            Release();
        }
        
        public async Task WaitAsync()
        {
            if (_eventResult == GatePaymentState.Default)
            {
                _lock = new SemaphoreSlim(0, 1);
                await _lock.WaitAsync();
            }
        }
        
        private void Release()
        {
            if (_lock != null && _lock.CurrentCount < 1)
            {
                _lock.Release();
            }
        }

        public void Dispose()
        {
            _lock?.Dispose();
        }
    }
}