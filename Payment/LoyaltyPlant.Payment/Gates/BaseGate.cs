using System.Threading.Tasks;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Payment.Model;
using LoyaltyPlant.Texts;

namespace LoyaltyPlant.Payment.Gates
{
    public abstract class BaseGate
    {
        public GateType Type { get; }
        public decimal MinAmount { get; }
        public int Timeout { get; set; }

        public string Url { get; set; }
        public string RedirectUrl { get; set; }
        public string TransactionId { get; set; }

        private string countryCode;
        public string CountryCode
        {
            get
            {
                if (countryCode == null)
                    return "US";
                return countryCode;
            }
            set { countryCode = value; }
        }

        public string GatewayTitle
        {
            get
            {
                switch (Type)
                {
                    case GateType.Stripe:
                    case GateType.StripeConnect:
                        return "stripe";
                    case GateType.Best2Pay:
                        return "best2pay";
                    case GateType.FreedomPay:
                        return "freedompay";
                    case GateType.BrainTree:
                        return "braintree";
                    case GateType.Payfort:
                        return "payfort";
                    case GateType.CheckoutCom:
                        return "checkoutltd";
                    case GateType.Fondy:
                        return "fondyeu";
                    case GateType.Vostok:
                        return "bankvostok";
                    default:
                        return "unknown";
                }
            }
        }

        private string currencyIsoName;
        public string CurrencyIsoName
        {
            get
            {
                if (currencyIsoName == null)
                    return "USD";
                return currencyIsoName;
            }
            set { currencyIsoName = value; }
        }

        public virtual bool IsSupportAddCard => true;

        // applePayMerchantId пока не используется, но оно приходит в gate-settings,
        // поэтому пусть будет, пока не решим, точно ли оно надо/не надо
        // applePayMerchantId сейчас подставляется build-скриптом в сборку
        // и эти две штуки пока используются только для freedomPay
        public string ApplePayMerchantId { get; set; }
        public string GooglePayMerchantId { get; set; }

        // for waiting when you add card via browser or native input
        protected CardInputLocker GatePayHandler { get; private set; }
        public virtual bool IsRequiredSalesOutletId { get; }

        public enum HandleState
        {
            Default,
            Success,
            Cancelation,
        }

        protected void InitPayHandler()
        {
            GatePayHandler = new CardInputLocker();
            ResetPaymentState();
        }

        protected abstract void ResetPaymentState();

        protected BaseGate(GateType type)
        {
            Type = type;
        }

        internal abstract Task OnAddCard(bool isSbpCard);

        internal virtual Task OnRedirect(string url, string html)
        {
            LpLogger.LOG_I(Type + ": success redirect was captured");
            GatePayHandler.Success();
            return Task.CompletedTask;
        }

        internal virtual Task OnCardInput(string num, string month, string year, string cvv, BankCardType type)
        {
            LpLogger.LOG_I(Type + ": card data was input");
            GatePayHandler.Success();
            return Task.CompletedTask;
        }

        internal void OnCancel()
        {
            LpLogger.LOG_I(Type + ": webView will be closed without payment");
            GatePayHandler?.Cancel();
        }

        internal void StopPay()
        {
            LpLogger.LOG_I($"{Type}: flow is stopping; GatePayHandler == null: {GatePayHandler == null}");
            GatePayHandler?.Cancel();
        }

        internal virtual HandleState WillRedirectBeHandled(string url, string html)
        {
            return HandleState.Default;
        }

        internal virtual async Task<bool> Pay(IErrorPaymentCallback errorPaymentCallback = null)
        {
            var paymentObject = PaymentModule.Instance.PaymentIntent.PaymentObject;
            var prepaymentResult = await paymentObject.OnPrePaymentActions();
            var isAccepted = prepaymentResult.Item1;
            var isConnectionProblemsMessageWasShown = prepaymentResult.Item2;

            if (!isAccepted)
            {
                paymentObject.OnMakePaymentFailed(I18N.PAYMENT_PROCESS_FAILED_ANY_ERROR, isConnectionProblemsMessageWasShown);
                return false;
            }

            return await PaymentModule.Instance.MakePayment();
        }

        protected async Task SaveCard(string token, string mask)
        {
            var card = new BankCard(Type)
            {
                Token = token,
                IsSaveable = true,
                CardMask = mask,
            };
            await PaymentModule.Instance.AddAndSelectCard(card);
        }
    }
}
