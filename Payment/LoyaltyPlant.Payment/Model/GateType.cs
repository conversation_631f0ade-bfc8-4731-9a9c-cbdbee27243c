using System;
using System.Runtime.Serialization;
using LoyaltyPlant.Core.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace LoyaltyPlant.Payment.Model
{
    [NetExtentions.PreserveAttribute(AllMembers = true)]
    [JsonConverter(typeof(StringEnumConverter))]
    public enum GateType
    {
        [EnumMember(Value = "STRIPE")]
        Stripe,
        [EnumMember(Value = "STRIPE_CONNECT")]
        StripeConnect,
        [EnumMember(Value = "VANTIV")]
        Vantiv,
        [EnumMember(Value = "BEST_2_PAY")]
        Best2Pay,
        [EnumMember(Value = "NETWORK_AE")]
        NetworkAE,
        [EnumMember(Value = "N_GENIUS")]
        NGenius,
        [EnumMember(Value = "PAYEEZY")]
        Payeezy,
        [EnumMember(Value = "PAYFORT")]
        Payfort,
        [EnumMember(Value = "PAYZE")]
        Payze,
        [EnumMember(Value = "USAEPAY")]
        UsaEpay,
        [EnumMember(Value = "BRAINTREE")]
        BrainTree,
        [EnumMember(Value = "FREEDOM_PAY")]
        FreedomPay,
        [EnumMember(Value = "FREEDOM_PAY_CIS")]
        FreedomPayCis,
        [EnumMember(Value = "CLOUDSOFT")]
        CloudSoft,
        [EnumMember(Value = "CHECKOUT_COM")]
        CheckoutCom,
        [EnumMember(Value = "PESAPALGATESETTINGS_2_0")]
        Pesapal,
        [EnumMember(Value = "CIBPAY")]
        Cibpay,
        [EnumMember(Value = "VIVA_WALLET")]
        VivaWallet,
        [EnumMember(Value = "FONDY")]
        Fondy,
        [EnumMember(Value = "VOSTOK")]
        Vostok,
        [EnumMember(Value = "STUB")]
        LoyaltyPlant
    }
}
