using System;
namespace LoyaltyPlant.Payment.Model
{
    public enum PaymentPurposeType
    {
        GiftCertificate,
        DeliveryOrder,
        Donation,
        VatutinkiPublicUtilities,
        VatutinkiServiceOrder
    }

    public static class PaymentPurposeTypeExtention
    {
        public static string GetStringRepresentation(this PaymentPurposeType type)
        {
            switch (type)
            {
                case PaymentPurposeType.DeliveryOrder:
                    return "DELIVERY_ORDER";
                case PaymentPurposeType.Donation:
                    return "DONATION";
                case PaymentPurposeType.VatutinkiPublicUtilities:
                    return "PUBLIC_UTILITIES";
                case PaymentPurposeType.VatutinkiServiceOrder:
                    return "NV_SERVICE_ORDER";
                case PaymentPurposeType.GiftCertificate:
                    return "BUY_CERTIFICATE";
            }

            return "";
        }
    }
}
