using System;
using Xunit;
using LoyaltyPlant.Base.Tests;
using LoyaltyPlant.Core;
using LoyaltyPlant.Content;
using LoyaltyPlant.Maps.Model;

[assembly: CollectionBehavior(DisableTestParallelization = true)]

namespace LoyaltyPlant.Maps.Tests
{
    public class MapsTest
    {
        [Fact]
        public async void Serializable_Successful_SaveAndLoadModel()
        {
            Engine.Initialize(new MockWebController(), new TestCacheSerilizer(), new MockConfiguration(), new MockPlatformIos(), new MockLoggerWriter());
            Engine.Instance.AddModule(new MapsModule());
            Assert.True(await MapsModule.Instance.SaveAsync());
            Assert.True(await MapsModule.Instance.LoadAsync());
        }

        [Fact]
        public async void Maps_SyncResponse_ParsingCard()
        {
            var wc = new MockWebController("sync_response.xml");

            Engine.Initialize(wc, new MockCacheController(),
                new MockConfiguration(), new MockPlatformIos(),
                new MockLoggerWriter());

            Engine.Instance.AddModule(new ContentModule());
            Engine.Instance.AddModule(new MapsModule());

            var syncResponse = await Engine.Instance.SyncAsync();
            Assert.True(syncResponse.Ok);

            var card = ContentModule.Instance.GetCard(170842) as OutletsCard;
            Assert.Equal(card.PreviewText, "Мы на карте");
        }
    }
}
