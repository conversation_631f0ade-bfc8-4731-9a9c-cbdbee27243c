using LoyaltyPlant.App.iOS;
using UIKit;

namespace LoyaltyPlant.Maps.iOS
{
    public class MapsFrontend : AppFrontend, IMapsFrontend
    {
        public MapsFrontend(UIWindow window, UINavigationController navigationController) : base(window, navigationController)
        {
        }

        public void OpenMaps(double latitude, double longitude, string name = "")
        {
            var locationTo = string.Format("{0},{1}",
                latitude.ToString(System.Globalization.CultureInfo.InvariantCulture),
                longitude.ToString(System.Globalization.CultureInfo.InvariantCulture));

            UIApplication.SharedApplication
                .OpenUrlAsync(new Foundation.NSUrl("https://www.google.com/maps/dir/?api=1&destination=" + locationTo),
                    new UIApplicationOpenUrlOptions());
        }
    }
}