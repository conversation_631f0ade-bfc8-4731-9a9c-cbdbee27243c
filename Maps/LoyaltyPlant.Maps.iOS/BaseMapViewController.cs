using System;
using System.Collections.Generic;
using CoreGraphics;
using CoreLocation;
using LoyaltyPlant.App.iOS.Controls;
using LoyaltyPlant.App.iOS.Utils;
using LoyaltyPlant.App.iOS.ViewControllers;
using LoyaltyPlant.Core;
using LoyaltyPlant.Core.Logger;
using LoyaltyPlant.Core.Model;
using LoyaltyPlant.Core.Utils;
using LoyaltyPlant.Maps.iOS.Utils;
using MapKit;
using UIKit;

namespace LoyaltyPlant.Maps.iOS
{
    public abstract class BaseMapViewController : BaseViewController, IUIScrollViewDelegate
	{
		private readonly List<Outlet> _outlets;

		private CLLocationManager _locationManager;
        private MKMapView _mapView;

        private UIScrollView _scrollView;
        private UIView _descView;
        private UIView _bottomView;
        private long _lastMarkerTap;

        private readonly string _buttonText;

        public override UIColor NavigationBarColor => UIColor.Clear;
        public override UIColor NavigationBarActionButtonsColor => UIColor.Black;
        public override UIColor NavigationBarTitleColor => UIColor.Black;

        protected abstract nfloat SetupContent(Outlet outlet, nfloat y, UIView descView);

        public BaseMapViewController(List<Outlet> outlets, string buttonText)
		{
			_buttonText = buttonText;
			_outlets = outlets;
		}

		public override void ViewDidLoad()
		{
			base.ViewDidLoad();

            View.BackgroundColor = Colors.SecondaryBackgroundColor;

			_mapView = new MKMapView(View.Bounds);
			_mapView.MapType = MKMapType.Standard;
			_mapView.Delegate = new MapViewDelegate(_outlets, this);
			_mapView.ZoomEnabled = true;
			_mapView.UserInteractionEnabled = true;
			_mapView.ShowsUserLocation = true;
			View.AddSubview(_mapView);

			foreach (var outlet in _outlets)
			{
				_mapView.AddAnnotation(new MKPointAnnotation
				{
					Coordinate = new CLLocationCoordinate2D(outlet.Latitude, outlet.Longitude)
				});
			}

			_descView = new UIView(new CGRect(0, 0, View.Frame.Width, 0));
			_descView.BackgroundColor = Colors.SecondaryBackgroundColor;
			_descView.Layer.CornerRadius = Dimensions.ScreenRadius;
			_descView.ClipsToBounds = false;

			_scrollView = new UIScrollView();
			_scrollView.SetWidth(View.Frame.Width);
			_scrollView.AddSubview(_descView);
			_scrollView.ClipsToBounds = false;
			_scrollView.Delegate = this;
			_scrollView.ShowsVerticalScrollIndicator = false;
			View.AddSubview(_scrollView);
			
			_locationManager = new CLLocationManager();

			SetRightRectangle();

			_locationManager.AuthorizationChanged += (sender, e) =>
			{
				var status = e.Status;
				switch (status)
				{
					case CLAuthorizationStatus.NotDetermined:
					case CLAuthorizationStatus.Denied:
						break;
					case CLAuthorizationStatus.AuthorizedWhenInUse:
					case CLAuthorizationStatus.AuthorizedAlways:
						_locationManager.StartUpdatingLocation();
						break;
				}
			};

			_locationManager.RequestWhenInUseAuthorization();

			var tgr = new UITapGestureRecognizer((arg) =>
			{
				var time = LpDateTime.Now.DateTimeTotalMillis();
				if (time - _lastMarkerTap > 500)
				{
					_bottomView?.RemoveFromSuperview();
					_scrollView.SetHeight(0);
					_scrollView.SetTop(View.Frame.Height);

				}
			});
            _mapView.AddGestureRecognizer(tgr);
		}

        public override void ViewWillAppear(bool animated)
        {
            base.ViewWillAppear(animated);
            NavigationController.NavigationBarHidden = false;
            NavigationController.NavigationBar.Translucent = true;
        }

        public void OpenMarkerDetails(Outlet outlet)
        {
            _lastMarkerTap = LpDateTime.Now.DateTimeTotalMillis();

            foreach (var view in _descView.Subviews)
                view.RemoveFromSuperview();

            var dragger = new UIView(new CGRect(0, 4f, 36f, 4f));
            dragger.BackgroundColor = Colors.HintTextColor;
            dragger.Layer.CornerRadius = 2f;
            dragger.ClipsToBounds = true;
            dragger.SetLeft(_descView.Frame.Width / 2 - dragger.Frame.Width / 2);
            _descView.AddSubview(dragger);

            var header = new UILabel(new CGRect(Dimensions.Padding, dragger.Frame.Bottom + Dimensions.Padding,
                                                _descView.Frame.Width - 2 * Dimensions.Padding, 0));
            header.SetFont(UniversalUtils.FontStyle.Subheader, UniversalUtils.FontType.Bold);
            header.TextColor = Colors.DefaultTextColor;
            header.Lines = 0;
            header.Text = outlet.Name;
            header.SetLineHeight(24f);
            header.AccessibilityIdentifier = "outletHeader";
            header.SizeHeightToFit();
            if (!Settings.IsLtrSelected)
	            header.TextAlignment = UITextAlignment.Right;

            _descView.AddSubview(header);

            var y = header.Frame.Bottom;

            if (!string.IsNullOrWhiteSpace(outlet.Address))
            {
                var addressView = new UILabel(new CGRect(Dimensions.Padding, y + Dimensions.PaddingSmall,
                                                         _descView.Frame.Width - 2 * Dimensions.Padding, 0));
                addressView.Text = outlet.Address;
                addressView.AccessibilityIdentifier = "outletDescription";
                addressView.Lines = 0;
                addressView.SetFont(UniversalUtils.FontStyle.Default);
                addressView.TextColor = Colors.DefaultTextColor;
                addressView.SetLineHeight(21f);
                addressView.SizeHeightToFit();
                if (!Settings.IsLtrSelected)
	                addressView.TextAlignment = UITextAlignment.Right;

                _descView.AddSubview(addressView);
                y = addressView.Frame.Bottom + Dimensions.Padding;
            }

            _descView.SetHeight(y);

            y = SetupContent(outlet, y, _descView);

            _descView.SetHeight(y + Dimensions.PaddingSmall);

            var backing = new UIView(new CGRect(0, _descView.Frame.Height - Dimensions.ScreenRadius, _descView.Frame.Width,
                                                UIScreen.MainScreen.Bounds.Height));
            backing.BackgroundColor = Colors.SecondaryBackgroundColor;
            _descView.AddSubview(backing);

            _bottomView?.RemoveFromSuperview();

            _bottomView = new UIView(new CGRect(0, 0, View.Frame.Width, 0));
            _bottomView.BackgroundColor = Colors.SecondaryBackgroundColor;

            var button = new DigitalOrderingButton(_buttonText, Dimensions.WidthPadding);
            button.SetTop(Dimensions.Padding);
            button.AccessibilityIdentifier = "outletButton";
            button.SetLeft(Dimensions.Padding);
            button.SetMaterialSelection(() => ActionButtonClicked(outlet));
            _bottomView.AddSubview(button);
            _bottomView.SetHeight(button.Frame.Height + 2 * Dimensions.Padding);
            _bottomView.SetBottom(View.Frame.Height);

            var line1 = new UIView(new CGRect(Dimensions.Padding, 0, Dimensions.WidthPadding, 0.5f));
            line1.BackgroundColor = Colors.DividerTextColor;
            _bottomView.AddSubview(line1);
            View.AddSubview(_bottomView);

            const float height = 250f;
            var minHeight = _descView.Frame.Height;
            if (height < minHeight)
                minHeight = height;

            _scrollView.SetTop(View.Frame.Height - minHeight - _bottomView.Frame.Height + Dimensions.ScreenRadius);
            _scrollView.SetHeight(minHeight);
            _scrollView.ContentSize = (new CGSize(View.Frame.Width, _descView.Frame.Height + 1f));

            UIView.Animate(0.2, delegate ()
            {
                var mapRegion = new MKCoordinateRegion();
                mapRegion.Center = new CLLocationCoordinate2D(outlet.Latitude - 0.003, outlet.Longitude);
                mapRegion.Span.LatitudeDelta = 0.01;
                mapRegion.Span.LongitudeDelta = 0.01;
                _mapView.SetRegion(mapRegion, true);
            });

            var shadowPath = UIBezierPath.FromRoundedRect(new CGRect(3f, 0, _descView.Bounds.Width - 6f,
                                                                     _descView.Bounds.Height - Dimensions.CardRadius - 10f), 0);
            _descView.Layer.MasksToBounds = false;
            _descView.Layer.ShadowColor = UIColor.Black.CGColor;
            _descView.Layer.ShadowOffset = new CGSize(0f, -0.3f);
            _descView.Layer.ShadowOpacity = 0.15f;
            _descView.Layer.ShadowPath = shadowPath.CGPath;
        }

        private void ActionButtonClicked(Outlet outlet)
        {
            MapsModule.Instance.Controller.OpenMaps(outlet);
        }

        private void SetRightRectangle()
		{
			var myLocation = _locationManager?.Location;

			if (_outlets.Count == 1)
			{
                var currentOutlet = _outlets[0];

                MKCoordinateRegion mapRegion;
				mapRegion.Center = new CLLocationCoordinate2D(currentOutlet.Latitude, currentOutlet.Longitude);
				mapRegion.Span.LatitudeDelta = 0.1;
				mapRegion.Span.LongitudeDelta = 0.1;
                if (mapRegion.Center.IsValid())
                {
                    _mapView.SetRegion(mapRegion, true);
                }
                else
                {
                    LpLogger.LOG_E("Something is wrong with mapView.SetRegion() with " + currentOutlet.Name + ". lat, lng: " + currentOutlet.Latitude + ", " + currentOutlet.Longitude);
                }
            }
			else
			{
				var outlets2d = new List<CLLocationCoordinate2D>();
				double minLatitude = Double.MaxValue, maxLatitude = Double.MinValue, minLongitude = Double.MaxValue,
					maxLongitude = Double.MinValue;

				if (myLocation == null)
				{
					foreach (var outlet in _outlets)
						outlets2d.Add(new CLLocationCoordinate2D(outlet.Latitude, outlet.Longitude));

				}
				else
				{
					var isOutletAdded = false;
					foreach (var outlet in _outlets)
					{
						if (Math.Abs(outlet.Latitude - myLocation.Coordinate.Latitude) < 1f && Math.Abs(outlet.Longitude - myLocation.Coordinate.Longitude) < 1f)
						{
							outlets2d.Add(new CLLocationCoordinate2D(outlet.Latitude, outlet.Longitude));
							isOutletAdded = true;
						}
					}

					//if nothing to add then show only outlets and remove location
					if (!isOutletAdded)
					{
						foreach (var outlet in _outlets)
							outlets2d.Add(new CLLocationCoordinate2D(outlet.Latitude, outlet.Longitude));
					}
					else
					{
						outlets2d.Add(myLocation.Coordinate);
					}
				}

				foreach (var ann in _outlets)
				{
					var lat = ann.Latitude;
					var lon = ann.Longitude;
					if (lat > maxLatitude) maxLatitude = lat;
					if (lat < minLatitude) minLatitude = lat;
					if (lon > maxLongitude) maxLongitude = lon;
					if (lon < minLongitude) minLongitude = lon;
				}

				MKCoordinateRegion region;
				region.Center.Latitude = (minLatitude + maxLatitude) / 2;
				region.Center.Longitude = (minLongitude + maxLongitude) / 2;

				region.Span.LatitudeDelta = (maxLatitude - minLatitude) * 1.1f;

				region.Span.LatitudeDelta = (region.Span.LatitudeDelta < 0.01f)
					? 0.01f
					: region.Span.LatitudeDelta;

				region.Span.LongitudeDelta = (maxLongitude - minLongitude) * 1.1f;

				var scaledRegion = _mapView.RegionThatFits(region);
                if (scaledRegion.Center.IsValid())
                {
                    _mapView.SetRegion(scaledRegion, true);
                }
                else
                {
                    LpLogger.LOG_E("Something is wrong with mapView.SetRegion() with scaledRegion. lat, lng: " + scaledRegion.Center.Latitude + ", " + scaledRegion.Center.Longitude);
                }
            }
		}
	}
}
